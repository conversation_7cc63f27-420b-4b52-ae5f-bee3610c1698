from pymongo import MongoClient
from datetime import datetime
import pandas as pd
from bson import ObjectId
import httpx
from datetime import timedelta
import json
import time
from typing import Optional

class StatisticDossierAdditionExport:
    def __init__(self, mongo_uri: str, database_name: str, messenger_base_url: str, auth_token: str, max_retries: int = 3, retry_delay: int = 1):
        self.client = MongoClient(mongo_uri)
        self.db = self.client[database_name]
        self.messenger_base_url = messenger_base_url
        self.auth_token = auth_token
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
    def _get_auth_headers(self):
        """Return headers with authentication token"""
        return {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
        
    def _make_api_call(self, url: str) -> Optional[dict]:
        """
        Make API call with retry logic and custom SSL configuration
        """
        # Create a custom SSL context
        ssl_context = httpx.create_ssl_context()
        # Set stronger DH key length
        ssl_context.set_ciphers("HIGH:!DH:!aNULL")
        
        retries = 0
        last_exception = None
        
        while retries < self.max_retries:
            try:
                with httpx.Client(verify=ssl_context) as client:
                    response = client.get(
                        url,
                        headers=self._get_auth_headers(),
                        timeout=30.0  # 30 second timeout
                    )
                    response.raise_for_status()
                    return response.json()
                    
            except httpx.TimeoutException as e:
                print(f"Timeout error on attempt {retries + 1}: {str(e)}")
                last_exception = e
            except httpx.HTTPStatusError as e:
                print(f"HTTP error {e.response.status_code} on attempt {retries + 1}: {str(e)}")
                if e.response.status_code in [401, 403]:  # Auth errors
                    raise  # Don't retry auth errors
                last_exception = e
            except httpx.RequestError as e:
                print(f"Request error on attempt {retries + 1}: {str(e)}")
                last_exception = e
            except Exception as e:
                print(f"Unexpected error on attempt {retries + 1}: {str(e)}")
                last_exception = e
                
            retries += 1
            if retries < self.max_retries:
                time.sleep(self.retry_delay * (2 ** (retries - 1)))  # Exponential backoff
                
        print(f"Failed after {self.max_retries} attempts")
        if last_exception:
            raise last_exception
        return None
        
    def get_statistic_dossier_addition(self, input_dto, page_size, page_number, type_export="export"):
        # Build criteria list similar to Java version
        criteria_list = []
        
        # Handle dates
        if input_dto.get('appliedDateTo'):
            to_date = datetime.strptime(input_dto['appliedDateTo'], '%Y-%m-%dT%H:%M:%S.%fZ')
            criteria_list.append({'appliedDate': {'$lte': to_date}})
            
        if input_dto.get('appliedDateFrom'):
            from_date = datetime.strptime(input_dto['appliedDateFrom'], '%Y-%m-%dT%H:%M:%S.%fZ')
            criteria_list.append({'appliedDate': {'$gte': from_date}})
            
        # Add status criteria
        criteria_list.append({'dossierStatus.id': {'$ne': 0}})
        criteria_list.append({'dossierStatus.id': {'$ne': 6}})
        
        # Add procedure criteria
        if input_dto.get('procedureId'):
            criteria_list.append({'procedure.id': ObjectId(input_dto['procedureId'])})
            
        if input_dto.get('sectorId'):
            criteria_list.append({'procedure.sector.id': ObjectId(input_dto['sectorId'])})
            
        # Handle agency criteria
        if input_dto.get('agencyId'):
            criteria_list.append({'agency.id': ObjectId(input_dto['agencyId'])})
        elif input_dto.get('parentAgencyId'):
            agency_t = ObjectId(input_dto['parentAgencyId'])
            criteria_list.append({
                '$or': [
                    {'agency.id': agency_t},
                    {'agency.ancestors.id': agency_t},
                    {'agency.parent.id': agency_t}
                ]
            })
            
        if input_dto.get('dossierCode'):
            criteria_list.append({'code': input_dto['dossierCode']})
            
        # Combine all criteria
        query = {'$and': criteria_list} if criteria_list else {}
        
        if type_export == "export":
            result_list = []
            
            # Get dossiers from MongoDB
            dossiers = self.db.dossier.find(query)
            
            for dossier in dossiers:
                try:
                    output_dto = {}
                    output_dto['code'] = dossier.get('code')
                    output_dto['_id'] = str(dossier.get('_id'))
                    output_dto['dossierStatus'] = dossier.get('dossierStatus')
                    output_dto['task'] = dossier.get('task')
                    
                    # Handle applicant data
                    applicant = dossier.get('applicant', {})
                    data = applicant.get('data', {})
                    if isinstance(data, dict):
                        output_dto['phoneNumber'] = data.get('phoneNumber', '')
                        output_dto['fullname'] = data.get('fullname', '')
                        output_dto['companyName'] = data.get('organization', '')
                        
                        # Build company address
                        company_address_parts = []
                        if data.get('address1'):
                            company_address_parts.append(data['address1'])
                        if isinstance(data.get('village1'), dict) and data['village1'].get('label'):
                            company_address_parts.append(data['village1']['label'])
                        if isinstance(data.get('district1'), dict) and data['district1'].get('label'):
                            company_address_parts.append(data['district1']['label'])
                        if isinstance(data.get('province1'), dict) and data['province1'].get('label'):
                            company_address_parts.append(data['province1']['label'])
                        output_dto['companyAddress'] = ' '.join(company_address_parts)
                        
                        # Build applicant address
                        applicant_address_parts = []
                        if data.get('address'):
                            applicant_address_parts.append(data['address'])
                        if isinstance(data.get('village'), dict) and data['village'].get('label'):
                            applicant_address_parts.append(data['village']['label'])
                        if isinstance(data.get('district'), dict) and data['district'].get('label'):
                            applicant_address_parts.append(data['district']['label'])
                        if isinstance(data.get('province'), dict) and data['province'].get('label'):
                            applicant_address_parts.append(data['province']['label'])
                        output_dto['applicantAddress'] = ' '.join(applicant_address_parts)
                    
                    # Add other dossier fields
                    output_dto['procedure'] = dossier.get('procedure')
                    output_dto['acceptedDate'] = dossier.get('acceptedDate')
                    output_dto['appliedDate'] = dossier.get('appliedDate')
                    output_dto['appointmentDate'] = dossier.get('appointmentDate')
                    output_dto['completedDate'] = dossier.get('completedDate')
                    output_dto['attachment'] = dossier.get('attachment')
                    output_dto['dossierReceivingKind'] = dossier.get('dossierReceivingKind')
                    
                    # Get agency name
                    agency = dossier.get('agency', {})
                    agency_names = agency.get('name', [])
                    output_dto['agencyName'] = agency_names[0].get('name', '') if agency_names else ''
                    
                    # Get comments and additions from messenger service
                    try:
                        dossier_id = str(dossier['_id'])
                        print(f"Fetching comments for dossier {dossier_id}")
                        comments_url = f"{self.messenger_base_url}/comment/?group-id=2&sort=createdDate,desc&item-id={dossier_id}"
                        
                        comments_data = self._make_api_call(comments_url)
                        
                        if comments_data:
                            num_additions = 0
                            comment_list = []
                            
                            if comments_data.get('numberOfElements', 0) > 0:
                                for comment in comments_data['content']:
                                    try:
                                        created_date = datetime.strptime(
                                            comment['createdDate'], 
                                            '%Y-%m-%dT%H:%M:%S.%f+0000'
                                        ) + timedelta(hours=7)
                                        
                                        if ('Yêu cầu bổ sung' in comment['content'] and 
                                            output_dto['acceptedDate'] < created_date):
                                            num_additions += 1
                                            
                                            comment_dto = {
                                                'content': comment['content'].split('<p>')[1].split('</p>')[0],
                                                'officerName': comment.get('user', {}).get('fullname', ''),
                                                'files': []
                                            }
                                            
                                            if comment.get('file'):
                                                for file_data in comment['file']:
                                                    file_info = {
                                                        'id': str(ObjectId(file_data['id'])),
                                                        'filename': file_data['filename'],
                                                        'size': int(file_data['size'])
                                                    }
                                                    comment_dto['files'].append(file_info)
                                            
                                            comment_list.append(comment_dto)
                                    except Exception as e:
                                        print(f"Error processing comment for dossier {dossier_id}: {str(e)}")
                                        continue
                            
                            output_dto['numberOfAddition'] = num_additions
                            
                            # Create separate rows for each comment
                            if comment_list:
                                for comment in comment_list:
                                    comment_row = output_dto.copy()
                                    comment_row['contentCmt'] = comment['content']
                                    comment_row['officerName'] = comment['officerName']
                                    comment_row['file'] = comment['files']
                                    result_list.append(comment_row)
                            else:
                                # Add the dossier even if it has no comments
                                output_dto['contentCmt'] = ''
                                output_dto['officerName'] = ''
                                output_dto['file'] = []
                                result_list.append(output_dto)
                                
                    except Exception as e:
                        print(f"Error fetching comments for dossier {dossier_id}: {str(e)}")
                        # Add the dossier even if comments fetch failed
                        output_dto['contentCmt'] = ''
                        output_dto['officerName'] = ''
                        output_dto['file'] = []
                        output_dto['numberOfAddition'] = 0
                        result_list.append(output_dto)
                        
                except Exception as e:
                    print(f"Error processing dossier {str(dossier.get('_id'))}: {str(e)}")
                    continue
            
            try:
                # Convert to pandas DataFrame for Excel export
                df = pd.DataFrame(result_list)
                
                # Configure Excel writer
                writer = pd.ExcelWriter('dossier_statistics.xlsx', engine='xlsxwriter')
                df.to_excel(writer, sheet_name='Statistics', index=False)
                
                # Auto-adjust column widths
                worksheet = writer.sheets['Statistics']
                for i, col in enumerate(df.columns):
                    max_length = max(df[col].astype(str).apply(len).max(), len(col))
                    worksheet.set_column(i, i, max_length + 2)
                
                writer._save()
                print(f"Successfully exported {len(result_list)} records to Excel")
                return result_list
            except Exception as e:
                print(f"Error creating Excel file: {str(e)}")
                raise
            
        return None

def main():
    # Configuration settings
    mongo_uri = "********************************************************************************************************************************"
    database_name = "svcPadman"      # Update with your database name
    messenger_base_url = "https://ketnoi.dichvucongcamau.gov.vn/me"  # Update with your messenger service URL
    auth_token = "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"  # Update with your authentication token
    
    try:
        # Create instance of StatisticDossierAdditionExport
        exporter = StatisticDossierAdditionExport(
            mongo_uri=mongo_uri,
            database_name=database_name,
            messenger_base_url=messenger_base_url,
            auth_token=auth_token,
            max_retries=3,
            retry_delay=1
        )
        
        # Example input parameters
        input_dto = {
            "appliedDateFrom": "2024-01-01T00:00:00.000Z",
            "appliedDateTo": "2024-12-31T23:59:59.999Z",
            "agencyId": "",  # Example ObjectId - replace with actual ID
            "procedureId": "",  # Optional
            "sectorId": "",     # Optional
            "dossierCode": "",  # Optional
            "parentAgencyId": "6494f6c7b6e17a7c177b0fa5"  # Used if agencyId is not provided
        }
        
        # Pagination parameters (though not strictly needed for export)
        page_size = 100
        page_number = 0
        
        print("Starting export process...")
        
        # Call the export function
        results = exporter.get_statistic_dossier_addition(
            input_dto=input_dto,
            page_size=page_size,
            page_number=page_number,
            type_export="export"
        )
        
        print(f"Export completed successfully!")
        print(f"Total records exported: {len(results) if results else 0}")
        print("Excel file has been generated as 'dossier_statistics.xlsx'")
        
    except Exception as e:
        print(f"An error occurred during export: {str(e)}")
        raise
    finally:
        # Clean up MongoDB connection
        if 'exporter' in locals():
            exporter.client.close()

if __name__ == "__main__":
    main()

