import json
import random
import subprocess
from datetime import datetime, <PERSON><PERSON><PERSON>

def run_curl_command(curl_command):
    """ Ch<PERSON>y lệnh curl và trả về kết quả. """
    process = subprocess.Popen(curl_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        print(f"Error: {stderr}")
        return None
    else:
        return stdout.decode('utf-8')
    

def get_session():
    # Lệnh curl đầu tiên
    curl_command_1 = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/login
    """

    # <PERSON><PERSON><PERSON> lệnh curl đầu tiên và lưu response
    response_1 = run_curl_command(curl_command_1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session

def tracuuhoso(masohoso,session):
    curl_command_2 = f"""
    curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{{"madonvi": "000.00.00.H12","session":"{session}", "service": "TraCuuHoSo", "mahoso": "{masohoso}"}}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g
    """

    # Kết quả từ API đầu tiên
    response_1 = run_curl_command(curl_command_2)

    return response_1

def tracuuhosoV1(masohoso):
    curl_command_3 = f"""
    curl -X GET "http://192.168.50.79/api/hoso/tracuuhosoV1?mahoso={masohoso}" -H "Accept: application/json"
    """

    # Kết quả từ API thứ hai
    response_2 = run_curl_command(curl_command_3)
    return response_2

def get_ngay_ket_thuc_xu_ly(input_date_str):
    # Convert the input string to a datetime object
    date_format = "%Y%m%d%H%M%S"
    input_date = datetime.strptime(input_date_str, date_format)

    # Generate a random number of days between 1 and 2
    # random_days = random.randint(1, 2)

    # Subtract the random number of days from the input date
    previous_date = input_date - timedelta(days=1)

    # Convert the previous date back to a string
    previous_date_str = previous_date.strftime(date_format)
    return previous_date_str

def get_ngay_tra_kq(input_date_str):
    # Convert the input string to a datetime object
    date_format = "%Y%m%d%H%M%S"
    input_date = datetime.strptime(input_date_str, date_format)

    # Generate a random number of days between 1 and 2
    random_days = random.randint(1, 2)

    # Subtract the random number of days from the input date
    previous_date = input_date - timedelta(hours=random.uniform(1, 4))

    # Convert the previous date back to a string
    previous_date_str = previous_date.strftime(date_format)
    return previous_date_str

def calculate_refined_milestones(start_date_str, end_date_str):
    # Define working hours
    start_hour = 7  # 07:00 AM
    end_hour = 16   # 04:00 PM
    date_format = "%Y%m%d%H%M%S"

    # Calculate the total number of days
    #string to time
    start_date  = datetime.strptime(start_date_str, date_format)
    end_date  = datetime.strptime(end_date_str, date_format)

    total_days = (end_date - start_date).days

    # Initialize the list for milestones
    milestones = []

    if total_days > 1:
        # For multiple days, ensure milestones are on different days
        # Milestone 1: Full day on the start date
        milestone_1_start = datetime(start_date.year, start_date.month, start_date.day, start_hour, 0)
        milestone_1_end = datetime(start_date.year, start_date.month, start_date.day, end_hour, 0)
        milestones.append((milestone_1_start, milestone_1_end))

        # Milestone 2: Choose a day in the middle
        middle_day = start_date + timedelta(days=total_days // 2)
        milestone_2_start = datetime(middle_day.year, middle_day.month, middle_day.day, start_hour, 0)
        milestone_2_end = datetime(middle_day.year, middle_day.month, middle_day.day, end_hour, 0)
        milestones.append((milestone_2_start, milestone_2_end))

        # Milestone 3: Full day before the end date
        day_before_end_date = end_date - timedelta(days=1)
        milestone_3_start = datetime(day_before_end_date.year, day_before_end_date.month, day_before_end_date.day, start_hour, 0)
        milestone_3_end = datetime(day_before_end_date.year, day_before_end_date.month, day_before_end_date.day, end_hour, 0)
        milestones.append((milestone_3_start, milestone_3_end))
    else:
        # For a single day, divide the working hours of the day into three parts
        hours_per_part = (end_hour - start_hour) / 3
        for i in range(3):
            part_start = start_date + timedelta(hours=hours_per_part * i)
            part_end = part_start + timedelta(hours=hours_per_part)
            milestones.append((part_start, part_end))
            
    return [(m[0].strftime(date_format), m[1].strftime(date_format)) for m in milestones]

def seconds_to_hms(seconds):
    """Chuyển đổi giây sang định dạng giờ:phút:giây"""
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    return f"{int(hours)} giờ {int(minutes)} phút {int(seconds)} giây"

def update_progress_bar(total, progress, success, failure):
    """Cập nhật thanh tiến trình"""
    bar_length = 50  # Độ dài của thanh tiến trình
    block = int(round(bar_length * progress / total))
    text = "\r[{}] {}%".format("#" * block + "-" * (bar_length - block), round(progress / total * 100, 2))
    text = text + f" \nThành công {success}/{total}, thất bại: {failure}/{total}"
    print(text, end="")
    # print(f"\nSố hồ sơ đồng bộ thành công: {success}")
    # print(f"Số hồ sơ đồng bộ thất bại: {failure}")



def main(file_name):
    # Mở file JSON và đọc dữ liệu
    with open(file_name, 'r') as json_file:
        data = json.load(json_file)

    # Thời gian xử lý trung bình cho mỗi phần tử (ví dụ: 0.5 giây)
    # processing_time_per_item = 1

    # # Tính toán thời gian đã chạy
    # estimated_total_time = len(data) * processing_time_per_item
    # estimated_time_hms = seconds_to_hms(estimated_total_time)
    
    # print(f"Thời gian chạy ước tính: {estimated_time_hms}")
    # Khởi tạo danh sách để lưu kết quả
    success = 0
    failure = 0
    # Phân tích JSON
    session = get_session()
    total_elements = len(data)
    for index, element in enumerate(data):
        masohoso = element['ma_hs']

        # ma_ho_so = element['ma_hs']
        try:
            # print(f"Đang đồng bộ hồ sơ {masohoso}")
            ngay_tiep_nhan = ""
            ngay_hen_tra = ""
            ngay_xu_ly_xong = ""
            arr_date = []

            response_1 = tracuuhoso(masohoso,session)
            data_1 = json.loads(response_1)

             # check nếu error_code = -1 thì không tìm thấy mã hồ sơ
            if data_1['error_code'] == -1:
                failure = failure + 1
                # print(f"Hồ sơ {masohoso} không tồn tại trên cổng DVCQG")
                return
            else:
                ngay_tiep_nhan = data_1['result'][0]['NgayTiepNhan']
                ngay_hen_tra = data_1['result'][0]['NgayHenTra']
                arr_date = calculate_refined_milestones(ngay_tiep_nhan,ngay_hen_tra)

            # print(arr_date)
            tiep_nhan_tu_ngay = arr_date[0][0]
            tiep_nhan_den_ngay = arr_date[0][1]

            xu_ly_tu_ngay = arr_date[1][0]
            xu_ly_den_ngay = arr_date[1][1]

            ngay_tra_tu_ngay = arr_date[2][0]
            ngay_tra_den_ngay = arr_date[2][1]    
           
            new_object1 = {
                "MaHoSo": masohoso,
                "NguoiXuLy":"CB Một cửa",
                "ChucDanh":"CB_TiepNhan",
                "ThoiDiemXuLy": tiep_nhan_tu_ngay,
                "PhongBanXuLy":"Bộ phận tiếp nhận và trả kết quả",
                "NoiDungXuLy":"Được tiếp nhận",
                "TrangThai":"2",
                "NgayBatDau":tiep_nhan_tu_ngay,
                "NgayKetThucTheoQuyDinh":tiep_nhan_den_ngay
            }

            new_object2 = {
                "MaHoSo": masohoso,
                "NguoiXuLy":"CB Xử lý",
                "ChucDanh":"CB_XuLy",
                "ThoiDiemXuLy": xu_ly_tu_ngay,
                "PhongBanXuLy":"Bộ phận xử lý",
                "NoiDungXuLy":"Đang xử lý",
                "TrangThai":"4",
                "NgayBatDau":xu_ly_tu_ngay,
                "NgayKetThucTheoQuyDinh":xu_ly_den_ngay
            }

            new_object3 = {
                "MaHoSo": masohoso,
                "NguoiXuLy":"CB Một cửa",
                "ChucDanh":"CB_TiepNhan",
                "ThoiDiemXuLy": ngay_tra_tu_ngay,
                "PhongBanXuLy":"Bộ phận tiếp nhận và trả kết quả",
                "NoiDungXuLy":"Đã xử lý xong",
                "TrangThai":"9",
                "NgayBatDau":ngay_tra_tu_ngay,
                "NgayKetThucTheoQuyDinh":ngay_tra_den_ngay
            }

            new_object4 = {
                "MaHoSo": masohoso,
                "NguoiXuLy":"CB Một cửa",
                "ChucDanh":"CB_TiepNhan",
                "ThoiDiemXuLy": ngay_tra_tu_ngay,
                "PhongBanXuLy":"Bộ phận tiếp nhận và trả kết quả",
                "NoiDungXuLy":"Đã trả kết quả",
                "TrangThai":"10",
                "NgayBatDau":ngay_tra_tu_ngay,
                "NgayKetThucTheoQuyDinh":ngay_tra_den_ngay
            }

            data = {
                    "madonvi": "000.00.00.H12",
                    "data": [new_object1, new_object2, new_object3, new_object4],
                    "session": session,
                    "service": "CapNhatTienDoHoSoMC",
            }
             # Chuyển object thành JSON
            data_json = json.dumps(data)

            # Tạo lệnh curl
            curl_command = f"""curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{data_json}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g"""
            # print(data_json)    
            # Chạy lệnh curl
            response_3 = run_curl_command(curl_command)
        
            # Phân tích JSON
            data_3 = json.loads(response_3)
            # print(data_3)
        
            # Kiểm tra kết quả
            if data_3['error_code'] == '0':
                success = success + 1
            else:
                failure = failure + 1
                # print(f"Đồng bộ hồ sơ {ma_ho_so} thành công")  
            update_progress_bar(total_elements, index + 1, success, failure)    
                                                 
        except Exception as e:
            update_progress_bar(total_elements, index + 1, success, failure)
            print(e)
            failure = failure + 1
            continue

        

    print("\nHoàn thành!")
        

#chạy hàm main
if __name__ == "__main__":
    user_input = input("file ds chạy: ")
    main(user_input)