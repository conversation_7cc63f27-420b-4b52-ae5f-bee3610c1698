import aiohttp
import asyncio
import json
import pymongo
import ssl
import httpx
from openpyxl import Workbook
import pandas as pd

# Connect to MongoDB
mongo_uri = '********************************************************************************************************************************'
client = pymongo.MongoClient(mongo_uri)

async def getToken():
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = {
        'grant_type': 'password',
        'username': 'admin',
        'password': 'iGate#Cmu@2024',
        'client_id': 'web-onegate'
    }
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    
    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.post(url, headers=headers, data=payload)
        response.raise_for_status()
        access_token = response.json().get('access_token')
        return access_token
    
# async def syncQG(code):
#     url = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"
#     token = await getToken()
#     headers = {
#         'Content-Type': 'application/json',
#         'Authorization': 'Bearer '+token,
#         'Cookie': 'JSESSIONID=979D1F437D51C126FF15E5BA8BE0E768'
#     }
    
#     # ["000.00.07.H12-231005-0204"]
#     payload = json.dumps([code])
#     # print(payload)
    
#     response = requests.request("POST", url, headers=headers, data=payload)
#     print(response.text.encode('utf8'))
#     if response.status_code == 200:
#         if response.json()['affectedRows'] == 1:
#             return 200
#         else:
#             return 500
#     else:
#         return 501

# Function to download a file asynchronously using httpx
async def download_file(url, token, code):
    headers = {'Authorization': f'Bearer {token}'}
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    
    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.get(url, headers=headers)
        if response.status_code == 200:
            file_name = code + '-KQ.pdf'
            file_path = 'download/' + file_name

            with open(file_path, 'wb') as file:
                async for chunk in response.aiter_bytes(1024):  # Stream the response in chunks
                    if chunk:
                        file.write(chunk)
            print(f"Download completed: {file_path}")
            return file_path
        else:
            print(f"Failed to download the file: {response.status_code}")
            return None


# Function to upload a file with Bearer token using httpx
async def upload_file(upload_url, file_path, token, file_name):
    headers = {'Authorization': f'Bearer {token}'}
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        with open(file_path, 'rb') as file:
            data = {
                'files': (file_name, file, 'application/pdf'),
                'account': '652e4e600ec8eb131c3c9ff6'  # Update account field as needed
            }
            response = await client.post(upload_url, files=data, headers=headers)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Failed to upload the file: {response.status_code}")
                return None

# Function to make a PUT request with Bearer token using httpx
async def update_attachment(put_url, attachment_data, token):
    payload = {
        "attachment": [attachment_data]
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.put(put_url, json=payload, headers=headers)
        if response.status_code == 200:
            print("Successfully updated the attachment")
        else:
            print(f"Failed to update the attachment: {response.status_code}")

# Function query the database and return a list of documents
def get_documents():
    database_name = 'svcPadman'  # Replace with your database name
    collection_name = 'dossier'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    # Query the database
    # db.getCollection("dossier").find({ $and: [ { "agency.code": "000.00.07.H12" }, { "attachment": null }, { "dossierStatus.id": { $in: [ 2, 3, 4, 5, 16, 17, 9 ] } } ] }).limit(1000).skip(0)
    # Sở TP
    # documents = collection.find({"$and": [{"agency.code": "000.00.02.H12"}, {"attachment": None}, {"dossierStatus.id": {"$in": [2, 3, 4, 5, 16, 17, 9]}}]})
    # Sở GT
    # documents = collection.find({"$and": [{"attachment": []}, {"dossierStatus.id": {"$in": [4]}}]},
    # {"_id": 1, "code": 1}, no_cursor_timeout=True)
    # documents = collection.find({"$and": [{"agency.code": "000.00.07.H12"}, {"attachment": []}, {"dossierStatus.id": {"$in": [4]}}]})
    # Sở Công Thương
    # documents = collection.find({"$and": [{"agency.code": "000.00.05.H12"}, {"attachment": None}, {"dossierStatus.id": {"$in": [4]}}]})
    # Huyện U Minh
    # documents = collection.find({"$and": [{"agency.code": "000.20.27.H12"}, {"attachment": None}, {"dossierStatus.id": {"$in": [2, 3, 4, 5, 16, 17, 9]}}]})
    # documents = collection.find({"$and": [{"agency.code": "000.00.07.H12"}, {"attachment": None}]}).limit(1)
    #loop in dossier code in excel after find dossier id in mongo and save to list documents
    import pandas as pd
    data = pd.read_excel('ds_thang_12_hcc.xlsx')
    # codes = []
    documents = []
    #get code from column Code
    codes = data['code']
    for code in codes:
        document = collection.find_one({"code": code})
        documents.append(document)
    return documents

def get_file_id(dossier_id):
    database_name = 'svcPadman'  # Replace with your database name
    collection_name = 'dossierFormFile'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    # Query the database
    # db.getCollection("dossierFormFile").find({ "dossier.id": ObjectId("6564106e389d3f4f3a1782ad") }).limit(1000).skip(0)
    documents = collection.find({"dossier.id": dossier_id}).limit(1)
    file_id = str(documents[0]['file'][0]['id'])
    return file_id


# Main async function
async def main():
    # download_url = 'https://ketnoi.dichvucongcamau.gov.vn/fi/file/655c1ed6489d0d1efbb91871'
    # bearer_token = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    upload_url = 'https://ketnoi.dichvucongcamau.gov.vn/fi/file/--multiple?uuid=1'
    # put_url = 'https://ketnoi.dichvucongcamau.gov.vn/pa/dossier/655c1ee2b79286424d685a99/--online'
    bearer_token = await getToken()

    # Get the documents from MongoDB
    documents = get_documents()
    success = 0
    fail = 0
    list_code_fail = []
    #save to file excel only code
    # import pandas as pd
    
    # data = []
    # for document in documents:
    #     # contain 'H' in code
    #     if 'H' in document['code']:
    #         data.append(document['code'])
    # df = pd.DataFrame(data, columns=['Code'])
    # df.to_excel('code.xlsx', index=False)
    
    for document in documents:
        try:
            code = document['code']
            print("Code: "+code)
            file_name = code+'-KQ.pdf'
            dossier_id = document['_id']
            file_id = get_file_id(dossier_id)
            print("File id: "+file_id)
            download_url = 'https://ketnoi.dichvucongcamau.gov.vn/fi/file/'+file_id
            put_url = 'https://ketnoi.dichvucongcamau.gov.vn/pa/dossier/'+str(dossier_id)+'/--online'

            downloaded_file_path = await download_file(download_url, bearer_token, code)
            if downloaded_file_path:
                print("File downloaded successfully. File path:", downloaded_file_path)
                # Upload the file
                response_data = await upload_file(upload_url, downloaded_file_path, bearer_token, file_name)
                print(response_data)
                if response_data:
                    print("Upload Successful, Response:", response_data)

                    # Prepare data for PUT request
                    attachment_data = {
                        "id": response_data[0]["id"],
                        "filename": response_data[0]["filename"],
                        "size": response_data[0]["size"],
                        "group": "5f9bd9692994dc687e68b5a6"  # Update this if needed
                    }

                    # Make the PUT request
                    await update_attachment(put_url, attachment_data, bearer_token)

                    # Sync QG
                    # await syncQG(code)

                print ("Done: "+code)
                success += 1
        except Exception as e:
            print(e)
            list_code_fail.append(code)
            fail += 1

    print("Success: "+str(success))
    print("Fail: "+str(fail))
    print(list_code_fail)

async def sync():
    documents = get_documents()
    success = 0
    fail = 0
    list_code_fail = []
    for document in documents:
        try:
            code = document['code']
            print("Code: "+code)
            # Sync QG
            # await syncQG(code)
            print ("Done: "+code)
            success += 1
        except Exception as e:
            print(e)
            list_code_fail.append(code)
            fail += 1
        

# Run the main function
asyncio.run(main())
