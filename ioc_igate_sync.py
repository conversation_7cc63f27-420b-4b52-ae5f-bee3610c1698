from datetime import datetime
import json
import os
import time
import subprocess
import logging
import pytz
from pymongo import MongoClient
from bson.son import SON
from bson import ObjectId
import mysql.connector
from mysql.connector import Error

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("vpn_switch_sync.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# Set console encoding to handle Unicode for Windows environment
import sys
import io
import codecs
if sys.platform == 'win32':
    # Force UTF-8 encoding for stdout to handle Vietnamese characters
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

# Configuration
CONFIG = {
    "vpn1": {
        "name": "MySQL VPN",
        "connect_command": "rasdial \"MySQL VPN\" username password",  # Replace with actual command
        "disconnect_command": "rasdial \"MySQL VPN\" /disconnect",
        "mysql": {
            "host": "***********",
            "user": "root",
            "password": "root",
            "database": "cmu_db_dtm"
        }
    },
    "vpn2": {
        "name": "MongoDB VPN",
        "connect_command": "rasdial \"MongoDB VPN\" username password",  # Replace with actual command
        "disconnect_command": "rasdial \"MongoDB VPN\" /disconnect",
        "mysql": {
            "host": "*************",
            "user": "etl_cmu",
            "password": "Ioc@2024",
            "database": "cmu_db_dtm"
        },
        "mongo": {
            "connection_string": "****************************************************************************************************************************",
            "database": "svcPadman",
            "collection": "dossier"
        }
    }
}

# Temporary storage files
TEMP_DIR = "temp_data"
AGENCIES_FILE = os.path.join(TEMP_DIR, "agencies.json")
PERIODS_FILE = os.path.join(TEMP_DIR, "periods.json")
PROCEDURES_FILE = os.path.join(TEMP_DIR, "procedures.json")
RESULTS_FILE = os.path.join(TEMP_DIR, "results.json")

# Ensure temporary directory exists
os.makedirs(TEMP_DIR, exist_ok=True)

# VPN connection management
def connect_vpn(vpn_config):
    """Connect to the specified VPN"""
    logger.info(f"Connecting to {vpn_config['name']}...")
    try:
        subprocess.run(vpn_config['connect_command'], shell=True, check=True)
        logger.info(f"Connected to {vpn_config['name']}")
        # Wait for connection to stabilize
        time.sleep(5)
        return True
    except subprocess.SubprocessError as e:
        logger.error(f"Failed to connect to {vpn_config['name']}: {e}")
        return False

def disconnect_vpn(vpn_config):
    """Disconnect from the specified VPN"""
    logger.info(f"Disconnecting from {vpn_config['name']}...")
    try:
        subprocess.run(vpn_config['disconnect_command'], shell=True, check=True)
        logger.info(f"Disconnected from {vpn_config['name']}")
        # Wait for disconnection to complete
        time.sleep(3)
        return True
    except subprocess.SubprocessError as e:
        logger.error(f"Failed to disconnect from {vpn_config['name']}: {e}")
        return False

# MySQL connection helper
def get_mysql_connection(config):
    """Establish a MySQL connection with the given configuration"""
    try:
        connection = mysql.connector.connect(
            host=config["host"],
            user=config["user"],
            password=config["password"],
            database=config["database"]
        )
        return connection
    except Error as e:
        logger.error(f"MySQL connection error: {e}")
        return None

# MongoDB connection helper
def get_mongodb_connection(config):
    """Establish a MongoDB connection with the given configuration"""
    try:
        client = MongoClient(config["connection_string"])
        db = client[config["database"]]
        collection = db[config["collection"]]
        return collection
    except Exception as e:
        logger.error(f"MongoDB connection error: {e}")
        return None

# Data acquisition functions - VPN1
def get_list_co_quan(mysql_config):
    """Get the list of agencies from MySQL (VPN1)"""
    connection = None
    try:
        connection = get_mysql_connection(mysql_config)
        if not connection:
            return []
        
        cursor = connection.cursor()
        query = "SELECT ID_DON_VI, TEN_DON_VI FROM igate_api_dm_co_quan_v2_0"
        cursor.execute(query)
        result = cursor.fetchall()
        
        # Convert to list of dicts for JSON serialization
        agencies = [{"id": row[0], "name": row[1]} for row in result]
        return agencies
    except Error as e:
        logger.error(f"Error getting agencies: {e}")
        return []
    finally:
        if connection and connection.is_connected():
            connection.close()

def get_list_procedure_id_from_db(mysql_config, id_don_vi):
    """Get the list of procedure IDs for a specific agency (VPN1)"""
    connection = None
    try:
        connection = get_mysql_connection(mysql_config)
        if not connection:
            return []
        
        cursor = connection.cursor()
        query = "SELECT ID_LINH_VUC FROM igate_dm_linh_vuc_v2_0 WHERE ID_DON_VI = %s"
        cursor.execute(query, (id_don_vi,))
        result = cursor.fetchall()
        list_procedure_id = [row[0] for row in result]
        return list_procedure_id
    except Error as e:
        logger.error(f"Error getting procedure IDs: {e}")
        return []
    finally:
        if connection and connection.is_connected():
            connection.close()

def get_list_dm_ky(mysql_config, thang, nam):
    """Get the list of periods for a specific month and year"""
    connection = None
    try:
        connection = get_mysql_connection(mysql_config)
        if not connection:
            return []
        
        cursor = connection.cursor()
        query = "SELECT ID_KY, GIA_TRI_NGAY, HIEU_LUC_TU, HIEU_LUC_DEN FROM dm_ky WHERE GIA_TRI_THANG = %s and GIA_TRI_NAM = %s and LOAI_KY = 'M'"
        cursor.execute(query, (thang, nam))
        result = cursor.fetchall()
        
        # Convert to list of dicts for JSON serialization
        periods = []
        for row in result:
            periods.append({
                "id_ky": row[0],
                "gia_tri_ngay": row[1].strftime("%Y-%m-%d") if row[1] else None,
                "hieu_luc_tu": row[2].strftime("%Y-%m-%d") if row[2] else None,
                "hieu_luc_den": row[3].strftime("%Y-%m-%d") if row[3] else None
            })
        return periods
    except Error as e:
        logger.error(f"Error getting periods: {e}")
        return []
    finally:
        if connection and connection.is_connected():
            connection.close()

# MongoDB data processing
def build_criteria_statistics_sync_dvcqg(input):
    """Build the criteria for MongoDB aggregation"""
    criteria_list = []

    timezone = pytz.timezone("Asia/Ho_Chi_Minh")
    from_date = datetime.strptime(input['fromDate'], "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)
    to_date = datetime.strptime(input['toDate'], "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)

    criteria_list.append({
        '$or': [
            {
                '$and': [
                    {'dossierStatus.id': {'$in': [6, 12, 13]}},
                    {'appliedDate': {'$gte': from_date}},
                    {'appliedDate': {'$lte': to_date}}
                ]
            },
            {
                '$and': [
                    {'appliedDate': {'$gte': from_date}},
                    {'appliedDate': {'$lte': to_date}}
                ]
            },
            {
                '$and': [
                    {'acceptedDate': {'$exists': True}},
                    {'acceptedDate': {'$lte': from_date}},
                    {'$or': [
                        {'dossierStatus.id': {'$in': [0, 1, 2, 3, 8, 9, 10, 11]}},
                        {
                            '$and': [
                                {'dossierStatus.id': {'$in': [4, 5]}},
                                {'completedDate': {'$gte': from_date}}
                            ]
                        }
                    ]}
                ]
            }
        ]
    })

    agency_temp = ObjectId(input['agencyId'])
    if input.get('isAncestors', False):
        criteria_list.append({
            '$or': [
                {'agency.id': agency_temp},
                {'agency.ancestors.id': agency_temp},
                {'agency.parent.id': agency_temp}
            ]
        })
    else:
        criteria_list.append({'agency.id': agency_temp})

    if criteria_list:
        criteria = {'$and': criteria_list}
    else:
        criteria = {}

    return criteria

def build_projection_operation_sync_dvcqg(from_date_str, to_date_str):
    """Build the projection for MongoDB aggregation"""
    timezone = pytz.timezone("Asia/Ho_Chi_Minh")
    from_date = datetime.strptime(from_date_str, "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)
    to_date = datetime.strptime(to_date_str, "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)

    projection = {
        "procedureId": "$procedure.sector.id",
        "tiepNhanTrongKy": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                            ]
                        },
                        {
                            "$and": [
                                {"$gte": ["$acceptedDate", from_date]},
                                {"$lte": ["$acceptedDate", to_date]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "tiepNhanTrucTuyen": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$eq": ["$applyMethodId", 0]}
                            ]
                        },
                        {
                            "$and": [
                                {"$gte": ["$acceptedDate", from_date]},
                                {"$lte": ["$acceptedDate", to_date]},
                                {"$eq": ["$applyMethodId", 0]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "tiepNhanTrucTiep": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$eq": ["$applyMethodId", 1]}
                            ]
                        },
                        {
                            "$and": [
                                {"$gte": ["$acceptedDate", from_date]},
                                {"$lte": ["$acceptedDate", to_date]},
                                {"$eq": ["$applyMethodId", 1]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "kyTruocChuyenSang": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [0, 1, 2, 3, 8, 9, 10, 11]]},
                        {"$lt": ["$acceptedDate", from_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLy": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$in": ["$dossierStatusId", [6, 12, 13]]}
                            ]
                        },
                        {
                            "$and": [
                                {"$in": ["$dossierStatusId", [4, 5]]},
                                {"$lte": ["$completedDate", to_date]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLyDungHan": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$in": ["$dossierStatusId", [6, 12, 13]]}
                            ]
                        },
                        {
                            "$and": [
                                {"$in": ["$dossierStatusId", [4, 5]]},
                                {"$lte": ["$completedDate", to_date]},
                                {"$eq": ["$completedDate", "$appointmentDate"]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLySomHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [4, 5]]},
                        {"$lte": ["$completedDate", to_date]},
                        {"$lt": ["$completedDate", "$appointmentDate"]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLyQuaHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [4, 5]]},
                        {"$lte": ["$completedDate", to_date]},
                        {"$gt": ["$completedDate", "$appointmentDate"]},
                        {"$gt": ["$processTime", 0]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "dangXuLy": {
            "$cond": {
                "if": {
                    "$or": [
                        {"$in": ["$dossierStatusId", [0, 1, 2, 3, 8, 9, 10, 11]]},
                        {
                            "$and": [
                                {"$in": ["$dossierStatusId", [4, 5]]},
                                {"$gt": ["$completedDate", to_date]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "dangXuLyTrongHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {
                            "$or": [
                                {"$in": ["$dossierStatusId", [0, 1, 2, 3, 8, 9, 10, 11]]},
                                {
                                    "$and": [
                                        {"$in": ["$dossierStatusId", [4, 5]]},
                                        {"$gt": ["$completedDate", to_date]}
                                    ]
                                }
                            ]
                        },
                        {
                            "$or": [
                                {"$eq": ["$undefindedCompleteTime", 1]},
                                {"$eq": ["$dossierMenuTaskRemindId", ObjectId("60f6364e09cbf91d41f88859")]},
                                {"$in": ["$dossierStatusId", [1, 3]]},
                                {"$lt": ["$appointmentDate", 0]},
                                {
                                    "$and": [
                                        {"$eq": ["$undefindedCompleteTime", 0]},
                                        {"$gte": ["$appointmentDate", to_date]}
                                    ]
                                }
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "dangXuLyQuaHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {
                            "$or": [
                                {"$in": ["$dossierStatusId", [0, 2, 8, 9, 10, 11]]},
                                {
                                    "$and": [
                                        {"$in": ["$dossierStatusId", [4, 5]]},
                                        {"$gt": ["$completedDate", to_date]},
                                        {"$gt": ["$processTime", 0]}
                                    ]
                                }
                            ]
                        },
                        {"$ne": ["$dossierMenuTaskRemindId", ObjectId("60f6364e09cbf91d41f88859")]},
                        {"$eq": ["$undefindedCompleteTime", 0]},
                        {"$gt": ["$appointmentDate", 0]},
                        {"$lt": ["$appointmentDate", to_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "DungXuLy": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$eq": ["$dossierMenuTaskRemindId", ObjectId("60f52e6a09cbf91d41f88836")]},
                        {"$gte": ["$appliedDate", from_date]},
                        {"$lte": ["$appliedDate", to_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "TraLai": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [6]]},
                        {"$gte": ["$appliedDate", from_date]},
                        {"$lte": ["$appliedDate", to_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
    }

    return projection

def process_mongo_data(mongo_collection, id_don_vi, id_ky, input, procedure_ids):
    """Process data from MongoDB and prepare results for MySQL"""
    # Define the aggregation pipeline
    pipeline = [
        {
            '$addFields': {
                'dossierStatusId': '$dossierStatus.id',
                'dossierMenuTaskRemindId': '$dossierMenuTaskRemind.id',
                'applyMethodId': '$applyMethod.id',
                'processTime': '$procedureProcessDefinition.processDefinition.processTime',
            }
        },
        {
            '$match': build_criteria_statistics_sync_dvcqg(input)
        },
        {
            '$project': build_projection_operation_sync_dvcqg(input['fromDate'], input['toDate'])
        },
        {
            '$group': {
                '_id': '$procedureId',
                'procedureId': {'$first': '$procedureId'},
                'received': {'$sum': '$tiepNhanTrongKy'},
                'onlineReceived': {'$sum': '$tiepNhanTrucTuyen'},
                'directReceived': {'$sum': '$tiepNhanTrucTiep'},
                'receivedOld': {'$sum': '$kyTruocChuyenSang'},
                'resolved': {'$sum': '$daXuLy'},
                'resolvedOnTime': {'$sum': '$daXuLyDungHan'},
                'resolvedEarly': {'$sum': '$daXuLySomHan'},
                'resolvedOverdue': {'$sum': '$daXuLyQuaHan'},
                'unresolved': {'$sum': '$dangXuLy'},
                'unresolvedOnTime': {'$sum': '$dangXuLyTrongHan'},
                'unresolvedOverdue': {'$sum': '$dangXuLyQuaHan'},
                'passed': {'$sum': '$DungXuLy'},
                'returned': {'$sum': '$TraLai'}
            }
        }
    ]

    # Perform the aggregation
    try:
        result = list(mongo_collection.aggregate(pipeline))
    except Exception as e:
        logger.error(f"MongoDB aggregation error: {e}")
        return []

    # Prepare the results for all procedure IDs
    all_results = []
    for procedure_id in procedure_ids:
        result_item = next((item for item in result if str(item.get('procedureId')) == procedure_id), None)
        
        record = {
            "id_ky": id_ky,
            "id_don_vi": id_don_vi,
            "id_linh_vuc": procedure_id,
            "received": 0,
            "received_online": 0,
            "received_direct": 0,
            "received_old": 0,
            "total_processing": 0,
            "total_resolved": 0,
            "total_resolved_on_time": 0,
            "total_resolved_early": 0,
            "total_resolved_over_due": 0,
            "total_unresolved": 0,
            "total_unresolved_on_due": 0,
            "total_unresolved_over_due": 0,
            "total_paused": 0,
            "total_returned": 0
        }
        
        if result_item:
            record.update({
                "received": result_item.get('received', 0),
                "received_online": result_item.get('onlineReceived', 0),
                "received_direct": result_item.get('directReceived', 0),
                "received_old": result_item.get('receivedOld', 0),
                "total_processing": result_item.get('received', 0) + result_item.get('receivedOld', 0),
                "total_resolved": result_item.get('resolved', 0),
                "total_resolved_on_time": result_item.get('resolvedOnTime', 0),
                "total_resolved_early": result_item.get('resolvedEarly', 0),
                "total_resolved_over_due": result_item.get('resolvedOverdue', 0),
                "total_unresolved": result_item.get('unresolved', 0),
                "total_unresolved_on_due": result_item.get('unresolvedOnTime', 0),
                "total_unresolved_over_due": result_item.get('unresolvedOverdue', 0),
                "total_paused": result_item.get('passed', 0),
                "total_returned": result_item.get('returned', 0)
            })
        
        all_results.append(record)
    
    return all_results

def insert_results_to_mysql(mysql_config, results):
    """Insert processed results into MySQL (VPN1)"""
    connection = None
    try:
        connection = get_mysql_connection(mysql_config)
        if not connection:
            return False
        
        cursor = connection.cursor()
        
        insert_query = """
        INSERT INTO igate_tk_don_vi_linh_vuc_v2_0 (
            ID_KY,
            ID_DONVI_LINHVUC,
            TIEP_NHAN,
            TIEP_NHAN_TRUC_TIEP,
            TIEP_NHAN_TRUC_TUYEN,
            DANG_XU_LY,
            DANG_XU_LY_TRONG_HAN,
            DANG_XU_LY_QUA_HAN,
            HOAN_THANH,
            HOAN_THANH_TRONG_HAN,
            HOAN_THANH_TRUOC_HAN,
            HOAN_THANH_TRE_HAN,
            DUNG_XU_LY,
            TRA_LAI
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            TIEP_NHAN = VALUES(TIEP_NHAN),
            TIEP_NHAN_TRUC_TIEP = VALUES(TIEP_NHAN_TRUC_TIEP),
            TIEP_NHAN_TRUC_TUYEN = VALUES(TIEP_NHAN_TRUC_TUYEN),
            DANG_XU_LY = VALUES(DANG_XU_LY),
            DANG_XU_LY_TRONG_HAN = VALUES(DANG_XU_LY_TRONG_HAN),
            DANG_XU_LY_QUA_HAN = VALUES(DANG_XU_LY_QUA_HAN),
            HOAN_THANH = VALUES(HOAN_THANH),
            HOAN_THANH_TRONG_HAN = VALUES(HOAN_THANH_TRONG_HAN),
            HOAN_THANH_TRUOC_HAN = VALUES(HOAN_THANH_TRUOC_HAN),
            HOAN_THANH_TRE_HAN = VALUES(HOAN_THANH_TRE_HAN),
            DUNG_XU_LY = VALUES(DUNG_XU_LY),
            TRA_LAI = VALUES(TRA_LAI)
        """
        
        for result in results:
            cursor.execute(insert_query, (
                result["id_ky"],
                result["id_don_vi"] + result["id_linh_vuc"],
                result["received"],
                result["received_direct"],
                result["received_online"],
                result["total_unresolved"],
                result["total_unresolved_on_due"],
                result["total_unresolved_over_due"],
                result["total_resolved"],
                result["total_resolved_on_time"],
                result["total_resolved_early"],
                result["total_resolved_over_due"],
                result["total_paused"],
                result["total_returned"]
            ))
        
        connection.commit()
        logger.info(f"Inserted {len(results)} records into MySQL")
        return True
    except Error as e:
        logger.error(f"Error inserting results to MySQL: {e}")
        return False
    finally:
        if connection and connection.is_connected():
            connection.close()

# File operations for data persistence between VPN connections
def save_to_json(data, file_path):
    """Save data to a JSON file"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving data to {file_path}: {e}")
        return False

def load_from_json(file_path):
    """Load data from a JSON file"""
    try:
        if not os.path.exists(file_path):
            return None
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading data from {file_path}: {e}")
        return None

# Main workflow
def phase1_get_initial_data():
    """Phase 1: Connect to MySQL VPN and get agencies, procedures and periods data"""
    logger.info("Starting Phase 1: Getting initial data from MySQL VPN")
    
    # Connect to MySQL VPN
    if not connect_vpn(CONFIG["vpn1"]):
        logger.error("Failed to connect to MySQL VPN. Aborting Phase 1.")
        return False
    
    try:
        # Get the list of agencies
        agencies = get_list_co_quan(CONFIG["vpn1"]["mysql"])
        if not agencies:
            logger.error("Failed to get agencies data. Aborting Phase 1.")
            return False
        
        # Save agencies data to file
        if not save_to_json(agencies, AGENCIES_FILE):
            logger.error("Failed to save agencies data. Aborting Phase 1.")
            return False
        
        logger.info(f"Retrieved {len(agencies)} agencies")
        
        # Get procedure IDs for each agency
        all_procedures = {}
        for agency in agencies:
            agency_id = agency["id"]
            procedures = get_list_procedure_id_from_db(CONFIG["vpn1"]["mysql"], agency_id)
            all_procedures[agency_id] = procedures
            
            # Use ASCII-safe version of agency name for console logging
            agency_name = agency["name"]
            try:
                # Try logging with the full Unicode name
                logger.info(f"Retrieved {len(procedures)} procedures for agency {agency_name}")
            except UnicodeEncodeError:
                # Fall back to a safe representation if encoding fails
                safe_name = agency_name.encode('ascii', 'replace').decode('ascii')
                logger.info(f"Retrieved {len(procedures)} procedures for agency ID: {agency_id} (name contains non-ASCII characters)")
        
        # Save procedures data to file
        if not save_to_json(all_procedures, PROCEDURES_FILE):
            logger.error("Failed to save procedures data. Aborting Phase 1.")
            return False
            
        # Get periods data (now from MySQL VPN)
        month = 3  # March
        year = 2025
        periods = get_list_dm_ky(CONFIG["vpn1"]["mysql"], month, year)
        if not periods:
            logger.error("Failed to get periods data. Aborting Phase 1.")
            return False
        
        # Save periods data to file
        if not save_to_json(periods, PERIODS_FILE):
            logger.error("Failed to save periods data. Aborting Phase 1.")
            return False
            
        logger.info(f"Retrieved {len(periods)} periods for month {month}, year {year}")
        
        logger.info("Phase 1 completed successfully")
        return True
    finally:
        # Disconnect from MySQL VPN
        disconnect_vpn(CONFIG["vpn1"])

def phase2_get_periods_data():
    """Phase 2: Connect to VPN2 (MongoDB VPN) and get periods data"""
    logger.info("Starting Phase 2: Getting periods data from MongoDB VPN")
    
    # Connect to VPN2 (MongoDB VPN)
    if not connect_vpn(CONFIG["vpn2"]):
        logger.error("Failed to connect to MongoDB VPN. Aborting Phase 2.")
        return False
    
    try:
        # Get the list of periods for the specified month and year
        month = 3  # March
        year = 2025
        periods = get_list_dm_ky(CONFIG["vpn2"]["mysql"], month, year)
        if not periods:
            logger.error("Failed to get periods data. Aborting Phase 2.")
            return False
        
        # Save periods data to file
        if not save_to_json(periods, PERIODS_FILE):
            logger.error("Failed to save periods data. Aborting Phase 2.")
            return False
        
        logger.info(f"Retrieved {len(periods)} periods for month {month}, year {year}")
        logger.info("Phase 2 completed successfully")
        return True
    finally:
        # Disconnect from VPN2 (MongoDB VPN)
        disconnect_vpn(CONFIG["vpn2"])

def phase2_process_mongodb_data():
    """Phase 2: Connect to MongoDB VPN and process data from MongoDB"""
    logger.info("Starting Phase 2: Processing MongoDB data from MongoDB VPN")
    
    # Load agencies, procedures, and periods data
    agencies = load_from_json(AGENCIES_FILE)
    if not agencies:
        logger.error("Failed to load agencies data. Aborting Phase 2.")
        return False
    
    procedures = load_from_json(PROCEDURES_FILE)
    if not procedures:
        logger.error("Failed to load procedures data. Aborting Phase 2.")
        return False
    
    periods = load_from_json(PERIODS_FILE)
    if not periods:
        logger.error("Failed to load periods data. Aborting Phase 2.")
        return False
    
    # Connect to MongoDB VPN
    if not connect_vpn(CONFIG["vpn2"]):
        logger.error("Failed to connect to MongoDB VPN. Aborting Phase 2.")
        return False
    
    try:
        # Connect to MongoDB
        mongo_collection = get_mongodb_connection(CONFIG["vpn2"]["mongo"])
        if not mongo_collection:
            logger.error("Failed to connect to MongoDB. Aborting Phase 2.")
            return False
        
        # Process data for each agency and period
        all_results = []
        
        # Track progress
        total_agencies = len(agencies)
        total_periods = len(periods)
        total_combinations = total_agencies * total_periods
        processed_combinations = 0
        
        for agency in agencies:
            agency_id = agency["id"]
            agency_name = agency["name"]
            agency_procedures = procedures.get(agency_id, [])
            
            if not agency_procedures:
                logger.warning(f"No procedures found for agency {agency_name}, skipping")
                processed_combinations += total_periods
                continue
            
            for period in periods:
                period_id = period["id_ky"]
                from_date = f"{period['hieu_luc_tu']}T00:00"
                to_date = f"{period['hieu_luc_den']}T23:59"
                
                # Build input for MongoDB query
                input_data = {
                    'fromDate': from_date,
                    'toDate': to_date,
                    'agencyId': agency_id,
                    'isAncestors': True
                }
                
                # Process data from MongoDB
                try:
                    logger.info(f"Processing data for agency '{agency_name}', period {period_id}")
                    results = process_mongo_data(mongo_collection, agency_id, period_id, input_data, agency_procedures)
                    
                    if results:
                        all_results.extend(results)
                        logger.info(f"Successfully processed {len(results)} results for agency '{agency_name}', period {period_id}")
                    else:
                        logger.warning(f"No results for agency '{agency_name}', period {period_id}")
                except Exception as e:
                    logger.error(f"Error processing data for agency '{agency_name}', period {period_id}: {e}")
                
                processed_combinations += 1
                logger.info(f"Progress: {processed_combinations}/{total_combinations} ({processed_combinations/total_combinations*100:.1f}%)")
        
        # Save all results to file
        if not save_to_json(all_results, RESULTS_FILE):
            logger.error("Failed to save results data. Aborting Phase 2.")
            return False
        
        logger.info(f"Processed a total of {len(all_results)} records")
        logger.info("Phase 2 completed successfully")
        return True
    finally:
        # Disconnect from MongoDB VPN
        disconnect_vpn(CONFIG["vpn2"])
        
        for agency in agencies:
            agency_id = agency["id"]
            agency_name = agency["name"]
            agency_procedures = procedures.get(agency_id, [])
            
            if not agency_procedures:
                logger.warning(f"No procedures found for agency {agency_name}, skipping")
                processed_combinations += total_periods
                continue
            
            for period in periods:
                period_id = period["id_ky"]
                from_date = f"{period['hieu_luc_tu']}T00:00"
                to_date = f"{period['hieu_luc_den']}T23:59"
                
                # Build input for MongoDB query
                input_data = {
                    'fromDate': from_date,
                    'toDate': to_date,
                    'agencyId': agency_id,
                    'isAncestors': True
                }
                
                # Process data from MongoDB
                try:
                    logger.info(f"Processing data for agency '{agency_name}', period {period_id}")
                    results = process_mongo_data(mongo_collection, agency_id, period_id, input_data, agency_procedures)
                    
                    if results:
                        all_results.extend(results)
                        logger.info(f"Successfully processed {len(results)} results for agency '{agency_name}', period {period_id}")
                    else:
                        logger.warning(f"No results for agency '{agency_name}', period {period_id}")
                except Exception as e:
                    logger.error(f"Error processing data for agency '{agency_name}', period {period_id}: {e}")
                
                processed_combinations += 1
                logger.info(f"Progress: {processed_combinations}/{total_combinations} ({processed_combinations/total_combinations*100:.1f}%)")
        
        # Save all results to file
        if not save_to_json(all_results, RESULTS_FILE):
            logger.error("Failed to save results data. Aborting Phase 3.")
            return False
        
        logger.info(f"Processed a total of {len(all_results)} records")
        logger.info("Phase 3 completed successfully")
        return True
    finally:
        # Disconnect from VPN2 (MongoDB VPN)
        disconnect_vpn(CONFIG["vpn2"])

def phase3_insert_data_to_mysql():
    """Phase 3: Connect to MySQL VPN and insert processed data into MySQL"""
    logger.info("Starting Phase 3: Inserting data into MySQL via MySQL VPN")
    
    # Load the processed results
    results = load_from_json(RESULTS_FILE)
    if not results:
        logger.error("Failed to load results data. Aborting Phase 3.")
        return False
    
    # Connect to MySQL VPN
    if not connect_vpn(CONFIG["vpn1"]):
        logger.error("Failed to connect to MySQL VPN. Aborting Phase 3.")
        return False
    
    try:
        # Insert results into MySQL
        success = insert_results_to_mysql(CONFIG["vpn1"]["mysql"], results)
        if not success:
            logger.error("Failed to insert results into MySQL. Aborting Phase 3.")
            return False
        
        logger.info("Phase 3 completed successfully")
        return True
    finally:
        # Disconnect from MySQL VPN
        disconnect_vpn(CONFIG["vpn1"])
        
        for agency in agencies:
            agency_id = agency["id"]
            agency_name = agency["name"]
            agency_procedures = procedures.get(agency_id, [])
            
            if not agency_procedures:
                logger.warning(f"No procedures found for agency {agency_name}, skipping")
                processed_combinations += total_periods
                continue
            
            for period in periods:
                period_id = period["id_ky"]
                from_date = f"{period['hieu_luc_tu']}T00:00"
                to_date = f"{period['hieu_luc_den']}T23:59"
                
                # Build input for MongoDB query
                input_data = {
                    'fromDate': from_date,
                    'toDate': to_date,
                    'agencyId': agency_id,
                    'isAncestors': True
                }
                
                # Process data from MongoDB
                try:
                    logger.info(f"Processing data for agency '{agency_name}', period {period_id}")
                    results = process_mongo_data(mongo_collection, agency_id, period_id, input_data, agency_procedures)
                    
                    if results:
                        all_results.extend(results)
                        logger.info(f"Successfully processed {len(results)} results for agency '{agency_name}', period {period_id}")
                    else:
                        logger.warning(f"No results for agency '{agency_name}', period {period_id}")
                except Exception as e:
                    logger.error(f"Error processing data for agency '{agency_name}', period {period_id}: {e}")
                
                processed_combinations += 1
                logger.info(f"Progress: {processed_combinations}/{total_combinations} ({processed_combinations/total_combinations*100:.1f}%)")
        
        # Save all results to file
        if not save_to_json(all_results, RESULTS_FILE):
            logger.error("Failed to save results data. Aborting Phase 3.")
            return False
        
        logger.info(f"Processed a total of {len(all_results)} records")
        logger.info("Phase 3 completed successfully")
        return True
    finally:
        # Disconnect from VPN2
        disconnect_vpn(CONFIG["vpn2"])

def phase4_insert_data_to_mysql():
    """Phase 4: Connect to VPN1 and insert processed data into MySQL"""
    logger.info("Starting Phase 4: Inserting data into MySQL via VPN1")
    
    # Load the processed results
    results = load_from_json(RESULTS_FILE)
    if not results:
        logger.error("Failed to load results data. Aborting Phase 4.")
        return False
    
    # Connect to VPN1
    if not connect_vpn(CONFIG["vpn1"]):
        logger.error("Failed to connect to VPN1. Aborting Phase 4.")
        return False
    
    try:
        # Insert results into MySQL
        success = insert_results_to_mysql(CONFIG["vpn1"]["mysql"], results)
        if not success:
            logger.error("Failed to insert results into MySQL. Aborting Phase 4.")
            return False
        
        logger.info("Phase 4 completed successfully")
        return True
    finally:
        # Disconnect from VPN1
        disconnect_vpn(CONFIG["vpn1"])

def manual_vpn_switch_workflow():
    """Run the workflow with manual VPN switching prompts"""
    logger.info("Starting data synchronization workflow with manual VPN switching")
    
    # Phase 1: Get initial data from MySQL VPN
    logger.info("Please ensure you are connected to MySQL VPN")
    input("Press Enter when connected to MySQL VPN...")
    
    success = True
    try:
        # Get agencies and procedure IDs
        agencies = get_list_co_quan(CONFIG["vpn1"]["mysql"])
        if not agencies:
            logger.error("Failed to get agencies data.")
            return False
        
        # Save agencies data to file
        if not save_to_json(agencies, AGENCIES_FILE):
            logger.error("Failed to save agencies data.")
            return False
        
        logger.info(f"Retrieved {len(agencies)} agencies")
        
        # Get procedure IDs for each agency
        all_procedures = {}
        for agency in agencies:
            agency_id = agency["id"]
            procedures = get_list_procedure_id_from_db(CONFIG["vpn1"]["mysql"], agency_id)
            all_procedures[agency_id] = procedures
            
            # Use ASCII-safe version of agency name for console logging
            agency_name = agency["name"]
            try:
                # Try logging with the full Unicode name
                logger.info(f"Retrieved {len(procedures)} procedures for agency {agency_name}")
            except UnicodeEncodeError:
                # Fall back to a safe representation if encoding fails
                safe_name = agency_name.encode('ascii', 'replace').decode('ascii')
                logger.info(f"Retrieved {len(procedures)} procedures for agency ID: {agency_id} (name contains non-ASCII characters)")
        
        # Save procedures data to file
        if not save_to_json(all_procedures, PROCEDURES_FILE):
            logger.error("Failed to save procedures data.")
            return False
            
        # Get periods data from MySQL VPN
        month = 3  # March
        year = 2025
        periods = get_list_dm_ky(CONFIG["vpn1"]["mysql"], month, year)
        if not periods:
            logger.error("Failed to get periods data.")
            return False
        
        # Save periods data to file
        if not save_to_json(periods, PERIODS_FILE):
            logger.error("Failed to save periods data.")
            return False
        
        logger.info(f"Retrieved {len(periods)} periods for month {month}, year {year}")
    except Exception as e:
        logger.error(f"Error in Phase 1: {e}")
        success = False
    
    if not success:
        return False
    
    # Phase 2: Process MongoDB data from MongoDB VPN
    logger.info("\nPlease disconnect from MySQL VPN and connect to MongoDB VPN")
    input("Press Enter when connected to MongoDB VPN...")
    
    success = True
    try:
        # Load agencies and procedures data
        agencies = load_from_json(AGENCIES_FILE)
        procedures = load_from_json(PROCEDURES_FILE)
        periods = load_from_json(PERIODS_FILE)
        
        # Connect to MongoDB
        mongo_collection = get_mongodb_connection(CONFIG["vpn2"]["mongo"])
        if not mongo_collection:
            logger.error("Failed to connect to MongoDB.")
            return False
        
        # Process data for each agency and period
        all_results = []
        
        for agency in agencies:
            agency_id = agency["id"]
            agency_name = agency["name"]
            agency_procedures = procedures.get(agency_id, [])
            
            if not agency_procedures:
                logger.warning(f"No procedures found for agency {agency_name}, skipping")
                continue
            
            for period in periods:
                period_id = period["id_ky"]
                from_date = f"{period['hieu_luc_tu']}T00:00"
                to_date = f"{period['hieu_luc_den']}T23:59"
                
                # Build input for MongoDB query
                input_data = {
                    'fromDate': from_date,
                    'toDate': to_date,
                    'agencyId': agency_id,
                    'isAncestors': True
                }
                
                # Process data from MongoDB
                try:
                    logger.info(f"Processing data for agency '{agency_name}', period {period_id}")
                    results = process_mongo_data(mongo_collection, agency_id, period_id, input_data, agency_procedures)
                    
                    if results:
                        all_results.extend(results)
                        logger.info(f"Successfully processed {len(results)} results for agency '{agency_name}', period {period_id}")
                    else:
                        logger.warning(f"No results for agency '{agency_name}', period {period_id}")
                except Exception as e:
                    logger.error(f"Error processing data for agency '{agency_name}', period {period_id}: {e}")
        
        # Save all results to file
        if not save_to_json(all_results, RESULTS_FILE):
            logger.error("Failed to save results data.")
            return False
        
        logger.info(f"Processed a total of {len(all_results)} records")
    except Exception as e:
        logger.error(f"Error in Phase 2: {e}")
        success = False
    
    if not success:
        return False
    
    # Phase 3: Insert data into MySQL via MySQL VPN
    logger.info("\nPlease disconnect from MongoDB VPN and connect back to MySQL VPN")
    input("Press Enter when connected to MySQL VPN...")
    
    success = True
    try:
        # Load the processed results
        results = load_from_json(RESULTS_FILE)
        if not results:
            logger.error("Failed to load results data.")
            return False
        
        # Insert results into MySQL
        success = insert_results_to_mysql(CONFIG["vpn1"]["mysql"], results)
        if not success:
            logger.error("Failed to insert results into MySQL.")
            return False
        
        logger.info("Data synchronization completed successfully!")
    except Exception as e:
        logger.error(f"Error in Phase 3: {e}")
        success = False
    
    return success
    except Exception as e:
        logger.error(f"Error in Phase 1: {e}")
        success = False
    
    if not success:
        return False
    
    # Phase 2: Get periods data from VPN2 (MongoDB VPN)
    logger.info("\nPlease disconnect from MySQL VPN and connect to MongoDB VPN")
    input("Press Enter when connected to MongoDB VPN...")
    
    success = True
    try:
        # Get periods data
        month = 3  # March
        year = 2025
        periods = get_list_dm_ky(CONFIG["vpn2"]["mysql"], month, year)
        if not periods:
            logger.error("Failed to get periods data.")
            return False
        
        # Save periods data to file
        if not save_to_json(periods, PERIODS_FILE):
            logger.error("Failed to save periods data.")
            return False
        
        logger.info(f"Retrieved {len(periods)} periods for month {month}, year {year}")
        
        # Process MongoDB data
        # Load agencies and procedures data
        agencies = load_from_json(AGENCIES_FILE)
        procedures = load_from_json(PROCEDURES_FILE)
        
        # Connect to MongoDB
        mongo_collection = get_mongodb_connection(CONFIG["vpn2"]["mongo"])
        if not mongo_collection:
            logger.error("Failed to connect to MongoDB.")
            return False
        
        # Process data for each agency and period
        all_results = []
        
        for agency in agencies:
            agency_id = agency["id"]
            agency_name = agency["name"]
            agency_procedures = procedures.get(agency_id, [])
            
            if not agency_procedures:
                logger.warning(f"No procedures found for agency {agency_name}, skipping")
                continue
            
            for period in periods:
                period_id = period["id_ky"]
                from_date = f"{period['hieu_luc_tu']}T00:00"
                to_date = f"{period['hieu_luc_den']}T23:59"
                
                # Build input for MongoDB query
                input_data = {
                    'fromDate': from_date,
                    'toDate': to_date,
                    'agencyId': agency_id,
                    'isAncestors': True
                }
                
                # Process data from MongoDB
                try:
                    logger.info(f"Processing data for agency '{agency_name}', period {period_id}")
                    results = process_mongo_data(mongo_collection, agency_id, period_id, input_data, agency_procedures)
                    
                    if results:
                        all_results.extend(results)
                        logger.info(f"Successfully processed {len(results)} results for agency '{agency_name}', period {period_id}")
                    else:
                        logger.warning(f"No results for agency '{agency_name}', period {period_id}")
                except Exception as e:
                    logger.error(f"Error processing data for agency '{agency_name}', period {period_id}: {e}")
        
        # Save all results to file
        if not save_to_json(all_results, RESULTS_FILE):
            logger.error("Failed to save results data.")
            return False
        
        logger.info(f"Processed a total of {len(all_results)} records")
    except Exception as e:
        logger.error(f"Error in Phase 2: {e}")
        success = False
    
    if not success:
        return False
    
    # Phase 3: Insert data into MySQL via VPN1 (MySQL VPN)
    logger.info("\nPlease disconnect from MongoDB VPN and connect back to MySQL VPN")
    input("Press Enter when connected to MySQL VPN...")
    
    success = True
    try:
        # Load the processed results
        results = load_from_json(RESULTS_FILE)
        if not results:
            logger.error("Failed to load results data.")
            return False
        
        # Insert results into MySQL
        success = insert_results_to_mysql(CONFIG["vpn1"]["mysql"], results)
        if not success:
            logger.error("Failed to insert results into MySQL.")
            return False
        
        logger.info("Data synchronization completed successfully!")
    except Exception as e:
        logger.error(f"Error in Phase 3: {e}")
        success = False
    
    return success
    

def main():
    """Main entry point for the script"""
    start_time = datetime.now()
    logger.info(f"Script started at {start_time}")
    
    # Choose workflow mode
    print("\n==== VPN Switching Data Synchronization ====")
    print("1. Tự động chuyển đổi VPN (yêu cầu cấu hình lệnh VPN)")
    print("2. Chuyển đổi VPN thủ công (bạn sẽ được nhắc khi cần chuyển VPN)\n")
    
    choice = input("Chọn chế độ (1 hoặc 2): ")
    
    success = False
    if choice == "1":
        # Automatic VPN switching workflow
        logger.info("Running automatic VPN switching workflow")
        
        # Run all phases in sequence
        if phase1_get_initial_data():
            if phase2_process_mongodb_data():
                success = phase3_insert_data_to_mysql()
    else:
        # Manual VPN switching workflow
        success = manual_vpn_switch_workflow()
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    if success:
        logger.info(f"Data synchronization completed successfully in {duration}")
        print(f"\n✓ Đồng bộ dữ liệu hoàn tất thành công trong {duration}")
    else:
        logger.error(f"Data synchronization failed after {duration}")
        print(f"\n✗ Đồng bộ dữ liệu thất bại sau {duration}")
        print("Vui lòng kiểm tra file log 'vpn_switch_sync.log' để biết thêm chi tiết.")
    
    return success

if __name__ == '__main__':
    main()