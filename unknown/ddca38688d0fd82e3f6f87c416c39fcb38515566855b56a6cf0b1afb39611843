import requests
import mysql.connector

# Connect to the database
cnx = mysql.connector.connect(
    user='clusteradmin',
    password='ClusTer_pAss_2022',
    host='************',
    port='32354',
    database='cmu_bhtn'
)

cursor = cnx.cursor()
arr_uuid = ["9eaad78c-3037-11ee-b413-0050568afb81",
"d6fbf9df-3046-11ee-b413-0050568afb81"]

#Get data from huyen table
#query = ("SELECT a.uuid_hs_huong_tctn, b.ngay_ky, b.so_quyet_dinh from (SELECT SUBSTRING(jobAsJson, LOCATE('uuid', jobAsJson) + 5, 36) as uuid_hs_huong_tctn FROM jobrunr_jobs WHERE LOCATE('uuid', jobAsJson) > 0 and `state` LIKE '%FAILED%' ) as a join hs_huong_tctn b on a.uuid_hs_huong_tctn = b.uuid_hs_huong_tctn")
query = "(Select uuid_hs_huong_tctn,ngay_ky,so_quyet_dinh from hs_huong_tctn where uuid_hs_huong_tctn in ('" + "','".join(arr_uuid) + "'))"
cursor.execute(query)
datas = cursor.fetchall()

# Close the database connection
cnx.close()

# Loop through the retrieved data
for dt in datas:
    uuid = dt[0]
    ngay_ky = dt[1].strftime("%Y-%m-%d")
    so_quyet_dinh = dt[2]
    if so_quyet_dinh is None:
        so_quyet_dinh = ""
    if ngay_ky is None:
        ngay_ky = ""
    print(uuid)

    # Make an API call using the user data
    api_url = "https://bhtncm.camau.gov.vn/sldtbxh/quan-ly-san-viec-lam/ql-bhtn/hs-huong-tctn/cho-so-quyet-dinh?uuidHsHuongTctn="+uuid+"&soQuyetDinh="+so_quyet_dinh+"&ngayKy="+ngay_ky
    bearer_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsibGFib3JfZXhwb3J0Iiwic29jaWFsX3Byb3RlY3Rpb24iLCJlZHVjYXRpb24iLCJjaGlsZHJlbiIsInBheW1lbnQiLCJleGNoYW5nZV9mbG9vcl9tYW5hZ2VtZW50IiwiaW52ZW50b3J5IiwidXNlciIsIm1lcml0b3Jpb3VzX3BlcnNvbiIsImRydWciXSwidXNlcl9uYW1lIjoiY211X29zX2xkdmwiLCJzY29wZSI6WyJSRUFEIiwiV1JJVEUiXSwiZXhwIjoxNzIxNDEwOTA1LCJ1c2VyRGV0YWlscyI6eyJ0ZW5EYW5nTmhhcCI6ImNtdV9vc19sZHZsIiwibWFOZ3VvaUR1bmciOjg2MDIsImhvVGVuIjoiTGFvIMSR4buZbmcgdmnhu4djIGzDoG0gQ01VIiwibWFUaW5oIjoiOTYiLCJtYUh1eWVuIjpudWxsLCJtYVhhIjpudWxsLCJtYVBob25nQmFuIjozMjQsImNhcEJhY0RvblZpIjpudWxsLCJtYURvblZpIjozMDAsInRoZW0iOnRydWUsInN1YSI6dHJ1ZSwieG9hIjp0cnVlLCJjYXBCYWNEViI6MSwidXVpZE5ndW9pRHVuZyI6ImYyZDNlOTk3LWRmZWQtMTFlYy1hMDI2LTAwNTA1Njg4NjNjMiIsImlzU3VwcGVyQWRtaW4iOjAsImlzQ29uZ0RhbiI6MCwiYXZ0IjpudWxsLCJtYVRydW5nVGFtQlRYSCI6bnVsbH0sImF1dGhvcml0aWVzIjpbIlJFQUQiLCJVUERBVEUiLCJERUxFVEUiLCJBRE1JTiIsIkNSRUFURSJdLCJqdGkiOiIzM2YyYzM2Ni05NmJjLTRkNzMtYmQxNi02OGQ1OGFjNzhhMDAiLCJjbGllbnRfaWQiOiJ3ZWJzaXRlIn0.aU38mExis2tCWVPJjKopMIv19DCSoB1XsXbxdLrrsRo"

    headers = {
        "Authorization": "Bearer {}".format(bearer_token),
        "Content-Type": "application/json"
    }

    response = requests.post(api_url, headers=headers)

    # Process the API response
    if response.status_code == 200:
        # Request successful
        response_data = response.json()
        # Process the response data
        print("Successfully posted data for uuid:"+uuid)
    else:
        # Request failed
        print("Failed to post data for uuid:"+uuid)
