from pymongo import MongoClient
from pymongo.errors import PyMongoError
from datetime import datetime, timedelta
import random
import pandas as pd
import json

# Setup connection
client = MongoClient('**********************************************************************************************')
# db = client['svcSysman']
db = client['svcHuman']
collection = db['user']

# Query the collection
# cursor = collection.find()
# cursor = collection.find({"username.value": {"$regex": r"\s+$"}})
cursor = collection.find({"account.username.value": {"$regex": r"\s+$"}})

rs = []

# Iterate over documents
# Iterate over documents
for document in cursor:
    try:
        updated_usernames = []
        username = document.get("account").get("username")[0].get("value")
        updated_usernames = username.strip()
        # print(updated_usernames)
        # for username in document.get("account").get("username):
        #     username['value'] = username['value'].strip()
            # updated_usernames.append(username)
            # print(updated_usernames)
            
        # Update the document
        # collection.update_one({'_id': document['_id']}, {'$set': {'username': updated_usernames}})
        collection.update_one({'_id': document['_id']}, {'$set': {'account.username.0.value': updated_usernames}})
        print(document['_id'])
        # if str(document['_id']) == '652e4e600ec8eb131c3c9ff9':
        #     collection.update_one({'_id': document['_id']}, {'$set': {'account.username.0.value': updated_usernames}})
        #     print(document['_id'])
        
    except PyMongoError as e:
        print(f"An error occurred: {e}")
        # Optionally, continue to the next iteration
        continue
    # Save result to file json
# with open('result-trim.json', 'w') as outfile:
#     json.dump(rs, outfile)


