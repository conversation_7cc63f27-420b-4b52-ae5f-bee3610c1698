import httpx
# Make an API call using the user data
api_url = "https://bhtncm.camau.gov.vn/sldtbxh/quan-ly-san-viec-lam/ql-bhtn/hs-huong-tctn/api/chuyen-tra-ket-qua-ho-so"
bearer_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsibGFib3JfZXhwb3J0Iiwic29jaWFsX3Byb3RlY3Rpb24iLCJlZHVjYXRpb24iLCJjaGlsZHJlbiIsInBheW1lbnQiLCJleGNoYW5nZV9mbG9vcl9tYW5hZ2VtZW50IiwiaW52ZW50b3J5IiwidXNlciIsIm1lcml0b3Jpb3VzX3BlcnNvbiIsImRydWciXSwidXNlcl9uYW1lIjoiY211X2FkbWluIiwic2NvcGUiOlsiUkVBRCIsIldSSVRFIl0sImV4cCI6MTczNzY1NzA1OCwidXNlckRldGFpbHMiOnsidGVuRGFuZ05oYXAiOiJjbXVfYWRtaW4iLCJtYU5ndW9pRHVuZyI6MzI1MiwiaG9UZW4iOiJBZG1pbiB04buJbmggQ01VIiwibWFUaW5oIjoiOTYiLCJtYUh1eWVuIjpudWxsLCJtYVhhIjpudWxsLCJtYVBob25nQmFuIjozMjQsImNhcEJhY0RvblZpIjpudWxsLCJtYURvblZpIjozMDAsInRoZW0iOnRydWUsInN1YSI6dHJ1ZSwieG9hIjp0cnVlLCJjYXBCYWNEViI6MSwidXVpZE5ndW9pRHVuZyI6ImFhMzhmYzI3LTNkZWMtMTFlYy1hMDI2LTAwNTA1Njg4NjNjMiIsImlzU3VwcGVyQWRtaW4iOjAsImlzQ29uZ0RhbiI6MCwiYXZ0IjpudWxsLCJtYVRydW5nVGFtQlRYSCI6bnVsbH0sImF1dGhvcml0aWVzIjpbIlJFQUQiLCJVUERBVEUiLCJERUxFVEUiLCJBRE1JTiIsIkNSRUFURSJdLCJqdGkiOiI3ZjkwMjFhYi1mYWRlLTQwZmItYjg1Zi04YWZiMTk0MDdmYTkiLCJjbGllbnRfaWQiOiJ3ZWJzaXRlIn0.AzMlLwa5VuCdXi1JOBIozaXYK05Clk-yvYQV4lPjfIs"

headers = {
        "Authorization": "Bearer {}".format(bearer_token),
        "Content-Type": "application/json"
}

body = {
        "uuidHsHuongTctn": str(uuid)
}

    # Create a custom SSL context
ssl_context = httpx.create_ssl_context()
    # Set stronger DH key length
ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    # Make the API call using httpx
with httpx.Client(verify=ssl_context) as client:
    response = client.post(api_url, json=body, headers=headers)

    # Process the API response
if response.status_code == 200:
        # Request successful
    response_data = response.json()
        # Process the response data
        print("Successfully posted data for uuid:"+uuid)
else:
        # Request failed
    print("Failed to post data for uuid:"+uuid)# Make an API call using the user data
api_url = "https://bhtncm.camau.gov.vn/sldtbxh/quan-ly-san-viec-lam/ql-bhtn/hs-huong-tctn/api/chuyen-tra-ket-qua-ho-so"
bearer_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsibGFib3JfZXhwb3J0Iiwic29jaWFsX3Byb3RlY3Rpb24iLCJlZHVjYXRpb24iLCJjaGlsZHJlbiIsInBheW1lbnQiLCJleGNoYW5nZV9mbG9vcl9tYW5hZ2VtZW50IiwiaW52ZW50b3J5IiwidXNlciIsIm1lcml0b3Jpb3VzX3BlcnNvbiIsImRydWciXSwidXNlcl9uYW1lIjoiY211X2FkbWluIiwic2NvcGUiOlsiUkVBRCIsIldSSVRFIl0sImV4cCI6MTczNzY1NzA1OCwidXNlckRldGFpbHMiOnsidGVuRGFuZ05oYXAiOiJjbXVfYWRtaW4iLCJtYU5ndW9pRHVuZyI6MzI1MiwiaG9UZW4iOiJBZG1pbiB04buJbmggQ01VIiwibWFUaW5oIjoiOTYiLCJtYUh1eWVuIjpudWxsLCJtYVhhIjpudWxsLCJtYVBob25nQmFuIjozMjQsImNhcEJhY0RvblZpIjpudWxsLCJtYURvblZpIjozMDAsInRoZW0iOnRydWUsInN1YSI6dHJ1ZSwieG9hIjp0cnVlLCJjYXBCYWNEViI6MSwidXVpZE5ndW9pRHVuZyI6ImFhMzhmYzI3LTNkZWMtMTFlYy1hMDI2LTAwNTA1Njg4NjNjMiIsImlzU3VwcGVyQWRtaW4iOjAsImlzQ29uZ0RhbiI6MCwiYXZ0IjpudWxsLCJtYVRydW5nVGFtQlRYSCI6bnVsbH0sImF1dGhvcml0aWVzIjpbIlJFQUQiLCJVUERBVEUiLCJERUxFVEUiLCJBRE1JTiIsIkNSRUFURSJdLCJqdGkiOiI3ZjkwMjFhYi1mYWRlLTQwZmItYjg1Zi04YWZiMTk0MDdmYTkiLCJjbGllbnRfaWQiOiJ3ZWJzaXRlIn0.AzMlLwa5VuCdXi1JOBIozaXYK05Clk-yvYQV4lPjfIs"

    headers = {
        "Authorization": "Bearer {}".format(bearer_token),
        "Content-Type": "application/json"
    }

    body = {
        "uuidHsHuongTctn": str(uuid)
    }

    # Create a custom SSL context
    ssl_context = httpx.create_ssl_context()
    # Set stronger DH key length
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    # Make the API call using httpx
    with httpx.Client(verify=ssl_context) as client:
        response = client.post(api_url, json=body, headers=headers)

    # Process the API response
    if response.status_code == 200:
        # Request successful
        response_data = response.json()
        # Process the response data
        print("Successfully posted data for uuid:"+uuid)
    else:
        # Request failed
        print("Failed to post data for uuid:"+uuid)
