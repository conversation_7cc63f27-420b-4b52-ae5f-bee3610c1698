[{"_id": {"$oid": "6573e3ce81532e4dbb2dd433"}, "code": "000.00.22.H12-230105-0024", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-05-16T04:01:20Z"}, "appliedDate": {"$date": "2023-05-16T23:14:33Z"}, "appointmentDate": {"$date": "2023-05-17T08:54:01Z"}, "completedDate": {"$date": "2023-05-16T16:24:31Z"}, "returnedDate": {"$date": "2023-05-17T01:38:30Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd434"}, "code": "000.00.22.H12-230111-0001", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-05-16T06:25:30Z"}, "appliedDate": {"$date": "2023-05-16T19:58:24Z"}, "appointmentDate": {"$date": "2023-05-25T04:46:45Z"}, "completedDate": {"$date": "2023-05-24T21:29:28Z"}, "returnedDate": {"$date": "2023-05-25T05:34:36Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd435"}, "code": "000.00.22.H12-230131-0010", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-09-11T10:31:49Z"}, "appliedDate": {"$date": "2023-09-11T23:57:06Z"}, "appointmentDate": {"$date": "2023-10-03T06:46:00Z"}, "completedDate": {"$date": "2023-10-02T18:32:33Z"}, "returnedDate": {"$date": "2023-10-03T05:31:23Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd436"}, "code": "000.00.22.H12-230203-0024", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-05-18T09:18:37Z"}, "appliedDate": {"$date": "2023-05-18T10:58:00Z"}, "appointmentDate": {"$date": "2023-05-29T13:57:05Z"}, "completedDate": {"$date": "2023-05-28T14:53:59Z"}, "returnedDate": {"$date": "2023-05-29T15:34:45Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd437"}, "code": "000.00.22.H12-230510-0011", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-05-10T03:09:13Z"}, "appliedDate": {"$date": "2023-05-10T04:24:37Z"}, "appointmentDate": {"$date": "2023-05-18T05:02:05Z"}, "completedDate": {"$date": "2023-05-17T18:22:39Z"}, "returnedDate": {"$date": "2023-05-18T07:13:58Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd438"}, "code": "000.00.22.H12-230510-0042", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-05-10T17:56:53Z"}, "appliedDate": {"$date": "2023-05-10T01:18:25Z"}, "appointmentDate": {"$date": "2023-05-18T19:55:25Z"}, "completedDate": {"$date": "2023-05-17T02:42:08Z"}, "returnedDate": {"$date": "2023-05-18T11:42:02Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd439"}, "code": "000.00.22.H12-230705-0011", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-07-05T03:09:30Z"}, "appliedDate": {"$date": "2023-07-05T03:39:16Z"}, "appointmentDate": {"$date": "2023-07-18T14:41:15Z"}, "completedDate": {"$date": "2023-07-17T20:03:27Z"}, "returnedDate": {"$date": "2023-07-18T12:01:40Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd43a"}, "code": "000.00.22.H12-230814-0004", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-08-14T01:08:16Z"}, "appliedDate": {"$date": "2023-08-14T18:59:47Z"}, "appointmentDate": {"$date": "2023-08-31T09:49:53Z"}, "completedDate": {"$date": "2023-08-30T21:41:10Z"}, "returnedDate": {"$date": "2023-08-31T21:36:12Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd43b"}, "code": "000.00.22.H12-230815-0002", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-08-15T00:36:52Z"}, "appliedDate": {"$date": "2023-08-15T05:53:12Z"}, "appointmentDate": {"$date": "2023-09-05T02:08:31Z"}, "completedDate": {"$date": "2023-09-04T08:05:48Z"}, "returnedDate": {"$date": "2023-09-05T02:53:00Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd43c"}, "code": "000.00.22.H12-230822-0038", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-08-22T04:09:21Z"}, "appliedDate": {"$date": "2023-08-22T00:05:31Z"}, "appointmentDate": {"$date": "2023-09-22T19:05:40Z"}, "completedDate": {"$date": "2023-09-21T14:26:38Z"}, "returnedDate": {"$date": "2023-09-22T07:43:05Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd43d"}, "code": "000.00.22.H12-230824-0017", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-08-24T14:06:56Z"}, "appliedDate": {"$date": "2023-08-24T04:01:41Z"}, "appointmentDate": {"$date": "2023-09-05T03:13:49Z"}, "completedDate": {"$date": "2023-09-04T19:51:55Z"}, "returnedDate": {"$date": "2023-09-05T15:57:41Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd43e"}, "code": "000.00.22.H12-230825-0018", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-08-25T15:02:37Z"}, "appliedDate": {"$date": "2023-08-25T21:12:49Z"}, "appointmentDate": {"$date": "2023-09-11T15:44:14Z"}, "completedDate": {"$date": "2023-09-10T03:48:20Z"}, "returnedDate": {"$date": "2023-09-11T20:33:46Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd43f"}, "code": "000.00.22.H12-230825-0027", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-08-25T17:16:47Z"}, "appliedDate": {"$date": "2023-08-25T22:02:33Z"}, "appointmentDate": {"$date": "2023-09-06T10:01:47Z"}, "completedDate": {"$date": "2023-09-05T01:11:50Z"}, "returnedDate": {"$date": "2023-09-06T04:25:13Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd440"}, "code": "000.00.22.H12-230830-0010", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-08-30T17:55:15Z"}, "appliedDate": {"$date": "2023-08-30T03:54:38Z"}, "appointmentDate": {"$date": "2023-09-11T08:50:24Z"}, "completedDate": {"$date": "2023-09-10T17:19:59Z"}, "returnedDate": {"$date": "2023-09-11T15:58:00Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd441"}, "code": "000.00.22.H12-230919-0012", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-09-19T15:58:49Z"}, "appliedDate": {"$date": "2023-09-19T05:31:54Z"}, "appointmentDate": {"$date": "2023-09-27T13:00:49Z"}, "completedDate": {"$date": "2023-09-26T08:09:50Z"}, "returnedDate": {"$date": "2023-09-27T21:04:08Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd442"}, "code": "000.00.22.H12-230919-0027", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-09-19T08:53:42Z"}, "appliedDate": {"$date": "2023-09-19T13:48:28Z"}, "appointmentDate": {"$date": "2023-10-02T05:42:25Z"}, "completedDate": {"$date": "2023-10-01T22:33:12Z"}, "returnedDate": {"$date": "2023-10-02T20:29:09Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}, {"_id": {"$oid": "6573e3ce81532e4dbb2dd443"}, "code": "000.00.22.H12-230920-0010", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-09-20T09:35:56Z"}, "appliedDate": {"$date": "2023-09-20T10:34:32Z"}, "appointmentDate": {"$date": "2023-10-03T22:30:46Z"}, "completedDate": {"$date": "2023-10-02T23:42:54Z"}, "returnedDate": {"$date": "2023-10-03T04:37:20Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}]