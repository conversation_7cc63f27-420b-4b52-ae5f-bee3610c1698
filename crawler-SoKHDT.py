import ssl
import httpx
import json
import datetime
import asyncio
import time

# Define the URL and headers for the API request
url = "https://ketnoi.dichvucongcamau.gov.vn/integration/lgsp-cmu/--get-ent-dossiers-cmu"
headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "vi",
    "Authorization": "Bearer *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "Connection": "keep-alive",
    "Content-Type": "application/json",
    "Origin": "https://motcua.dichvucongcamau.gov.vn",
    "Referer": "https://motcua.dichvucongcamau.gov.vn/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0",
    "sec-ch-ua": '"Chromium";v="130", "Microsoft Edge";v="130", "Not?A_Brand";v="99"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"'
}

# Function to make API calls with retry on server error
async def get_api_data(client, params):
    while True:
        try:
            response = await client.get(url, headers=headers, params=params,timeout=90.0)
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 500 or response.status_code == 417:
                print("Server error (500). Retrying...")
                await asyncio.sleep(1)  # wait briefly before retrying
            else:
                print(f"Request failed with status code: {response.status_code}")
                return None
        except Exception as e:
            #retry on connection error
            print(f"Request failed with exception: {e}")
            await asyncio.sleep(1)  # wait briefly before retrying

# Main function to loop through dates and make API requests
async def fetch_data():
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        # Loop through each month from January to September (1 to 9)
        for month in range(10, 11):
            all_content = []  # Reset content list for each month

            # Define the start and end of the month
            start_date = datetime.datetime(2024, month, 1)
            end_date = (start_date.replace(month=month % 12 + 1, day=1) - datetime.timedelta(days=1)) if month < 12 else datetime.datetime(2024, 12, 31)

            # Loop over each 3-day range within the month
            current_date = start_date
            while current_date <= end_date:
                from_date = current_date.strftime("%Y/%m/%d")
                to_date = (current_date + datetime.timedelta(days=1)).strftime("%Y/%m/%d")

                # Adjust `to_date` if it exceeds the end of the month
                if datetime.datetime.strptime(to_date, "%Y/%m/%d") > end_date:
                    to_date = end_date.strftime("%Y/%m/%d")

                # Pagination parameters
                params = {
                    "page": 0,
                    "size": 20,
                    "spec": "page",
                    "fromDate": from_date,
                    "toDate": to_date,
                    "config-id": "65f25877efb7c043e258af54"
                }

                print(f"Fetching data from {from_date} to {to_date}...")

                # Loop to handle pagination within the 3-day range
                while True:
                    # Get data from API with retry on server error
                    data = await get_api_data(client, params)
                    if data is None:
                        break

                    # Append content from response
                    all_content.extend(data.get("content", []))

                    # Check if it's the last page
                    if data["last"]:
                        break
                    
                    # Move to next page
                    params["page"] += 1

                # Move to the next 3-day window
                current_date += datetime.timedelta(days=2)

            # Save the data for the month to a JSON file
            output_file = f"enterprise_{start_date.strftime('%Y_%m')}.json"
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(all_content, f, ensure_ascii=False, indent=4)
            
            print(f"Data fo22r month {month} saved to {output_file}")

# Run the main function
asyncio.run(fetch_data())
