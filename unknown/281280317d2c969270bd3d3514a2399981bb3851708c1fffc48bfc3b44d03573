import json
import subprocess
import logging
import time
import argparse
from typing import List, Dict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sync_process.log'),
        logging.StreamHandler()
    ]
)

class CurlClient:
    def __init__(self):
        self.base_url = "http://192.168.50.81:8081/XrdAdapter/RestService/forward/mapi"
        self.ma_don_vi = "000.00.00.H12"
        self.headers = "-H 'Content-Type: application/json' -H 'Charset: utf-8' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'"
        self.max_retries = 3
        self.retry_delay = 2  # seconds
        
    def run_curl(self, curl_command: str, retry_count: int = 0) -> Dict:
        """Execute curl command with retry mechanism"""
        try:
            process = subprocess.Popen(
                curl_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True
            )
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                if retry_count < self.max_retries:
                    logging.warning(f"Retry attempt {retry_count + 1} for curl command")
                    time.sleep(self.retry_delay)
                    return self.run_curl(curl_command, retry_count + 1)
                logging.error(f"Curl error after {self.max_retries} retries: {stderr.decode()}")
                return {"error_code": -1}
            
            return json.loads(stdout.decode('utf-8'))
        except Exception as e:
            if retry_count < self.max_retries:
                logging.warning(f"Retry attempt {retry_count + 1} due to error: {str(e)}")
                time.sleep(self.retry_delay)
                return self.run_curl(curl_command, retry_count + 1)
            logging.error(f"Error executing curl after {self.max_retries} retries: {str(e)}")
            return {"error_code": -1}

    def get_session(self) -> str:
        """Get session token"""
        curl_command = f"""
        curl -d '{{"username":"{self.ma_don_vi}","password":"{self.ma_don_vi}@123456"}}' \
        {self.headers} {self.base_url}/login
        """
        response = self.run_curl(curl_command)
        return response.get('session', '')
    
    def update_record(self, record: Dict, session: str) -> Dict:
        """Update single record"""
        payload = {
            "madonvi": self.ma_don_vi,
            "data": [record],  # Wrap single record in list
            "session": session,
            "service": "CapNhatTienDoHoSoMC"
        }
        curl_command = f"""
        curl -X POST {self.headers} -d '{json.dumps(payload)}' {self.base_url}/g
        """
        return self.run_curl(curl_command)

def process_json_file(file_path: str) -> List[Dict]:
    """Đọc dữ liệu từ file JSON"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
            if not isinstance(data, list):
                logging.error("File JSON phải chứa một array của các object")
                return []
            return data
    except FileNotFoundError:
        logging.error(f"Không tìm thấy file: {file_path}")
        return []
    except json.JSONDecodeError:
        logging.error(f"File không đúng định dạng JSON: {file_path}")
        return []
    except Exception as e:
        logging.error(f"Lỗi khi đọc file: {str(e)}")
        return []

def main(args):
    """
    Hàm chính để xử lý và update từng record
    """
    # Khởi tạo client
    client = CurlClient()
    
    # Lấy session token
    session = client.get_session()
    if not session:
        logging.error("Không thể lấy session token")
        return
    
    # Đọc dữ liệu
    data = process_json_file(args.file)
    if not data:
        logging.error("Không có dữ liệu để xử lý")
        return
    
    total_records = len(data)
    logging.info(f"Tổng số record cần xử lý: {total_records}")
    
    # Xử lý từng record
    success_count = 0
    error_count = 0
    
    for idx, record in enumerate(data, 1):
        try:
            result = client.update_record(record, session)
            if result.get('error_code', -1) == -1:
                error_count += 1
                logging.error(f"Lỗi khi xử lý record {idx}/{total_records}")
            else:
                success_count += 1
                logging.info(f"Xử lý thành công record {idx}/{total_records}")
        except Exception as e:
            error_count += 1
            logging.error(f"Lỗi khi xử lý record {idx}/{total_records}: {str(e)}")
    
    # Báo cáo kết quả
    logging.info(f"""
    Kết quả xử lý:
    - Tổng số record: {total_records}
    - Thành công: {success_count}
    - Thất bại: {error_count}
    """)

if __name__ == "__main__":
    # Tạo parser để nhận tham số từ command line
    parser = argparse.ArgumentParser(description='Process JSON file and update records')
    parser.add_argument('file', help='Đường dẫn tới file JSON')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Chạy xử lý
    main(args)