import tkinter as tk
from tkinter import ttk, messagebox
import asyncio
from threading import Thread
from datetime import datetime, timezone
import pymongo
import httpx
import json
from bson import ObjectId
from openpyxl import Workbook

# Kết nối tới MongoDB
mongo_uri = '*********************************************************************************************************************************'
client = pymongo.MongoClient(mongo_uri)

# Lấy access token từ API
async def get_token():
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = 'grant_type=password&username=admin&password=Cmu#0801&client_id=web-onegate'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    try:
        async with httpx.AsyncClient(verify=ssl_context) as client:
            response = await client.post(url, data=payload, headers=headers)
            return response.json()['access_token']
    except Exception as e:
        print(f"Error getting token: {e}")
        return None

# Hàm đồng bộ hóa dữ liệu
async def sync_qg(code, token):
    url = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    }
    payload = json.dumps([code])
    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    try:
        async with httpx.AsyncClient(verify=ssl_context) as client:
            response = await client.post(url, data=payload, headers=headers)
            print(response.text.encode('utf8'))
            return response.status_code == 200 and response.json()['affectedRows'] == 1
    except Exception as e:
        print(f"Error syncing dossier {code}: {e}")
        return False

# Truy vấn database
def get_documents(fromDate, toDate):
    database_name = 'svcPadman'
    collection_name = 'dossier'
    database = client[database_name]
    collection = database[collection_name]
    dayfrom = int(fromDate.split("/")[0])
    monthfrom = int(fromDate.split("/")[1])
    yearfrom = int(fromDate.split("/")[2])

    dayto = int(toDate.split("/")[0])
    monthto = int(toDate.split("/")[1])
    yearto = int(toDate.split("/")[2])

    start_date = datetime(yearfrom,monthfrom,dayfrom, 0, 0, 0, tzinfo=timezone.utc)
    end_date = datetime(yearto, monthto, dayto, 23, 59, 59, tzinfo=timezone.utc)
    try:
        documents = collection.find({
            "$and": [
                {"acceptedDate": {"$gte": start_date, "$lte": end_date}},
                {'procedure.code': {"$nin": ["13709","59214","7702","HUY.1.004203.000.00.00.H12","1.004227.000.00.00.H12.HUY","HUY.2.000889.000.00.00.H12","2.000488.000.00.00.H12","LVNGOAIVU81","LVNGOAIV7","STN-DDBD-01","CMU-291172","CMU-01","CMU-02","CMU-03","CMU-04","CMU-05","CMU-06"]}}
            ]
        }, no_cursor_timeout=True).limit(100000)
        return list(documents)
    except Exception as e:
        print(f"Error retrieving documents: {e}")
        return []

# Cập nhật file
def update_file(dossier_id):
    database_name = 'svcPadman'
    collection_name = 'dossierFormFile'
    database = client[database_name]
    collection = database[collection_name]
    try:
        result = collection.update_many(
            {"dossier.id": ObjectId(dossier_id)},
            {"$set": {"file.$[].reused": 1}}
        )
        print(f"Updated {result.modified_count} documents for dossier {dossier_id}")
        return result.modified_count > 0
    except Exception as e:
        print(f"Error updating file for dossier {dossier_id}: {e}")
        return False

# Giao diện người dùng và xử lý
def run_async():
    asyncio.run(main(progress, status_label, start_time, from_date_entry.get(),to_date_entry.get()))

def start_processing():
    global start_time
    start_time = datetime.now()
    Thread(target=run_async).start()

async def main(progress, status_label, start_time, from_date, to_date):
    token = await get_token()
    if token is None:
        messagebox.showerror("Error", "Failed to get token. Please check logs.")
        return
    print(f"from_date: {from_date}")
    print(f"to_date: {to_date}")
    documents = get_documents(from_date,to_date)
    if not documents:
        messagebox.showerror("Error", "No documents found or failed to retrieve documents.")
        return
    wb = Workbook()
    ws = wb.active
    ws.append(["code", "status"])
    for i, document in enumerate(documents):
        code = document['code']
        ws.append([code])
        dossier_id = document['_id']
        try:
            if update_file(dossier_id):
                if await sync_qg(code, token):
                    ws.cell(row=i+2, column=2, value="success")
                else:
                    ws.cell(row=i+2, column=2, value="fail")
                ws.cell(row=i+2, column=2, value="success")
            else:
                ws.cell(row=i+2, column=2, value="update failed")
        except Exception as e:
            print(f"Error processing dossier {code}: {e}")
            ws.cell(row=i+2, column=2, value="error")
        progress['value'] = (i + 1) / len(documents) * 100
        status_label.config(text=f"Đã xử lý {i+1}/{len(documents)} hồ sơ")
        root.update()
    filename = "_".join(from_date.split("/")).join(to_date.split("/")) + ".xlsx"
    wb.save(filename)
    messagebox.showinfo("Xử lý xong", f"Danh sách hồ sơ xử lý lưu vào file {filename}")

root = tk.Tk()
root.title("Tool hỗ trợ biểu đồ 766")

#set window size HD
root.geometry("400x300")


# From Date input
from_date_label = ttk.Label(root, text="Nhâp từ ngày (dd/mm/yyyy):")
from_date_label.pack(pady=10)
from_date_entry = ttk.Entry(root)
from_date_entry.pack(pady=10)

# To Date input
to_date_label = ttk.Label(root, text="Nhâp đến ngày (dd/mm/yyyy):")
to_date_label.pack(pady=10)
to_date_entry = ttk.Entry(root)
to_date_entry.pack(pady=10)

status_label = ttk.Label(root, text="Trạng thái xử lý:")
status_label.pack(pady=10)

start_button = ttk.Button(root, text="Bắt đầu xử lý...", command=start_processing)
start_button.pack(pady=20)

progress = ttk.Progressbar(root, length=300, mode='determinate')
progress.pack(pady=20)

root.mainloop()
