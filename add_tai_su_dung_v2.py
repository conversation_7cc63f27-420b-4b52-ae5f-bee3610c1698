import asyncio
import json
import pymongo
import traceback
from datetime import datetime, timezone
from bson import ObjectId
import httpx
from openpyxl import Workbook

# Connect to MongoDB
mongo_uri = '**********************************************************************************************'
client = pymongo.MongoClient(mongo_uri)

async def getToken():
    # from api
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = 'grant_type=password&username=admin&password=Cmu#0801&client_id=web-onegate'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }


    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.post(url, data=payload, headers=headers)
        access_token = response.json()['access_token']
    return access_token

async def syncQG(code):
    url = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"
    token = await getToken()
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer '+token,
        'Cookie': 'JSESSIONID=979D1F437D51C126FF15E5BA8BE0E768'
    }

    payload = json.dumps([code])

    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.post(url, data=payload, headers=headers)
        if response.status_code == 200:
            if response.json()['affectedRows'] == 1:
                return 200
            else:
                return 500
        else:
            return 501

# Function query the database and return a list of documents
def get_documents():
    database_name = 'svcPadman'  # Replace with your database name
    collection_name = 'dossier'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    # Query the database
    start_date = datetime(2024, 4, 17, 0, 0, 0, tzinfo=timezone.utc)
    end_date = datetime(2024, 4, 22, 23, 59, 59, tzinfo=timezone.utc)

    with client.start_session() as session:
        documents = collection.find({
            "$and": [
                {"acceptedDate": {
                    "$gte": start_date,
                    "$lte": end_date
                }},
                {'procedure.code': { "$nin": ["13709","59214","7702","HUY.1.004203.000.00.00.H12","1.004227.000.00.00.H12.HUY","HUY.2.000889.000.00.00.H12","2.000488.000.00.00.H12","LVNGOAIVU81","LVNGOAIV7","STN-DDBD-01","CMU-291172","CMU-01","CMU-02"]}}
            ]
        }, no_cursor_timeout=True, session=session).limit(100000)
        return list(documents)

def update_file(dossier_id):
    database_name = 'svcPadman'  # Replace with your database name
    collection_name = 'dossierFormFile'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    rs = "ok"
    dossier_id = ObjectId(dossier_id)
   
    result = collection.update_many(
        {"dossier.id": dossier_id},
        {"$set": {"file.$[].reused": 1}}
    )
    if result.modified_count > 0:
        print(f"Document with ID {dossier_id} was updated.")
    else:
        print(f"Document with ID {dossier_id} was not updated.")
        rs = "fail"
    return rs

async def main():
    count = 0
    try:
        # Get the list of documents with no_cursor_timeout
        documents = get_documents()

        # Create a new Excel workbook
        wb = Workbook()
        ws = wb.active
        ws.append(["Code"])  # Add a header row

        for document in documents:
            try:
                code = document['code']
                ws.append([code])  # Append code to the Excel sheet
                print(f"Processing dossier with code: {code}")
                dossier_id = document['_id']
                rs = update_file(dossier_id)
                if rs == "ok":
                    count += 1
                else:
                    print(f"Failed to update file for dossier ID: {dossier_id}")
                    continue
            except Exception as doc_exception:
                print(f"Failed to process document: {doc_exception}")
                traceback.print_exc()
                # Bạn có thể đặt mã xử lý khác tại đây nếu cần
                
    except Exception as e:
        print(f"Failed to get documents: {e}")
        traceback.print_exc()
        # Bạn có thể xử lý ngoại lệ chung cho vòng lặp ở đây nếu cần
    finally:
        # Save the workbook to a file
        wb.save("excel/thang041.xlsx")
        print(f"Processed {count} documents.")

        

# Run the main function
asyncio.run(main())
