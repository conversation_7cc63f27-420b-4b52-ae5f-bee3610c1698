import ssl
context = ssl.create_default_context()
context.check_hostname = False
context.verify_mode = ssl.CERT_NONE
context.set_ciphers('DEFAULT@SECLEVEL=1')  # Giảm security level để test
import socket
import OpenSSL
from datetime import datetime
from cryptography import x509
from cryptography.hazmat.backends import default_backend
import idna

def check_wildcard_ssl(domain, port=443):
    """
    Kiểm tra wildcard SSL certificate
    """
    try:
        # Chuyển đổi domain sang dạng IDN nếu cần
        encoded_domain = idna.encode(domain.replace('*.', 'test.'))
        
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE

        with socket.create_connection((encoded_domain, port)) as sock:
            with context.wrap_socket(sock, server_hostname=domain.replace('*.', 'test.')) as ssock:
                cert_der = ssock.getpeercert(binary_form=True)
                cert_pem = ssl.DER_cert_to_PEM_cert(cert_der)
                cert = OpenSSL.crypto.load_certificate(OpenSSL.crypto.FILETYPE_PEM, cert_pem)
                
                results = {
                    "status": "Valid",
                    "domain": domain,
                    "issues": [],
                    "warnings": [],
                    "details": {}
                }
                
                # Kiểm tra wildcards
                san = []
                for i in range(cert.get_extension_count()):
                    ext = cert.get_extension(i)
                    if ext.get_short_name() == b'subjectAltName':
                        san = str(ext).replace('DNS:', '').split(', ')

                results["details"]["subject_alt_names"] = san
                
                # Kiểm tra wildcard coverage
                wildcard_valid = False
                base_domain = domain.replace('*.', '')
                for name in san:
                    if name.startswith('*.') and name.endswith(base_domain):
                        wildcard_valid = True
                        break
                
                if not wildcard_valid:
                    results["issues"].append("Wildcard certificate không khớp với domain")
                
                # Kiểm tra thời hạn
                not_before = datetime.strptime(cert.get_notBefore().decode(), '%Y%m%d%H%M%SZ')
                not_after = datetime.strptime(cert.get_notAfter().decode(), '%Y%m%d%H%M%SZ')
                now = datetime.utcnow()
                
                results["details"].update({
                    "valid_from": not_before.strftime('%Y-%m-%d'),
                    "valid_until": not_after.strftime('%Y-%m-%d'),
                    "issuer": dict(cert.get_issuer().get_components()),
                    "version": cert.get_version() + 1,
                    "serial_number": hex(cert.get_serial_number()),
                    "signature_algorithm": cert.get_signature_algorithm().decode()
                })
                
                if now < not_before:
                    results["issues"].append("Certificate chưa có hiệu lực")
                elif now > not_after:
                    results["issues"].append("Certificate đã hết hạn")
                elif (not_after - now).days < 30:
                    results["warnings"].append(f"Certificate sẽ hết hạn trong {(not_after - now).days} ngày")
                
                # Kiểm tra chuỗi chứng chỉ
                try:
                    cert_chain = get_certificate_chain(cert)
                    results["details"]["chain_length"] = len(cert_chain)
                    if len(cert_chain) < 2:
                        results["issues"].append("Thiếu intermediate certificates")
                except Exception as e:
                    results["issues"].append(f"Lỗi khi kiểm tra chuỗi chứng chỉ: {str(e)}")
                
                # Kiểm tra key usage và extended key usage
                for i in range(cert.get_extension_count()):
                    ext = cert.get_extension(i)
                    if ext.get_short_name() == b'keyUsage':
                        results["details"]["key_usage"] = str(ext)
                    elif ext.get_short_name() == b'extendedKeyUsage':
                        results["details"]["extended_key_usage"] = str(ext)
                
                if not results["issues"]:
                    results["status"] = "Valid"
                else:
                    results["status"] = "Invalid"
                
                return results

    except Exception as e:
        return {
            "status": "Error",
            "domain": domain,
            "error": str(e),
            "issues": ["Không thể kết nối hoặc xác thực certificate"]
        }

def get_certificate_chain(cert):
    """
    Lấy chuỗi chứng chỉ
    """
    chain = [cert]
    # TODO: Implement full chain verification
    return chain

def print_wildcard_results(results):
    """
    In kết quả kiểm tra
    """
    print("\nKết quả kiểm tra Wildcard SSL Certificate:")
    print("-" * 60)
    print(f"Domain: {results['domain']}")
    print(f"Trạng thái: {results['status']}")
    
    if "error" in results:
        print(f"\nLỗi: {results['error']}")
        return
    
    if results["issues"]:
        print("\nVấn đề phát hiện được:")
        for issue in results["issues"]:
            print(f"- {issue}")
    
    if results["warnings"]:
        print("\nCảnh báo:")
        for warning in results["warnings"]:
            print(f"- {warning}")
    
    print("\nChi tiết certificate:")
    details = results["details"]
    print(f"- Hiệu lực từ: {details['valid_from']}")
    print(f"- Hiệu lực đến: {details['valid_until']}")
    print(f"- Nhà phát hành: {details['issuer']}")
    print(f"- Phiên bản: {details['version']}")
    print(f"- Serial Number: {details['serial_number']}")
    print(f"- Thuật toán chữ ký: {details['signature_algorithm']}")
    print("\nSubject Alternative Names:")
    for san in details["subject_alt_names"]:
        print(f"- {san}")

# Chạy kiểm tra
results = check_wildcard_ssl("ketnoi.dichvucongcamau.gov.vn")
print_wildcard_results(results)