import openpyxl
import json
from bson import ObjectId  # Import ObjectId from pymongo

# Đ<PERSON><PERSON> dữ liệu từ tệp Excel
def read_excel(file_path):
    wb = openpyxl.load_workbook(file_path)
    sheet = wb.active
    data = []

    for row in sheet.iter_rows(values_only=True):
        data.append(row)

    return data

# Tạo đối tượng JSON mới với trường "_id" mới và thay đổi trường "procedure.id"
def generate_new_ids_and_replace_procedure_id(json_array, excel_data):
    for item in json_array:
        item["_id"] = {"$oid": str(ObjectId())}  # Chuyển ObjectId thành chuỗi và cập nhật "_id"

        # Thay đổi trường "procedure.id"
        for row in excel_data[1:]:
            code_a = row[0]  # G<PERSON><PERSON> sử cột A là cột mã
            code_b = row[6]  # <PERSON><PERSON><PERSON> sử cột B là cột cần thay thế
            if code_a == item["procedure"]["id"]["$oid"]:
                item["procedure"]["id"]["$oid"] = code_b

if __name__ == "__main__":
    excel_file_path = "ds_thu_tuc.xlsx"  # Đường dẫn đến tệp Excel chứa dữ liệu

    with open("damdoi.json", 'r') as template_file:
        template_data = json.load(template_file)

    excel_data = read_excel(excel_file_path)

    generate_new_ids_and_replace_procedure_id(template_data, excel_data)

    with open("tvt.json", 'w', encoding='utf-8') as output_file:
        json.dump(template_data, output_file, ensure_ascii=False, indent=4)
