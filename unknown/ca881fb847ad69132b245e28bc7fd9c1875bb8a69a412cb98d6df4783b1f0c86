from pymongo import MongoClient
import pandas as pd

# Kết nối tới MongoDB
client = MongoClient("**********************************************************************************************")  # Thay thế bằng chuỗi kết nối của bạn
db = client["svcPadman"]  # Thay thế bằng tên database của bạn
collection = db["dossier"]

# Truy vấn MongoDB
pipeline = [
    {
        "$match": {
            "applyMethod.name": "Trực tuyến",
            "createdDate": {
                "$gte": pd.to_datetime("2023-01-01T00:00:00.000Z"),
                "$lt": pd.to_datetime("2024-01-01T00:00:00.000Z")
            },
            "dossierStatus.name": {
                "$elemMatch": {
                    "languageId": 228,
                    "name": {
                        "$in": ["<PERSON><PERSON> kết quả", "<PERSON><PERSON> trả kết quả"]
                    }
                }
            }
        }
    },
    {
        "$sort": {
            "createdDate": 1
        }
    },
    {
        "$group": {
            "_id": "$procedure.id",
            "procedure": {"$first": "$procedure"},
            "dossier": {"$first": "$$ROOT"}
        }
    },
    {
        "$project": {
            "_id": 0,
            "procedure": 1,
            "dossier": 1
        }
    }
]

# Sử dụng allowDiskUse=True để cho phép sử dụng ổ đĩa
results = list(collection.aggregate(pipeline, allowDiskUse=True))

# Chuyển đổi kết quả sang DataFrame
data = []
for result in results:
    row = {
        "Mã thủ tục": result["procedure"]["code"],
        "Tên thủ tục": result["procedure"]["translate"][0]["name"],
        "Mã hồ sơ": result["dossier"]["code"],
    }
    data.append(row)

df = pd.DataFrame(data)

# Xuất DataFrame ra file Excel
df.to_excel("thong_ke_thu_tuc_2023.xlsx", index=False)

print("Exported successfully to dossiers_2023.xlsx")
