from pymongo import MongoClient
from bson.objectid import ObjectId

# Replace with your actual MongoDB connection string and database name
mongo_url = "**********************************************************************************************"
database_name = "svcBpm"

# Establishing a connection to the database
client = MongoClient(mongo_url)
db = client[database_name]

# Defining the query and update
query = {"isFirst": 1}
update = {"$set": {"dynamicVariable.canUseDigitalSignNEAC": 1}}

# Fetching the documents and updating them
collection = db['processDefinitionTask']
documents = collection.find(query)
count = 0
# id = ObjectId("64ae0b48b5e5620e1db1818d")
for doc in documents:
    collection.update_one({'_id': doc['_id']}, update)
    print(doc['_id'])
    count += 1
print("Documents updated successfully "+str(count))
# Closing the database connection
client.close()
