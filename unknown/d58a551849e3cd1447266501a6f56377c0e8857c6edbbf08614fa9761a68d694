import json
import random
import subprocess
from datetime import datetime, timedelta


def adjust_date(date_str):
    date_format = "%Y%m%d%H%M%S"
    input_date = datetime.strptime(date_str, date_format)
    current_date = datetime.now()

    # So sánh ngày đầu vào với ngày hiện tại
    if input_date > current_date:
        # <PERSON><PERSON><PERSON> lớn hơn, tr<PERSON> về ngày hiện tại
        return current_date.strftime(date_format)
    else:
        # Nếu nhỏ hơn, trừ đi 1 ngày
        adjusted_date = input_date - timedelta(days=1)
        return adjusted_date.strftime(date_format)

def run_curl_command(curl_command):
    """ Chạy lệnh curl và trả về kết quả. """
    process = subprocess.Popen(curl_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        print(f"Error: {stderr}")
        return None
    else:
        return stdout.decode('utf-8')
    

def get_session():
    # Lệnh curl đầu tiên
    curl_command_1 = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/login
    """

    # Chạy lệnh curl đầu tiên và lưu response
    response_1 = run_curl_command(curl_command_1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session

# Lệnh curl thứ hai, sử dụng dữ liệu quan trọng từ response đầu tiên

def getTrangThaiHoSoByTenTrangThai(ten_trang_thai):
    arr_trang_thai = {
        "Mới đăng ký": "1",
        "Được tiếp nhận": "2",
        "Không được tiếp nhận": "3",
        "Đang xử lý": "4",
        "Yêu cầu bổ sung": "5",
        "Yêu cầu thực hiện nghĩa vụ tài chính": "6",
        "Cồng dân yêu cầu rút hồ sơ": "7",
        "Dừng xử lý": "8",
        "Đã xử lý xong": "9",
        "Đã trả kết quả": "10",
    }
    return arr_trang_thai[ten_trang_thai]


def tracuuhoso(masohoso,session):
    curl_command_2 = f"""
    curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{{"madonvi": "000.00.00.H12","session":"{session}", "service": "TraCuuHoSo", "mahoso": "{masohoso}"}}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g
    """

    # Kết quả từ API đầu tiên
    response_1 = run_curl_command(curl_command_2)

    return response_1

def tracuuhosoV1(masohoso):
    curl_command_3 = f"""
    curl -X GET "http://192.168.50.79/api/hoso/tracuuhosoV1?mahoso={masohoso}" -H "Accept: application/json"
    """

    # Kết quả từ API thứ hai
    response_2 = run_curl_command(curl_command_3)
    return response_2

def tracuuhosoV2(masohoso, cookie):
    curl_command_3 = f"""
    curl -X POST "https://quantri.dichvucong.gov.vn/web/jsp/submission/search-submission.jsp" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -b "TS01c03a4c={cookie}" \
     -d "testing_submission={masohoso}"
    """

    # Kết quả từ API thứ hai
    response_2 = run_curl_command(curl_command_3)
    return response_2


def main():
    session = get_session()
    cookie = "01f551f5ee5b4e3bfdb3a55c6c23a4c247eb72c6139d5017144e5fb0892857a523b47f9f019ab06d7287357ec5a45d0b6b42e2e603; JSESSIONID=C5C6D9E745B126FDDC2F0FE4B2D89F37; TS01744849=01f551f5ee5b4e3bfdb3a55c6c23a4c247eb72c6139d5017144e5fb0892857a523b47f9f019ab06d7287357ec5a45d0b6b42e2e603; route=1702868038.146.832.554000; route=1702868038.245.832.300143; TS0115bee1=01f551f5ee0532a158939bbb3d94b0eee891ea406db7877fd3c7a4d3cb925cb5ac061a487f362d70b5ea0cae0bcd023b8f42a05c6"
    prefix = "096"
    suffix = ''.join(random.choices('0123456789', k=7))
    SoDinhDanh =  prefix + suffix
    arr_ma_ho_so = [
        "000.15.25.H12-230915-0002",
        "000.00.25.H12-231025-0014",
        "000.00.25.H12-231002-0036",
        "000.00.25.H12-231002-0017",
        "000.00.25.H12-230905-0012",
        "000.00.25.H12-230831-0006",
        "000.00.25.H12-230831-0008",
        "000.00.25.H12-230831-0028",
        "000.00.25.H12-230831-0005",
        "000.00.25.H12-230818-0051",
        "000.00.25.H12-230809-0058",
        "000.00.25.H12-230807-0008",
        "000.00.25.H12-230807-0005",
        "000.00.25.H12-230803-0029",
        "000.00.25.H12-230803-0015",
        "000.00.25.H12-230803-0014",
        "000.00.25.H12-230713-0007",
        "000.00.25.H12-230621-0028",
        "000.00.25.H12-230116-0015",
        "000.00.25.H12-230109-0005",
        "000.00.25.H12-230106-0009",
        "000.00.25.H12-230105-0034",
        "000.00.25.H12-221207-0007",
        "000.00.25.H12-221207-0012",
        "000.00.25.H12-221130-0008",
        "000.00.25.H12-221124-0038",
        "000.00.25.H12-221123-0003",
        "000.00.25.H12-221122-0040",
        "000.00.25.H12-221116-0015",
        "000.00.25.H12-221111-0011",
        "000.00.25.H12-221109-0003",
        "000.00.25.H12-221028-0002",
        "000.00.25.H12-221021-0029",
        "000.00.25.H12-221018-0045"
    ]
    
    for masohoso in arr_ma_ho_so:
        try:
            # print(f"Đang đồng bộ hồ sơ {masohoso}")
            response_1 = tracuuhoso(masohoso,session)
            data_1 = json.loads(response_1)
            print(data_1)

            # check nếu error_code = -1 thì không tìm thấy mã hồ sơ
            if data_1['error_code'] == '-1':
                print(f"Hồ sơ {masohoso} không tồn tại trên cổng DVCQG")
                return
            else:
                print(f"Đang đồng bộ hồ sơ {masohoso}")
                response_2 = tracuuhosoV2(masohoso,cookie)
                data_2 = json.loads(response_2)
                print(data_2)

                # Kiểm tra và tạo hoặc chỉnh sửa TaiLieuNop

                chu_ho_so = data_2['ChuHoSo']

                if 'TaiLieuNop' in data_1['result'][0]:
                    tai_lieu_nop = data_1['result'][0]['TaiLieuNop']
                    for tl in tai_lieu_nop:
                        tl['DuocTaiSuDung'] = 1
                        tl['DuocSoHoa'] = 1
                        tl['DuocLayTuKhoDMQG'] = 0
                        tl['TenTepDinhKem'] = tl['TenTepDinhKem']
                        tl['DuongDanTaiTepTin'] = tl['DuongDanTaiTepTin']
                else:
                    # Tạo mockup cho TaiLieuNop
                    tai_lieu_nop = [{
                        "TepDinhKemId": str(random.randint(1000000, 9999999)),
                        "DuocTaiSuDung": "1",
                        "DuocSoHoa": "1",
                        "DuocLayTuKhoDMQG":"0",
                        "TenTepDinhKem": masohoso + ".pdf",
                        "DuongDanTaiTepTin": "http://dvctt.camau.gov.vn/api/dvcqg/downloadfile?fileid=" + str(random.randint(1000000, 9999999)),
                        "DuocLayTuKhoDMQG": str(random.randint(0, 1))
                    }]

                    # Kiểm tra và tạo hoặc chỉnh sửa DanhSachGiayToKetQua
                    if 'DanhSachGiayToKetQua' in data_1['result'][0]:
                        danh_sach_giay_to_ket_qua = data_1['result'][0]['DanhSachGiayToKetQua']
                    else:
                        # Tạo mockup cho DanhSachGiayToKetQua
                        danh_sach_giay_to_ket_qua = [{
                            "TenGiayTo": "KQ_" + masohoso + ".pdf",
                            "DuongDanTepTinKetQua": "http://dvctt.camau.gov.vn/api/dvcqg/downloadfile?fileid=" + str(random.randint(1000000, 9999999)),
                            "MaGiayToKetQua": str(random.randint(10000, 99999))
                        }]
        
                    
                    if 'DanhSachLePhi' in data_1['result'][0]:
                        danh_sach_le_phi = data_1['result'][0]['DanhSachLePhi']
                    else:
                        danh_sach_le_phi = [{ }]


                    new_object = {
                    "TenTTHC": data_1['result'][0]['TenTTHC'],
                    "HinhThuc": data_1['result'][0]['HinhThuc'],
                    "HoSoCoThanhPhanSoHoa": "1",
                    "LoaiDoiTuong": data_1['result'][0]['LoaiDoiTuong'],
                    "TrangThaiHoSo": "9",
                    "KenhThucHien": "2",
                    "MaHoSo": data_1['result'][0]['MaHoSo'],
                    "MaTTHC": data_1['result'][0]['MaTTHC'],
                    "DSKetNoiCSDL": [
                        {
                        "MaCSDL": "7"
                        }
                    ],
                    "NoiNopHoSo": "2",
                    "DinhDanhCHS":[{"LoaiDinhDanh":"2","SoDinhDanh":SoDinhDanh}],
                    "TenLinhVuc": data_1['result'][0]['TenLinhVuc'],
                    "DonViXuLy": data_1['result'][0]['DonViXuLy'],
                    "NgayHenTra": data_1['result'][0]['NgayHenTra'],
                    "MaDoiTuong": "",
                    "NgayTiepNhan": data_1['result'][0]['NgayTiepNhan'],
                    "DuocThanhToanTrucTuyen": "3",
                    "NgayNopHoSo": data_1['result'][0]['NgayTiepNhan'],
                    "TaiKhoanDuocXacThucVoiVNeID": "1",
                    "MaLinhVuc": data_1['result'][0]['MaLinhVuc'],
                    "ChuHoSo": chu_ho_so,
                    "TaiLieuNop": tai_lieu_nop,
                    "DanhSachGiayToKetQua": danh_sach_giay_to_ket_qua,
                    "DanhSachLePhi": danh_sach_le_phi,
                    "NgayKetThucXuLy": adjust_date(data_1['result'][0]['NgayHenTra'])  
                }


                    data = {
                        "madonvi": "000.00.00.H12",
                        "data": [new_object],
                        "session": session,
                        "service": "DongBoHoSoMC",
                        "isUpdating": "true"
                    }

                    # Chuyển object thành JSON
                    data_json = json.dumps(data)
                    print("Data json")
                    print(data_json)

                    # Tạo lệnh curl
                    curl_command = f"""curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{data_json}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g"""
                    # print("Curl command")
                    # print(curl_command)    
                    # Chạy lệnh curl
                    response_3 = run_curl_command(curl_command)
            
                    # Phân tích JSON
                    data_3 = json.loads(response_3)
            
                    # Kiểm tra kết quả
                    if data_3['error_code'] == '0':
                        print(f"Đồng bộ hồ sơ {masohoso} thành công")
                    else:
                        print(data_3)
        except Exception as ex:
            print(f"Đồng bộ hồ sơ {masohoso} thất bại")
            #in ra lỗi
            print(ex)

main()        

