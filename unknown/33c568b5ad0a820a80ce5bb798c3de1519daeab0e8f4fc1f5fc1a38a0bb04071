from pymongo import MongoClient
from bson import ObjectId

# Connect to MongoDB
client = MongoClient('**********************************************************************************************')  # Replace with your MongoDB connection string

# Select the database and collection
db = client['svcBasepad']  # Replace with your database name
collection = db['procedure']

# Define the query
query = {
    # "nationCode": { "$exists": "True" },
    # "children": { "$ne": [] },
    "extendKTM.category": { "$not": { "$elemMatch": { "id": ObjectId("663f7b11786f387a49264816") } } }
}

# Define the update operation
update = {
    "$addToSet": {
        "extendKTM.category": {
            "id": ObjectId("663f7b11786f387a49264816"),
            "name": [
                {
                    "languageId": 228,
                    "name": "Th<PERSON> tục khác"
                }
            ],
            "code": "CD_Khac"
        }
    }
}

# Find the documents and limit the results to 1000
documents = collection.find(query).limit(10)

# Update the documents
for document in documents:
    collection.update_one({"_id": document["_id"]}, update)
    # print(document)

print(f"Updated up to 1000 documents with the specified query and update")
