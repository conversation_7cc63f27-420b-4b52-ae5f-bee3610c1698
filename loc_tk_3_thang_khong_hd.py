from pymongo import MongoClient
import pandas as pd
from datetime import datetime, timedelta

# Kết nối tới MongoDB
client = MongoClient('*************************************************?authMechanism=DEFAULT&directConnection=true')
sys_log_db = client['sysLog']
svc_human_db = client['svcHuman']
svc_sysman_db = client['svcSysman']

# Tính toán ngày từ 3 tháng trước
three_months_ago = datetime.now() - timedelta(days=90)

# <PERSON><PERSON><PERSON> danh sách userEvents.id từ bảng userEventsLog trong collection sysLog
user_events_log_collection = sys_log_db['userEventsLog']

user_events_ids = user_events_log_collection.find(
    {"createdDate": {"$gte": three_months_ago}, "clientId": "web-onegate"},
    {"userEvents.id": 1, "_id": 0}
)

user_events_ids_list = [event['userEvents']['id'] for event in user_events_ids]

# <PERSON><PERSON><PERSON>nh sách tài khoản trong bảng user của collection svcHuman
user_collection = svc_human_db['user']

filtered_users = user_collection.find(
    {
        # "role": {"$in": ["role/IGATE_MOT_CUA", "role/IGATE_XU_LY_HO_SO"]},
        "role": {"$in": ["role/admin"]},
        "account.enable": True,
        # "id": {"$nin": user_events_ids_list}
    }
)

# Lọc danh sách tài khoản trong bảng user của collection svcHuman
user_collection = svc_human_db['user']

# Tạo danh sách id của các user không có agency
# user_ids_without_agency = []
# for user in filtered_users:
#     if not user.get("experience", [{}])[0].get("agency"):
#         user_ids_without_agency.append(user["_id"])

# Cập nhật trạng thái enable:false trong collection svcSysman, bảng account
account_collection = svc_sysman_db['account']

# user_ids_without_agency_parent = []
# for user in filtered_users:
#     parent_name = user.get("experience", [{}])[0].get("agency", {}).get("parent", {}).get("name", [{}])[0].get("name", "")
#     if parent_name != "":
#         user_ids_without_agency_parent.append(user["_id"])

# print(f"Updated {len(user_ids_without_agency)} accounts to enable: false")


user_data = []
so_tt = 0
for user in filtered_users:
    
    fullname = user.get("fullname", "")
    username = user.get("account", {}).get("username", [{}])[0].get("value", "")
    agency_name = user.get("experience", [{}])[0].get("agency", {}).get("name", [{}])[0].get("name", "")
    parent_name = user.get("experience", [{}])[0].get("agency", {}).get("parent", {}).get("name", [{}])[0].get("name", "")
    if parent_name != "":
        so_tt += 1
        user_data.append([so_tt,fullname, username, agency_name, parent_name])

# Chuyển dữ liệu thành DataFrame
df = pd.DataFrame(user_data, columns=["no","fullname", "username", "agency_name", "parent_name"])

# Sắp xếp theo "Parent Name"
df_sorted = df.sort_values(by=["parent_name"])

# Xuất ra file Excel
output_file = "Danh_sach_tai_khoan_admin.xlsx"
df_sorted.to_excel(output_file, index=False)

print(f"Data has been exported to {output_file}")

# Đóng kết nối
client.close()