from collections.abc import Iterable
import json
from datetime import datetime, timedelta, timezone
from pymongo import MongoClient
from bson import ObjectId
import subprocess

def run_curl_command(curl_command):
    """ Chạy lệnh curl và trả về kết quả. """
    process = subprocess.Popen(curl_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        print(f"Error: {stderr}")
        return None
    else:
        return stdout.decode('utf-8')
    
def get_session():
    # Lệnh curl đầu tiên
    curl_command_1 = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/login
    """

    # Ch<PERSON><PERSON> lệnh curl đầu tiên và lưu response
    response_1 = run_curl_command(curl_command_1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session

def process_document(from_date, to_date):
    list_success = []
    list_fail = []
    # Initialize the MongoDB client (replace with your connection details)
    client = MongoClient('*********************************************************************************************************************************')

    # Select the database and collection
    db = client["svcAdapter"]
    collection = db["integratedLogs"]

    # Define the date range
    from_date = datetime.strptime(from_date, "%Y-%m-%d")
    to_date = datetime.strptime(to_date, "%Y-%m-%d")

    # Generate the list of dates in yymmdd format
    date_list = []
    current_date = from_date
    while current_date <= to_date:
        date_list.append(current_date.strftime("%y%m%d"))
        current_date += timedelta(days=1)

    # Query the MongoDB collection for matching item.code values
    # Query the MongoDB collection for matching item.code values and status 1, projecting only the "data" field
    # results = []
    # for date in date_list:
    #     regex_pattern = f".*{date}.*"
    #     query = {
    #         "item.code": {"$regex": regex_pattern},
    #         "status": 1,
    #         "service.id": ObjectId("5f7c16069abb62f511890030"),
    #         "data": {"$ne": "null"}
    #     }
    #     projection = {"_id": 0, "data": 1}  # Project only the "data" field
    #     matching_documents = list(collection.find(query, projection))
    #     results.extend(matching_documents)
    pipeline = [
    {
        "$match": {
            "item.code": {"$regex": f"{'|'.join(date_list)}"},
            "status": 1,
            "service.id": ObjectId("5f7c16069abb62f511890030"),
            "data": {"$ne": None}
        }
    },
    {
        "$sort": {"item.code": 1, "createdDate": -1}
    },
    {
        "$group": {
            "_id": "$item.code",
            "latestDocument": {"$first": "$$ROOT"}
        }
    },
    {
        "$replaceRoot": {"newRoot": "$latestDocument"}
    },
    {
        "$project": {"_id": 0, "data": 1}  # Project only the "data" field
    }
]

    results = list(collection.aggregate(pipeline, allowDiskUse=True))

    # Map the fields
    mapped_results = []
    unique_codes = set()
    for result in results:
        if "data" in result:
            try:
                data = json.loads(result["data"])  # Parse the JSON string
            except json.JSONDecodeError:
                continue  # Skip this document if parsing fails
            session = get_session()
            try:
                mapped_data = {
                    "session": session,
                    "madonvi": data.get("madonvi"),
                    "service": data.get("service"),
                    "isUpdating": "true",
                    "data": []
                }
            except Exception as e:
                print(f"Không thể lấy dữ liệu quan trọng từ document: {e}")
                continue
            
            for item in data.get("data", []):
               
                if item.get("status") != "1":  # Ensure item status is 1
                    continue
                code = item.get("code")
                if code in unique_codes:
                    continue
                unique_codes.add(code)
                
                mapped_item = {
                    "MaHoSo": item.get("code"),
                    "MaTTHC": item.get("procedureCode"),
                    "TenTTHC": item.get("procedureName"),
                    "MaLinhVuc": item.get("sectorCode"),
                    "TenLinhVuc": item.get("sectorName"),
                    "KenhThucHien": item.get("receptionMethod"),
                    "ChuHoSo": item.get("applicantName"),
                    "LoaiDoiTuong": item.get("applicantType"),
                    "MaDoiTuong": item.get("applicantCode"),
                    "ThongTinKhac": item.get("applicantOtherInfo"),
                    "Email": item.get("applicantEmail"),
                    "Fax": item.get("applicantFax"),
                    "SoDienThoai": item.get("applicantPhoneNumber"),
                    "TrangThaiHoSo": item.get("status"),
                    "NgayTiepNhan": item.get("receptedDate"),
                    "NgayHenTra": item.get("appointmentDate"),
                    "NgayTra": item.get("completedDate"),
                    "HinhThuc": item.get("returnedMethod"),
                    "HoSoCoThanhPhanSoHoa": item.get("dossierDigitizing"),
                    "TaiKhoanDuocXacThucVoiVNeID": item.get("accountAuthenticatedWithVNeID"),
                    "DuocThanhToanTrucTuyen": item.get("onlinePayment"),
                    "DinhDanhCHS": [{"LoaiDinhDanh": chs.get("identityType"), "SoDinhDanh": chs.get("identityNumber")} for chs in item.get("ownerAuthenticated", [])],
                    "NgayNopHoSo": item.get("submissionDate"),
                    "DSKetNoiCSDL": [{"MaCSDL": csdl.get("codeCSDL")} for csdl in item.get("ConnectedDatabase", [])],
                    "NoiNopHoSo": item.get("submissionPlace"),
                    "DonViXuLy": item.get("handlingAgency"),
                    "TaiLieuNop": [{"TenTepDinhKem": tl.get("name"), "DuongDanTaiTepTin": tl.get("fileLink"), "DuocSoHoa": tl.get("digitized"), "DuocTaiSuDung": "1", "DuocLayTuKhoDMQG": tl.get("takeFromStockDMQG")} for tl in item.get("attachment", [])],
                    "DanhSachLePhi": [{"TenPhiLePhi": phi.get("name","Lệ phí"), "MaPhiLePhi": phi.get("code"), "HinhThucThu": phi.get("feeMethod"), "Gia": phi.get("price"), "LoaiPhiLePhi": phi.get("type")} for phi in item.get("fees", [])],
                    "DanhSachHoSoBoSung": [{"HoSoBoSungId": hsbs.get("idAdditional"), "NguoiYeuCauBoSung": hsbs.get("name","Can Bo"), "NoiDungBoSung": hsbs.get("content",""), "NgayBoSung": hsbs.get("additionalDate"), "NgayTiepNhanBoSung": hsbs.get("additionalAcceptedDate"), "TrangThaiBoSung": hsbs.get("status"), "NgayHenTraTruoc": hsbs.get("preAppointmentDate")} for hsbs in item.get("additionalDossiers", [])],
                    "DanhSachGiayToKetQua": [{"TenGiayTo": gkq.get("name"), "DuongDanTepTinKetQua": gkq.get("fileLink"), "MaGiayToKetQua": gkq.get("resultCode")} for gkq in item.get("result", [])],
                    "NgayYeuCau": item.get("requestDateCreated"),
                    "NgayTuChoi": item.get("cancelDateCreated"),
                }
               
                mapped_data["data"].append(mapped_item)
                
                # Chuyển object thành JSON
                data_json = json.dumps(mapped_data, ensure_ascii=False)

                # Tạo lệnh curl
                curl_command = f"""curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{data_json}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g"""
                # print(curl_command)
                # Chạy lệnh curl
                try:
                    response = run_curl_command(curl_command)

                    # Phân tích JSON
                    data_res = json.loads(response)
                    # Kiểm tra kết quả
                    if data_res['error_code'] == '0':
                        list_success.append(code)
                    else:
                        list_fail.append(code)
                    print(f"Đồng bộ hồ sơ {code} thành công")
                except Exception as e:
                    list_fail.append(code)
                    print(f"Đồng bộ hồ sơ {code} gặp lỗi: {e}")
        else:
            print("Không có trường 'data' trong document")
            continue
    # Trả về số lượng hồ sơ thành công và thất bại
    message = f"Đồng bộ {len(list_success)} hồ sơ thành công và {len(list_fail)} hồ sơ thất bại"
    return message

    
def main(from_date,to_date):
    message = process_document(from_date, to_date)
    print(message)

if __name__ == "__main__":
    from_date = input("Nhập ngày bắt đầu (yyyy-mm-dd): ")
    to_date = input("Nhập ngày kết thúc (yyyy-mm-dd): ")
    main(from_date,to_date)
