{"_id": {"$oid": "6548a20473607d436e63463a"}, "code": "000.00.23.H12-231106-0204", "codePattern": {"id": {"$oid": "644747fdbc8884294b47393e"}}, "procedure": {"id": {"$oid": "652dedaae8b67510552d43cc"}, "code": "1.003003.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON><PERSON>ng ký và cấp <PERSON><PERSON><PERSON><PERSON> chứng nhận quyền sử dụng đất, quyền sở hữu nhà ở và tài sản khác gắn liền với đất lần đầu"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652dee0ee8b67510552d4419"}, "processDefinition": {"id": {"$oid": "652de8a0e1553b068aebf024"}, "processingTime": 21, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}}, "applicantEForm": {"id": {"$oid": "652d4264c56bc1001fc6e11f"}}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411beabaf4d70261e45f22d"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "activiti": {"id": "Process_aqxi1eXIi:336:b00dee3a-6c8f-11ee-8dd7-a2137e48eaaf", "model": {"_id": "28a8edd0-44fa-4473-a08d-7fc0aff607fd", "name": "qui-trinh-ilis", "project": {"id": "4058239e-3d80-436d-acb6-21b67a2793e2"}}}}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON> tảng <PERSON> to<PERSON> vụ công <PERSON> gia"}, "applicant": {"eformId": {"$oid": "652d4264c56bc1001fc6e11f"}, "userId": {"$oid": "65489feb60b15f57aa93fa24"}, "data": {"birthday": "1979-01-05T00:00:00.000+0000", "note": "", "gender": "", "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "5def47c5f47614018c001971"}, "identityDate": "1979-01-05T00:00:00+07:00", "nycSoGiayToTuyThan": "381161039", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 1, "nycHoTen": "Tống Trườ<PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "381161039", "serialMap": "12", "identityAgency": {"name": "<PERSON>ông an tỉnh Cà Mau", "id": "64803460b6e17a7c177b0d07", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "<PERSON><PERSON><PERSON>", "value": "5def47c5f47614018c132191"}, "email": "", "address": "khóm 4", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON><PERSON>", "value": "5def47c5f47614018c132191"}, "pdg": false, "serialLand": "80", "phoneNumber": "0943601106", "declarationForm": {"identifyNo": "381161039", "phone": "0943601106", "fullName": "Tống Trườ<PERSON>", "birthDateStr": "05/01/1979", "email": "", "idTypeId": 1}, "district": {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "5def47c5f47614018c001971"}, "diaChiThuaDat": "khóm 4", "fullname": "Tống Trườ<PERSON>"}}, "accepter": {"id": {"$oid": "652e3be70ec8eb131c3c9fc9"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411beabaf4d70261e45f22d"}, "code": "000.00.23.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả KQ UBND huyện Năm Căn"}], "parent": {"id": {"$oid": "64093967af4d70261e45f1bf"}, "code": "000.00.23.H12", "name": [{"languageId": 228, "name": "UBND huyện Năm <PERSON>ăn"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "64093967af4d70261e45f1bf"}, "name": [{"languageId": 228, "name": "UBND huyện Năm <PERSON>ăn"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-11-06T08:25:07.000Z"}, "appliedDate": {"$date": "2023-11-06T08:21:24.285Z"}, "appointmentDate": {"$date": "2023-12-05T08:25:00.000Z"}, "completedDate": {"$date": "2023-11-08T08:47:59.892Z"}, "returnedDate": {"$date": "2023-11-09T09:57:14.374Z"}, "attachment": [{"id": {"$oid": "654b4b46bed81f4d056bb028"}, "filename": "000.00.23.H12-231106-0204-KQ.pdf", "size": {"$numberLong": "251174"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "6548a2e5cba57f723c89c94d"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411beabaf4d70261e45f22d"}, "code": "000.00.23.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả KQ UBND huyện Năm Căn"}], "parent": {"id": {"$oid": "64093967af4d70261e45f1bf"}, "name": [{"languageId": 228, "name": "UBND huyện Năm <PERSON>ăn"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "64093967af4d70261e45f1bf"}, "name": [{"languageId": 228, "name": "UBND huyện Năm <PERSON>ăn"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:336:b00dee3a-6c8f-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411beabaf4d70261e45f22d"}, "code": "000.00.23.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả KQ UBND huyện Năm Căn"}], "parent": {"id": {"$oid": "64093967af4d70261e45f1bf"}, "name": [{"languageId": 228, "name": "UBND huyện Năm <PERSON>ăn"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "64093967af4d70261e45f1bf"}, "name": [{"languageId": 228, "name": "UBND huyện Năm <PERSON>ăn"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-06T08:25:07.000Z"}, "completedDate": {"$date": "2023-11-06T08:32:12.243Z"}, "dueDate": {"$date": "2023-11-07T02:25:00.000Z"}, "createdDate": {"$date": "2023-11-06T08:25:07.000Z"}, "updatedDate": {"$date": "2023-11-06T08:32:12.243Z"}, "activitiTask": {"id": "feb7ab4b-7c7d-11ee-87ef-1e0609d18942", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652de8a0e1553b068aebf025"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6548a48b992f19002bd80afa"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "64093967af4d70261e45f1bf"}, "code": "000.00.23.H12", "name": [{"languageId": 228, "name": "UBND huyện Năm <PERSON>ăn"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:336:b00dee3a-6c8f-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "6548a2e5cba57f723c89c94d"}}, "agency": {"id": {"$oid": "64093967af4d70261e45f1bf"}, "code": "000.00.23.H12", "name": [{"languageId": 228, "name": "UBND huyện Năm <PERSON>ăn"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-06T08:32:11.254Z"}, "completedDate": {"$date": "2023-11-08T08:48:05.247Z"}, "dueDate": {"$date": "2023-12-04T08:32:00.000Z"}, "claimDate": {"$date": "2023-11-06T08:32:11.254Z"}, "createdDate": {"$date": "2023-11-06T08:32:11.254Z"}, "updatedDate": {"$date": "2023-11-08T08:48:05.247Z"}, "activitiTask": {"id": "fae6d787-7c7e-11ee-b084-f2e05e58db49", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652de8a0e1553b068aebf026"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 20, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b4b44e4af0a2ae165e58e"}, "assignee": {"id": {"$oid": "652e3be70ec8eb131c3c9fc9"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "652e3be794d7072e39bbda01"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411beabaf4d70261e45f22d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093967af4d70261e45f1bf"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:336:b00dee3a-6c8f-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "6548a48b992f19002bd80afa"}}, "agency": {"id": {"$oid": "6411beabaf4d70261e45f22d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093967af4d70261e45f1bf"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-08T08:48:04.260Z"}, "completedDate": {"$date": "2023-11-09T09:57:14.374Z"}, "dueDate": {"$date": "2023-11-09T02:48:00.000Z"}, "createdDate": {"$date": "2023-11-08T08:48:04.260Z"}, "updatedDate": {"$date": "2023-11-09T09:57:14.374Z"}, "activitiTask": {"id": "87c46f71-7e13-11ee-be87-42a6c3097252", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652de8a0e1553b068aebf027"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "654b4b44e4af0a2ae165e58e"}, "assignee": {"id": {"$oid": "652e3be70ec8eb131c3c9fc9"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "652e3be794d7072e39bbda01"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411beabaf4d70261e45f22d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093967af4d70261e45f1bf"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:336:b00dee3a-6c8f-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "6548a48b992f19002bd80afa"}}, "agency": {"id": {"$oid": "6411beabaf4d70261e45f22d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093967af4d70261e45f1bf"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-08T08:48:04.260Z"}, "completedDate": {"$date": "2023-11-09T09:57:14.374Z"}, "dueDate": {"$date": "2023-11-09T02:48:00.000Z"}, "createdDate": {"$date": "2023-11-08T08:48:04.260Z"}, "updatedDate": {"$date": "2023-11-09T09:57:14.374Z"}, "activitiTask": {"id": "87c46f71-7e13-11ee-be87-42a6c3097252", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652de8a0e1553b068aebf027"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "feb78436-7c7d-11ee-87ef-1e0609d18942", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1979-01-05T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "381161039", "nycLoaiGiayToTuyThan": 1, "declarationForm": {"identifyNo": "381161039", "phone": "0943601106", "fullName": "Tống Trườ<PERSON>", "birthDateStr": "05/01/1979", "email": "", "idTypeId": 1}, "phoneNumber": "0943601106", "nycHoTen": "Tống Trườ<PERSON>", "identityNumber": "381161039", "fullname": "Tống Trườ<PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-11-06T08:21:24.285Z"}, "updatedDate": {"$date": "2023-11-09T09:57:24.806Z"}, "sync": {"id": 1, "sourceCode": "000.00.23.H12-231106-0204", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: X/sn2qHaK+xvXSIiPLrC6D1iE4mbXv7cJE5R4JSfAoYpm6u7Pm0aYw\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5f39f4335224cf235e134c5b"}, "name": [{"languageId": 228, "name": "Cấp Tỉnh"}, {"languageId": 46, "name": "Province"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": true, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 0, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}