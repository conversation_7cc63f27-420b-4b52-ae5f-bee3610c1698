import json
import subprocess
import logging
from datetime import datetime, timedelta
from typing import List, Tuple, Dict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sync_process.log'),
        logging.StreamHandler()
    ]
)

class CurlClient:
    def __init__(self):
        self.base_url = "http://*************:8081/XrdAdapter/RestService/forward/mapi"
        self.ma_don_vi = "000.00.00.H12"
        self.headers = "-H 'Content-Type: application/json' -H 'Charset: utf-8' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'"

    def run_curl(self, curl_command: str) -> Dict:
        """Execute curl command and return JSON response"""
        try:
            process = subprocess.Popen(
                curl_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True
            )
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                logging.error(f"Curl error: {stderr.decode()}")
                return {"error_code": -1}

            return json.loads(stdout.decode('utf-8'))
        except Exception as e:
            logging.error(f"Error executing curl: {str(e)}")
            return {"error_code": -1}

    def get_session(self) -> str:
        """Get session token"""
        curl_command = f"""
        curl -d '{{"username":"{self.ma_don_vi}","password":"{self.ma_don_vi}@123456"}}' \
        {self.headers} {self.base_url}/login
        """
        response = self.run_curl(curl_command)
        return response.get('session', '')

    def search_record(self, record_id: str, session: str) -> Dict:
        """Search for record details"""
        data = {
            "madonvi": self.ma_don_vi,
            "session": session,
            "service": "TraCuuHoSo",
            "mahoso": record_id
        }
        curl_command = f"""
        curl -X POST {self.headers} -d '{json.dumps(data)}' {self.base_url}/g
        """
        return self.run_curl(curl_command)

    def update_record_progress(self, data: List[Dict], session: str) -> Dict:
        """Update record progress"""
        payload = {
            "madonvi": self.ma_don_vi,
            "data": data,
            "session": session,
            "service": "CapNhatTienDoHoSoMC"
        }
        curl_command = f"""
        curl -X POST {self.headers} -d '{json.dumps(payload)}' {self.base_url}/g
        """
        return self.run_curl(curl_command)

class RecordProcessor:
    def __init__(self):
        self.client = CurlClient()
        self.date_format = "%Y%m%d%H%M%S"

    def calculate_milestones(self, start_date: str) -> List[Tuple[str, str]]:
        """Calculate milestones within 8 hours from start_date"""
        try:
            # Parse start date
            start_dt = datetime.strptime(start_date, self.date_format)
            
            # Add 8 hours to start date
            end_dt = start_dt + timedelta(hours=8)
            
            # If end time is after 17:00, adjust to 17:00
            if end_dt.hour >= 17:
                end_dt = end_dt.replace(hour=17, minute=0, second=0)
            
            # Calculate time range in minutes
            total_minutes = int((end_dt - start_dt).total_seconds() / 60)
            
            # Create 3 milestones
            milestones = []
            for i in range(3):
                milestone_start = start_dt + timedelta(minutes=(total_minutes * i) // 3)
                milestone_end = start_dt + timedelta(minutes=(total_minutes * (i + 1)) // 3)
                
                milestones.append((
                    milestone_start.strftime(self.date_format),
                    milestone_end.strftime(self.date_format)
                ))
            
            return milestones
        except Exception as e:
            logging.error(f"Error calculating milestones: {str(e)}")
            return []

    def create_progress_objects(self, record_id: str, milestones: List[Tuple[str, str]]) -> List[Dict]:
        """Create progress update objects"""
        stages = [
            ("CB Một cửa", "CB_TiepNhan", "Bộ phận tiếp nhận và trả kết quả", "Được tiếp nhận", "2"),
            ("CB Xử lý", "CB_XuLy", "Bộ phận xử lý", "Đang xử lý", "4"),
            ("CB Một cửa", "CB_TiepNhan", "Bộ phận tiếp nhận và trả kết quả", "Đã xử lý xong", "9"),
            ("CB Một cửa", "CB_TiepNhan", "Bộ phận tiếp nhận và trả kết quả", "Đã trả kết quả", "10")
        ]
        
        return [
            {
                "MaHoSo": record_id,
                "NguoiXuLy": stage[0],
                "ChucDanh": stage[1],
                "ThoiDiemXuLy": milestone[0],
                "PhongBanXuLy": stage[2],
                "NoiDungXuLy": stage[3],
                "TrangThai": stage[4],
                "NgayBatDau": milestone[0],
                "NgayKetThucTheoQuyDinh": milestone[1]
            }
            for stage, milestone in zip(stages, milestones + [milestones[-1]])
        ]

    def process_record(self, record: Dict, session: str) -> bool:
        """Process a single record"""
        try:
            record_id = record['ma_hs']
            search_result = self.client.search_record(record_id, session)
            
            if search_result.get('error_code') == -1:
                logging.warning(f"Record {record_id} not found")
                return False
                
            result = search_result['result'][0]
            milestones = self.calculate_milestones(result['NgayTiepNhan'])
            
            if not milestones:
                return False
                
            progress_objects = self.create_progress_objects(record_id, milestones)
            update_result = self.client.update_record_progress(progress_objects, session)
            
            success = update_result.get('error_code') == '0'
            if success:
                logging.info(f"Successfully processed record {record_id}")
            else:
                logging.error(f"Failed to process record {record_id}")
            return success
            
        except Exception as e:
            logging.error(f"Error processing record {record.get('ma_hs')}: {str(e)}")
            return False

def display_progress(total: int, current: int, success: int, failure: int):
    """Display progress bar"""
    bar_length = 50
    progress = current / total
    filled = int(bar_length * progress)
    bar = "█" * filled + "-" * (bar_length - filled)
    
    print(f"\r[{bar}] {progress*100:.1f}% | "
          f"Success: {success}/{total} | "
          f"Failed: {failure}/{total}", end="")

def main(file_path: str):
    try:
        # Initialize processor
        processor = RecordProcessor()
        
        # Load records
        with open(file_path, 'r') as f:
            records = json.load(f)
        
        total_records = len(records)
        success = failure = 0
        
        # Get session token
        session = processor.client.get_session()
        if not session:
            logging.error("Failed to obtain session token")
            return
            
        # Process records
        for i, record in enumerate(records, 1):
            success_status = processor.process_record(record, session)
            if success_status:
                success += 1
            else:
                failure += 1
            display_progress(total_records, i, success, failure)
        
        print("\nProcessing completed!")
        logging.info(f"Final results - Success: {success}, Failed: {failure}")
        
    except Exception as e:
        logging.error(f"Critical error in main process: {str(e)}")

if __name__ == "__main__":
    file_path = input("Enter the file path: ")
    main(file_path)