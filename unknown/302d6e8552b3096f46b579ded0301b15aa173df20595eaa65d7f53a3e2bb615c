import pandas as pd
from pymongo import MongoClient
from datetime import datetime
from bson import ObjectId
import dateutil

# Read data from Excel
excel_file = 'excel/VNPOST.xlsx'
df = pd.read_excel(excel_file)

# Connect to MongoDB
client = MongoClient('**********************************************************************************************')
db = client['svcAdapter']
collection = db['mappingData']

# Iterate over the DataFrame and create the objects
dateStr = '2024-06-04T03:24:35.357Z'
myDatetime = dateutil.parser.parse(dateStr)
for index, row in df.iterrows():
    document = {
        "_id": ObjectId(),
        "type": {
            "id": ObjectId("5fc0707a62681a8bef000007"),
            "trans": [
                {
                    "languageId": 228,
                    "name": "C<PERSON> quan VNPost",
                    "description": "Mapping danh mục cơ quan VNPost"
                },
                {
                    "languageId": 46,
                    "name": "Agency VNPost",
                    "description": "Mapping agency category VNPost"
                }
            ]
        },
        "source": {
            "id": row['ID_DON_VI'],
            "name": row['TEN_DON_VI']
        },
        "dest": {
            "id": row['MA_VNPOST'],
            "name": row['TEN_DON_VI']
        },
        "status": 1,
        "createdDate":  myDatetime,
        "updatedDate":  myDatetime,
        "ignoreDeployment": True,
        "deleted": False,
        "deploymentId": ObjectId("60ff693e8dbda12b96b63a4b"),
        "_class": "vn.vnpt.digo.adapter.document.MappingData"
    }
    
    # Insert the document into MongoDB
    collection.insert_one(document)

print("Data inserted successfully.")
