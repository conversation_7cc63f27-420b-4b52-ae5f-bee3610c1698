
import asyncio
import json
import pymongo
import requests
from datetime import datetime
import traceback
from bson import ObjectId
import httpx

# Connect to MongoDB
mongo_uri = '**********************************************************************************************'
client = pymongo.MongoClient(mongo_uri)

async def getToken():
    # from api
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = 'grant_type=password&username=admin&password=Cmu#0801&client_id=web-onegate'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    access_token = response.json()['access_token']
    return access_token
    
async def syncQG(code):
    url = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"
    token = await getToken()
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer '+token,
        'Cookie': 'JSESSIONID=979D1F437D51C126FF15E5BA8BE0E768'
    }
    
    # ["000.00.07.H12-231005-0204"]
    payload = json.dumps([code])
    # print(payload)
    
    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.text.encode('utf8'))
    if response.status_code == 200:
        if response.json()['affectedRows'] == 1:
            return 200
        else:
            return 500
    else:
        return 501
    
# Function query the database and return a list of documents
def get_documents():
    database_name = 'svcPadman'  # Replace with your database name
    collection_name = 'dossier'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    # Query the database
    # db.dossier.find({acceptedDate: {$gte: new Date("2023-11-01T00:00:00.000Z"), $lte: new Date("2023-11-02T23:59:59.999Z") }}).limit(1000)
    # Define the date range
    start_date = datetime(2024, 1, 1, 0, 0, 0)
    end_date = datetime(2024, 1, 31, 23, 59, 59, 999000)

    # Start a session
    with client.start_session() as session:
        # Query the collection with no_cursor_timeout within the session
        documents = collection.find({
            "acceptedDate": {
                "$gte": start_date,
                "$lte": end_date
            }
        }, no_cursor_timeout=True, session=session).limit(10000)

        # Return a list of documents, or handle them here as needed
        return list(documents)

def update_file(dossier_id):
    database_name = 'svcPadman'  # Replace with your database name
    collection_name = 'dossierFormFile'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    rs = "ok"
    # Query the database
    dossier_id = ObjectId(dossier_id)
    documents = collection.find({"dossier.id": dossier_id})
    # Update 'reused' to 1 in all objects inside the 'file' array for each document
    for document in documents:
        result = collection.update_one(
            {"_id": document["_id"]},
            {"$set": {"file.$[].reused": 1}}
        )

        # Check if the update was successful
        if result.modified_count > 0:
            print(f"Document with ID {document['_id']} was updated.")
        else:
            print(f"Document with ID {document['_id']} was not updated.")
            rs = "fail"

    return rs

async def main():
    count = 0

    # Get the list of documents with no_cursor_timeout
    # try:
    #     documents = get_documents()
    # except Exception as e:
    #     print(f"Failed to get documents: {e}")
    #     return

    # Loop through the documents
    for index, row in df.iterrows():
        code = None
        try:
            # Safely get the dossier id and code
            code = row['code']
            dossier_id = row['id']
            if not code or not dossier_id:
                raise ValueError("Document missing required fields")

            # Update the file
            rs = update_file(dossier_id)
            if rs == "ok":
                # Sync to QG with retry
                success = await syncQG(code)
                if success:
                    count += 1
                
        except Exception as e:
            if code:
                print(f"Error processing document {code if code else 'Unknown'}: {e}")
                traceback.print_exc()
                continue

    print("Done total: "+str(count))

# Run the main function
asyncio.run(main())
