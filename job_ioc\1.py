from datetime import datetime
import mysql.connector
from mysql.connector import Error
import json
import os

def get_list_co_quan():
    """Get list of agencies from MySQL"""
    connection = None 
    try:
        connection = mysql.connector.connect(
            host="*************",
            user="etl_cmu",
            password="Ioc@2024",
            database="cmu_db_dtm"
        )
        cursor = connection.cursor()
        query = "SELECT ID_DON_VI, TEN_DON_VI FROM igate_api_dm_co_quan_v2_0"
        cursor.execute(query)
        result = cursor.fetchall()
        return [(row[0], row[1]) for row in result]  # Convert to list of tuples
    except Error as e:
        print(f"Error: {e}")
        return []
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def get_list_procedure_id_from_db(id_don_vi):
    """Get list of procedure IDs for a specific agency"""
    connection = None
    try:
        connection = mysql.connector.connect(
            host="*************",
            user="etl_cmu",
            password="Ioc@2024",
            database="cmu_db_dtm"
        )
        cursor = connection.cursor()
        query = "SELECT ID_LINH_VUC FROM igate_dm_linh_vuc_v2_0 WHERE ID_DON_VI = %s"
        cursor.execute(query, (id_don_vi,))
        result = cursor.fetchall()
        return [row[0] for row in result]
    except Error as e:
        print(f"Error: {e}")
        return []
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def get_list_dm_ky(thang, nam):
    """Get list of periods from MySQL"""
    connection = None
    try:
        connection = mysql.connector.connect(
            host="*************",
            user="etl_cmu",
            password="Ioc@2024",
            database="cmu_db_dtm"
        )
        cursor = connection.cursor()
        query = "SELECT ID_KY, GIA_TRI_NGAY, HIEU_LUC_TU, HIEU_LUC_DEN FROM dm_ky WHERE GIA_TRI_THANG = %s and GIA_TRI_NAM = %s and LOAI_KY = 'M'"
        cursor.execute(query, (thang, nam))
        result = cursor.fetchall()
        
        # Convert to list of dictionaries for JSON serialization
        periods = []
        for row in result:
            periods.append({
                "id_ky": row[0],
                "gia_tri_ngay": row[1].strftime("%Y-%m-%d") if row[1] else None,
                "hieu_luc_tu": row[2].strftime("%Y-%m-%d") if row[2] else None,
                "hieu_luc_den": row[3].strftime("%Y-%m-%d") if row[3] else None
            })
        return periods
    except Error as e:
        print(f"Error: {e}")
        return []
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    start_time = datetime.now()
    thang = 5  # Month to fetch
    nam = 2025  # Year to fetch
    
    # Create output directory if it doesn't exist
    os.makedirs("data", exist_ok=True)
    
    # Get and save list of agencies
    print("Fetching list of agencies...")
    list_don_vi = get_list_co_quan()
    
    agencies_data = []
    for id_don_vi, ten_don_vi in list_don_vi:
        print(f"Processing agency: {ten_don_vi}")
        
        # Get list of procedure IDs for this agency
        procedure_ids = get_list_procedure_id_from_db(id_don_vi)
        
        # Add to agencies data
        agencies_data.append({
            "id_don_vi": id_don_vi,
            "ten_don_vi": ten_don_vi,
            "procedure_ids": procedure_ids
        })
    
    # Save agencies data to JSON
    with open("data/agencies.json", "w", encoding="utf-8") as f:
        json.dump(agencies_data, f, ensure_ascii=False, indent=2)
    
    # Get and save list of periods
    print(f"Fetching periods for month {thang}/{nam}...")
    list_ky = get_list_dm_ky(thang, nam)
    
    # Save periods data to JSON
    with open("data/periods.json", "w", encoding="utf-8") as f:
        json.dump(list_ky, f, ensure_ascii=False, indent=2)
    
    end_time = datetime.now()
    print(f"Time run script: {end_time - start_time}")
    print(f"Data saved to 'data/agencies.json' and 'data/periods.json'")

if __name__ == '__main__':
    main()