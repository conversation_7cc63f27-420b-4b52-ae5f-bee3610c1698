from datetime import datetime
import mysql.connector
from mysql.connector import <PERSON><PERSON>r
import json
import os

def insert_documents_to_mysql(data):
    """Import data from JSON to MySQL database"""
    connection = None
    try:
        connection = mysql.connector.connect(
            host="*************",
            user="etl_cmu",
            password="Ioc@2024",
            database="cmu_db_dtm"
        )
        cursor = connection.cursor()

        # SQL query for inserting or updating data
        insert_query = """
        INSERT INTO igate_tk_don_vi_linh_vuc_v2_0 (
            ID_KY,
            ID_DONVI_LINHVUC,
            TIEP_NHAN,
            TIEP_NHAN_TRUC_TIEP,
            TIEP_NHAN_TRUC_TUYEN,
            DANG_XU_LY,
            DANG_XU_LY_TRONG_HAN,
            DANG_XU_LY_QUA_HAN,
            HOAN_THANH,
            HOAN_THANH_TRONG_HAN,
            HOAN_THANH_TRUOC_HAN,
            HOA<PERSON>_THANH_TRE_HAN,
            DUNG_XU_LY,
            TRA_LAI
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            TIEP_NHAN = VALUES(TIEP_NHAN),
            TIEP_NHAN_TRUC_TIEP = VALUES(TIEP_NHAN_TRUC_TIEP),
            TIEP_NHAN_TRUC_TUYEN = VALUES(TIEP_NHAN_TRUC_TUYEN),
            DANG_XU_LY = VALUES(DANG_XU_LY),
            DANG_XU_LY_TRONG_HAN = VALUES(DANG_XU_LY_TRONG_HAN),
            DANG_XU_LY_QUA_HAN = VALUES(DANG_XU_LY_QUA_HAN),
            HOAN_THANH = VALUES(HOAN_THANH),
            HOAN_THANH_TRONG_HAN = VALUES(HOAN_THANH_TRONG_HAN),
            HOAN_THANH_TRUOC_HAN = VALUES(HOAN_THANH_TRUOC_HAN),
            HOAN_THANH_TRE_HAN = VALUES(HOAN_THANH_TRE_HAN),
            DUNG_XU_LY = VALUES(DUNG_XU_LY),
            TRA_LAI = VALUES(TRA_LAI)
        """
        
        # Track progress
        total_records = len(data)
        processed_records = 0
        
        # Process records in batches
        batch_size = 100
        for i in range(0, total_records, batch_size):
            batch = data[i:i+batch_size]
            for doc in batch:
                # Combine id_don_vi and id_linh_vuc to create ID_DONVI_LINHVUC
                id_donvi_linhvuc = doc['id_don_vi'] + doc['id_linh_vuc']
                
                # Execute the query
                cursor.execute(insert_query, (
                    doc['id_ky'],
                    id_donvi_linhvuc,
                    doc['received'],
                    doc['received_direct'],
                    doc['received_online'],
                    doc['total_unresolved'],
                    doc['total_unresolved_on_due'],
                    doc['total_unresolved_over_due'],
                    doc['total_resolved'],
                    doc['total_resolved_on_time'],
                    doc['total_resolved_early'],
                    doc['total_resolved_over_due'],
                    doc['total_paused'],
                    doc['total_returned']
                ))
            
            # Commit the batch
            connection.commit()
            
            # Update progress
            processed_records += len(batch)
            print(f"Processed {processed_records} of {total_records} records")
        
        print("Data import completed successfully")
        return True
    except Error as e:
        print(f"Error importing data: {e}")
        return False
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def main():
    start_time = datetime.now()
    
    # Check if results file exists
    results_file = "data/results/all_results.json"
    if not os.path.exists(results_file):
        print(f"Error: Results file '{results_file}' does not exist.")
        return
    
    # Load results from file
    with open(results_file, "r", encoding="utf-8") as f:
        data = json.load(f)
        
    # Insert data into MySQL
    success = insert_documents_to_mysql(data)
    
    end_time = datetime.now()
    print(f"Time run script: {end_time - start_time}")
    print(f"Results saved to '{results_file}'")

if __name__ == '__main__':
    main()