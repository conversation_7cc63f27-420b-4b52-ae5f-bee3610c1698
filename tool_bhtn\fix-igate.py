import asyncio
import pymongo
import httpx

# MongoDB connection details
mongo_uri = '*********************************************************************************************************************************'  # Replace with your MongoDB URI
database_name = 'svcPadman'  # Replace with your database name
collection_name = 'dossier'  # Replace with your collection name

async def getToken():
    # from api
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = 'grant_type=password&username=admin&password=Cmu#0801&client_id=web-onegate'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }


    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.post(url, data=payload, headers=headers)
        access_token = response.json()['access_token']
    return access_token

async def main():
    #call api get data
    # bearer_token = await getToken()
    # for from page 0 to 10 if data null break
    for i in range(5):
        # api_url = f"https://ketnoi.dichvucongcamau.gov.vn/pa/dossier/search?code=&spec=slice&page={i}&size=10&sort=dueDate,asc&identity-number=&applicant-name=&applicant-owner-name=&nation-id=&province-id=&district-id=&ward-id=&accepted-from=&accepted-to=&dossier-status=2,3,4,5,16,17,9&remove-status=0&filter-type=1&assignee-id=000000000000000000007777&sender-id=&candidate-group-id=652f3415292a834987456017&candidate-position-id=611ac793803d7d6c1411204b&candidate-group-parent-id=637d7dc1f217d52a06d6d0f9&current-task-agency-type-id=6546ffdc52888853c12b3092,6411c095e2c0611917fa0dfa&bpm-name-id=&noidungyeucaugiaiquyet=&noidung=&taxCode=&resPerson=&extendTime=&applicant-organization=&filter-by-candidate-group=false&is-query-processing-dossier=false&approve-agencys-id=652f3415292a834987456017,637d7dc1f217d52a06d6d0f9&remind-id=64116c60e2c0611917fa0df9&procedure-id=&vnpost-status-return-code="
        # headers = {
        #     "Authorization": "Bearer {}".format(bearer_token),
        #     "Content-Type": "application/json"
        # }

        # ssl_context = httpx.create_ssl_context()
        # ssl_context.set_ciphers("HIGH:!DH:!aNULL")

        # async with httpx.AsyncClient(verify=ssl_context) as client:
        #     response = await client.get(api_url, headers=headers)
        #     data = response.json()

        # # Connect to MongoDB
        client = pymongo.MongoClient(mongo_uri)
        database = client[database_name]
        collection = database[collection_name]

        # Extract codes and update the document
        # codes = [item['code'] for item in data['content']]
        codes = ['000.00.11.H12-240611-0059', '000.00.11.H12-240611-0052', '000.00.11.H12-240611-0058']

        for code in codes:
            # code = '000.00.11.H12-240522-0057'
            document = collection.find_one({'code': code})
            if document:
                # document['currentTask'] = [task for task in document['task'] if task['isLast'] == 1]
                document['currentTask'][0]['assignee'] = {}

                # Update the document in MongoDB
                collection.replace_one({'_id': document['_id']}, document)

                print(f'Document with code {code} updated successfully.')
            else:
                print(f'Document with code {code} not found.')
        
    # Close the MongoDB connection
    client.close()

asyncio.run(main())
