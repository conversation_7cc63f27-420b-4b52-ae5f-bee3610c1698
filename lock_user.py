import pandas as pd
from pymongo import MongoClient

# Step 1: Read the Excel file and get the usernames
df = pd.read_excel('excel/ds_lock.xlsx')
usernames = df['username'].tolist()

# Step 2: Connect to MongoDB
client = MongoClient("*************************************************?authMechanism=DEFAULT&directConnection=true")
svc_human_db = client['svcHuman']
svc_sysman_db = client['svcSysman']

users_collection = svc_human_db['users']
account_collection = svc_sysman_db['account']

# Step 3: Update the enable field in svcHuman.users
users_collection.update_many(
    {"account.username.value": {"$in": usernames}},
    {"$set": {"account.enable": False}}
)

# Step 4: Update the enable field in svcSysman.account
account_collection.update_many(
    {"username.value": {"$in": usernames}},
    {"$set": {"enable": False}}
)

print(f"Updated {len(usernames)} accounts to enable: false")