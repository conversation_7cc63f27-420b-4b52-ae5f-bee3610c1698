import pandas as pd
import pymongo
import requests
import json
from datetime import datetime
from bson import ObjectId
import re 

# MongoDB connection details
# Replace with your MongoDB URI
mongo_uri = '*********************************************************************************************************************************'
client = pymongo.MongoClient(mongo_uri)

huyens = [{"code": "000.00.20.H12",
           "alias": "CNC",
           "id": "637d8bf2f217d52a06d6d104"},
          {"code": "000.00.24.H12",
           "alias": "NHN",
           "id": "640939acaf4d70261e45f1c0"},
          {"code": "000.00.23.H12",
           "alias": "NCN",
           "id": "64093967af4d70261e45f1bf"},
          {"code": "000.00.22.H12",
           "alias": "PTN",
           "id": "640939cbaf4d70261e45f1c1"},
          {"code": "000.00.25.H12",
           "alias": "TBH",
           "id": "64093a03af4d70261e45f1c8"},
          {"code": "000.00.26.H12",
           "alias": "TVT",
           "id": "64093a91af4d70261e45f1c9"},
          {"code": "000.00.27.H12",
           "alias": "UMH",
           "id": "64093ab0af4d70261e45f1ca"},
          {"code": "000.00.21.H12",
           "alias": "DDI",
           "id": "637d8b2af217d52a06d6d101"},
          {"code": "000.00.28.H12",
           "alias": "TPCM",
           "id": "64093ad0af4d70261e45f1cb"}]


def updateagencyLevelProcedure():
    database_name = 'svcBasepad'  # Replace with your database name
    collection_name = 'procedure'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    capTinh = {'id': ObjectId('5f39f4335224cf235e134c5b'), 'name': [
        {'languageId': 228, 'name': 'Cấp Tỉnh'}]}
    capHuyen = {'id': ObjectId('5f39f4155224cf235e134c59'), 'name': [
        {'languageId': 228, 'name': 'Quận/ Huyện'}]}
    capXa = {'id': ObjectId('5febfe2295002b5c79f0fc9f'), 'name': [
        {'languageId': 228, 'name': 'Phường/ Xã'}]}
    cqKhac = {'id': ObjectId('62b967526f87844cfe48e26b'), 'name': [
        {'languageId': 228, 'name': 'Cơ quan khác'}]}
    with open('input/proCapTinh.json', encoding='utf-8') as f:
        codes = json.load(f)
    print(f'Số thủ tục cần cập nhật: {codes.__len__()}')
    codeNotFound = []
    for c in codes:
        agencyLevel = []
        code = c['code']
        level = c['level'].lower()
        for lv in level.split(';'):
            if lv.strip() == 'cấp tỉnh':
                agencyLevel.append(capTinh)
            elif lv.strip() == 'cấp huyện':
                agencyLevel.append(capHuyen)
            elif lv.strip() == 'cấp xã':
                agencyLevel.append(capXa)
            elif lv.strip() == 'cơ quan khác':
                agencyLevel.append(cqKhac)
        document = collection.find_one({'code': code})
        if document:
            collection.update_many(
                {'code': code}, {'$set': {'agencyLevel': agencyLevel}})
        else:
            print(f'Không tìm thấy {code}')
            codeNotFound.append(code)
    if codeNotFound.__len__() > 0:
        print(f'Số thủ tục không tìm thấy: {codeNotFound.__len__()}')
        print(codeNotFound)


def updateProcedure():
    database_name = 'svcBasepad'  # Replace with your database name
    collection_name = 'procedure'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    level = {'id': ObjectId('5f5b2c2b4e1bd312a6f3ae23'), 'code': 'MUCDO_2', 'name': [
        {'languageId': 228, 'name': 'Một phần'}]}
    query = {"sector.id":ObjectId("62b169356d754349f444bdb5")}
    results = collection.find(query)
    count = 0
    for result in results:
        count += 1
        print(count, result['code'])
        rs = collection.update_one({"_id": ObjectId(result['_id'])}, {
                                   "$set": {"level": level}})
        print(f"{rs.modified_count} thủ tục đã được cập nhật.")
    print(count)
    # results = collection.update_many({"level.id": ObjectId(
    #     "5f5b2c2b4e1bd312a6f3ae23")}, {"$set": {"level": level}})
    # print(f"{results.modified_count} tài liệu đã được cập nhật.")

def updateAgency():
    database_name = 'svcBasedata'  # Replace with your database name
    collection_name = 'agency'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    code = '000.00.05.H12'
    level_id_to_find = ObjectId("5f39f42d5224cf235e134c5a")
    capTinh = {'id': ObjectId('5f39f4335224cf235e134c5b'), 'name': [
        {'languageId': 228, 'name': 'Cấp Tỉnh'}, {'languageId': 46, 'name': 'Province'}]}
    # document = collection.find({"level.id": level_id_to_find})
    result = collection.update_many({"level.id": level_id_to_find}, {
                                    "$set": {"level": capTinh}})
    print(f"{result.modified_count} tài liệu đã được cập nhật.")


def checkUser():
    database_name = 'svcSysman'  # Replace with your database name
    collection_name = 'account'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    re = 'xhth.'
    result = collection.delete_many(
        {"username.value": {"$regex": re, "$options": "i"}})
    count = 0
    print(f"{result.deleted_count} tài liệu đã được xóa.")


def insertUser():
    database_name = 'svcHuman'  # Replace with your database name
    collection_name = 'user'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]

    with open('input/user.json', encoding='utf-8') as f:
        users = json.load(f)
    print(f'Số user cần cập nhật: {users.__len__()}')
    new_created_date = datetime(2023, 10, 3, 9, 0, 0, 0)
    for u in users:
        account = checkUser(u['user'])
        idaccount = str(account['_id'])
        newUser = {
            "_id": ObjectId(),
            "fullname": u['ten'],
            "gender": 1,
            "account": {
                "_id": {
                    "$oid": idaccount
                },
                "username": account['username'],
                "verificationLevel": 1,
                "enable": True
            },
            "email": [
                {
                    "value": u['email'],
                    "status": True,
                    "primary": False
                }
            ],
            "type": 1,
            "usageCount": 0,
            "online": 0,
            "createdDate": new_created_date,
            "isFederatedUser": False,
            "order": 1000,
            "logged": 0,
            "_class": "vn.vnpt.digo.human.document.User"
        }
        result = collection.insert_one(newUser)

    # # In kết quả chèn (ObjectId của tài liệu mới)
        print(f"Đã chèn tài liệu mới với ObjectId: {result.inserted_id}")


def deleteUser():
    database_name = 'svcHuman'  # Replace with your database name
    collection_name = 'user'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    result = collection.delete_many(
        {"account.username.value": {"$regex": "xhth.", "$options": "i"}})
    print(f"{result.deleted_count} tài liệu đã được xóa.")


def deleteAcc():
    database_name = 'svcSysman'  # Replace with your database name
    collection_name = 'account'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    result = collection.delete_many(
        {"username.value": {"$regex": "p7.", "$options": "i"}})
    print(f"{result.deleted_count} tài liệu đã được xóa.")
# In thông tin về việc xóa
    print(f"Số tài liệu đã xóa: {result.deleted_count}")
# Close the MongoDB connection


def updateUsser():
    database_name = 'svcHuman'  # Replace with your database name
    collection_name = 'user'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    regex_pattern = "p7."
    # results = collection.find({"account.username.value": {"$regex": regex_pattern, "$options": "i"}})
    result = collection.update_many({"account.username.value": {
                                    "$regex": regex_pattern, "$options": "i"}}, {"$set": {"type": 3}})
    print(f"{result.modified_count} tài liệu đã được cập nhật.")


def updateProcedureForm():
    database_name = 'svcBasepad'  # Replace with your database name
    collection_name = 'procedureForm'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    id_to_find = ObjectId("6493ad551463284dbab4c288")
    banSao = {'type': {'id': ObjectId('623462c0f2e2ad4ed5787167'), 'name': [
        {'languageId': 228, 'name': 'Bản sao'}]}, 'quantity': 1}
    forms = []
    forms.append(banSao)
    results = collection.update_many(
        {"$expr": {"$eq": [{"$size": "$detail"}, 0]}}, {"$set": {"detail": forms}})
    print(f"{results.modified_count} tài liệu đã được cập nhật.")


def process(codePro, namePro):
    database_name = 'svcBpm'  # Replace with your database name
    # Replace with your collection name
    collection_name = 'processDefinition'
    database = client[database_name]
    collection = database[collection_name]
    #
    results = collection.update_one(
        {"code": codePro}, {"$set": {"name": namePro}})
    print(f"{results.modified_count} tài liệu đã được cập nhật.")


def capNhatTenQt(codePro):
    database_nameQt = 'svcBpm'
    collection_nameQt = 'processDefinition'
    databaseQt = client[database_nameQt]
    collectionQt = databaseQt[collection_nameQt]

    database_name_task = 'svcBpm'
    collection_name_task = 'processDefinitionTask'
    database_task = client[database_name_task]
    collection_task = database_task[collection_name_task]

    database_name = 'svcBasepad'
    collection_name = 'procedureProcessDefinition'
    database = client[database_name]
    collection = database[collection_name]

    qts = collectionQt.find({"code": codePro})
    for qt in qts:
        id = qt['_id']
        name = qt['name']
        print(name)
        pro = collection.update_many({"processDefinition.id": ObjectId(id)}, {
                                     "$set": {"processDefinition.name": name}})
        print(f"{pro.modified_count} thủ tục đã được cập nhật.")
        task = collection_task.update_many({"processDefinition.id": ObjectId(id)}, {
                                           "$set": {"processDefinition.name": name}})
        print(f"{task.modified_count} task đã được cập nhật.")


def deleteProcedureProcess(codeProcedure, idProcess):
    database_name = 'svcBasepad'
    collection_name = 'procedureProcessDefinition'
    database = client[database_name]
    collection = database[collection_name]
    count = 0
    for h in huyens:
        rs = collection.delete_many({"$and": [{"procedure.code": codeProcedure}, {
                                    "procedure.agency.id": ObjectId(h['id'])}, {"processDefinition.id": ObjectId(idProcess)}]})
        count += rs.deleted_count
    print(f"{count} quy trình đã được xóa.")

# deleteProcedureProcess("59214", "655050ec530c2a22c6c613b6")

def updateStatusFee(codeDossier):
    database_name = 'svcPadman'
    collection_name = 'dossier'
    database = client[database_name]
    collection = database[collection_name]

    dossier = collection.find_one({"code": codeDossier})
    _id = dossier['_id']

    collectionFee_name = 'dossierFee'
    collectionFee = database[collectionFee_name]
    fee = collectionFee.update_one(
        {"dossier.id": ObjectId(_id)}, {"$set": {"paid": 1}})
    print(f"{fee.modified_count} thủ tục đã được cập nhật.")


def getListAgency():
    database_name = 'svcBasedata'
    collection_name = 'agency'
    database = client[database_name]
    collection = database[collection_name]
    agencies = collection.find({"$and": [{"status": 1}, {"code": {"$exists": True}}, {
                               "code": {"$ne": ""}}, {"parent": {"$exists": True}}]})
    rs = []
    for agency in agencies:
        ag = {
            'id': str(agency['_id']),
            'code': agency['code'],
            'name': agency['name'][0]['name'],
            'level': agency['level']['name'][0]['name'],
            'parent': agency['parent']['code'],
            'nameParent': agency['parent']['name'][0]['name']
        }
        rs.append(ag)
    json_list = json.dumps(rs, indent=4, ensure_ascii=False)
    with open("output/agencies.json", "w", encoding='utf-8') as outfile:
        outfile.write(json_list)

def updateProcost():
    database_name = 'svcBasepad'
    collection_name = 'procost'
    database = client[database_name]
    collection = database[collection_name]

    rs = collection.update_many({"procedure.id": ObjectId("64969cca1463284dbab50345")}, {
                                "$set": {"procedure.id": ObjectId("652dedaae8b67510552d43b4")}})
    print(f"{rs.modified_count} thủ tục đã được cập nhật.")


def updateProcessTask():
    database_name = 'svcBpm'
    collection_name = 'processDefinitionTask'
    database = client[database_name]
    collection = database[collection_name]

    colelectionName_process = 'processDefinition'
    collection_process = database[colelectionName_process]
    re = "1.010816-SLD"
    reName = "Trường hợp 25 ngày"
    # process = collection_process.find(
    #     {"code": {"$regex": re, "$options": "i"}})
    process = collection_process.find({"$and": [{"code": {"$regex": re, "$options": "i"}}, {
                                      "name": {"$regex": reName, "$options": "i"}}]})
    # process = collection_process.update_many({"$and": [{"code": {"$regex": re, "$options": "i"}}, {
    #                                          "name": {"$regex": reName, "$options": "i"}}]}, {"$set": {"processingTime": 25}})
    count = 0
    for p in process:
        count += 1
        print(count, p['code'])
        rs = collection.update_many({"$and": [{"processDefinition.id": ObjectId(p['_id'])}, {
                                    "activiti.name": "Chuyên viên xử lý hô sơ"}]}, {"$set": {"processingTime": 24}})
        print(f"{rs.modified_count} task đã được cập nhật.")



def requiredProcost():
    database_name = 'svcBasepad'
    collection_name = 'procost'
    database = client[database_name]
    collection = database[collection_name]

    rs = collection.update_many({"required": 0}, {"$set": {"required": 1}})
    print(f"{rs.modified_count} thủ tục đã được cập nhật.")


def up():
    database_name = 'svcBasepad'
    collection_name = 'procedure'
    database = client[database_name]
    collection = database[collection_name]

    rs = collection.update_many(
        {}, {"$set": {"extendHCM.enableAllowChangeProcessProcedure": True}})
    print(f"{rs.modified_count} thủ tục đã được cập nhật.")


def capNhatEform():
    database_nameQt = 'svcBpm'
    collection_nameQt = 'processDefinition'
    databaseQt = client[database_nameQt]
    collectionQt = databaseQt[collection_nameQt]

    database_name2 = 'svcBasepad'
    collection_name2 = 'procedureProcessDefinition'
    database2 = client[database_name2]
    collection2 = database2[collection_name2]

    re = '-KKT-'
    eForm = {
        'id': ObjectId('65010eb6cbf908001f61f00b'),
        'name': 'HCC bieu mau thong tin chung'
    }
    query = {"appliedAgency.id":ObjectId("637d7dc1f217d52a06d6d0fa")}
    qts = collectionQt.find(query)
    count = 0
    for qt in qts:
        print(qt['code'])
        count+=1
        id = qt['_id']
        rs = collection2.update_many({"processDefinition.id": ObjectId(id)}, {
                                     "$set": {"applicantEForm": eForm}})
        print(f"{rs.modified_count} quy trình đã được cập nhật.")
    print(count)
        # print(f"{rs.modified_count} quy trình đã được cập nhật.")
    # qts = collectionQt.update_many({'code': {'$regex': re, '$options': 'i'}}, {'$set': {'eForm': eForm}})
    # print(f"{qts.modified_count} quy trình đã được cập nhật.")


def capNhatTassk():
    database_name = 'svcBpm'
    collection_name = 'processDefinitionTask'
    database = client[database_name]
    collection = database[collection_name]

    # rs = collection.update_many({"isLast":1}, {"$set": {"variable.mustAttachResultsFile":1}})
    re = collection.update_many({}, {"$set": {"variable.mustAttachResultsFile":1}})
    print(f"{rs.modified_count} task đã được cập nhật.")

def upDateEformDosser():
    database_name = 'svcPadman'
    collection_name = 'dossier'
    database = client[database_name]
    collection = database[collection_name]
    re = '000.00.12.H12-231103-0203'
    # reNation = 'G22'
    # query = {'$and': [{'code': {'$regex': re, '$options': 'i'}},
    #                                     #  {'applicant.eformId':ObjectId('65010eb6cbf908001f61f00b')}, 
    #                                     {'nationCode': {'$regex': reNation, '$options': 'i'}}]}
    query = {'code': {'$regex': re,'$options': 'i'}}

    dossiers = collection.find(query)
    count = 0
    for dosser in dossiers:
        id = dosser['_id']
        rs = collection.update_one({"_id": ObjectId(id)}, {
                                     "$set": {"procedureProcessDefinition.processDefinition.applicantEForm.id": ObjectId('65010eb6cbf908001f61f00b'), 
                                                        'procedureProcessDefinition.processDefinition.applicantEForm.name': 'HCC bieu mau thong tin chung',
                                                        'applicant.eformId':ObjectId('65010eb6cbf908001f61f00b')}})
        count += 1
    print(f"{count} hồ sơ đã được cập nhật.")
# upDateEformDosser()


def cancelOTP(username):
    database_name = 'svcSysman'
    collection_name = 'account'
    database = client[database_name]
    collection = database[collection_name]

    rs = collection.update_one({"username.value": username}, {"$set": {"otp.enable": False}})
    print(f"{rs.modified_count} tài khoản đã được cập nhật.")
# cancelOTP("************")

def fix():
    database_name = 'svcPadman'
    collection_name = 'dossier'
    database = client[database_name]
    collection = database[collection_name]
    
    # rss = collection.find({"dossierReceivingKind.id":ObjectId("613b067e38fec268b80879cf")})
    query = {"dossierReceivingKind.id":ObjectId("613b067e38fec268b80879cf")}
    rss = collection.update_many(query, {"$set": {"dossierReceivingKind.id":ObjectId("5f8968888fffa53e4c073ded"), "dossierReceivingKind.name.0.name":"Nhận trực tiếp"}})
    print(f"{rss.modified_count} hồ sơ đã được cập nhật.")


#Thường dùng
def huyQT(codeProcedure, codeProcess):
    database_name = 'svcBasepad'
    collection_name = 'procedureProcessDefinition'
    database = client[database_name]
    collection = database[collection_name]

    database_name_qt = 'svcBpm'
    collection_name_qt = 'processDefinition'
    database_qt = client[database_name_qt]
    collection_qt = database_qt[collection_name_qt]

    
    # query = {"$and": [{"procedure.code": codeProcedure}, {"processDefinition.id": ObjectId(codeProcess)}]}
    query = {"procedure.code": codeProcedure}
    # pro = collection.update_many(query, {"$set": {"status": 0, 'isApplied':0}})
    pro = collection.update_many({"$and":{{"procedure.code": codeProcedure}}}, {"$set": {"status": 0, 'isApplied':0}})
    print(f"{pro.modified_count} quy trình đã được cập nhật.")
# huyQT("2.000286.000.00.00.H12", "6532223f3f01eb5b27e72176")

def requiredFee():
    database_name = 'svcPadman'
    collection_name = 'dossierFee'
    database = client[database_name]
    collection = database[collection_name]

    results = collection.update_many({"$and": [{"paid": 0}, {"required": 0}, {
                                     "amount": {"$gt": 0}}]}, {"$set": {"required": 1}})
    print(f"{results.modified_count} phí đã được cập nhật.")

def updateAssTask(userValueOld, userValueNew):
    database_name = 'svcPadman'
    collection_name = 'dossier'
    database = client[database_name]
    collection = database[collection_name]

    database_name_human = 'svcHuman'
    collection_name_user = 'user'
    database_user = client[database_name_human]
    collection_user = database_user[collection_name_user]

    user = collection_user.find_one({'account.username.value': userValueNew})
    idUserNew = user['_id']
    accountIdNew = str(user['account']['id'])
    fullNameNew = user['fullname']

    userOld = collection_user.find_one({'account.username.value': userValueOld})
    idUserOld = userOld['_id']
    # idUserOld = "6531f8350ec8eb131c3ca671"
    print(idUserOld)

    # reCode = '000.00.20.H12'
    # doser = collection.find({'$and': [{'task.assignee.id': ObjectId(idUserOld)}, 
    #                                   {'code': {'$regex': reCode,'$options': 'i'}}]})
    # doser = collection.find({'task.candidateUser.id': ObjectId(idUserOld)})
    # query = {"candidateUser":{"$elemMatch":{"id":ObjectId(idUserOld)}}}
    query = {'task.assignee.id': ObjectId(idUserOld)}
    # query = {'code':"000.00.25.H12-231027-0202"}
    doser = collection.find(query)
    count = 0
    for d in doser:
        count += 1
        id = d['_id']
        print(count, d['code'])

        # rsTask = collection.update_one({"_id": ObjectId(id), "task.isCurrent":1},
        #                                {"$set": {"task.$.assignee.id": ObjectId(idUserNew),
        #                                          "task.$.assignee.account.id": ObjectId(accountIdNew),
        #                                          "task.$.assignee.account.username.0.value": userValueNew,
        #                                          "task.$.assignee.fullname": fullNameNew}}),
        rsTask = collection.update_one({"_id": ObjectId(id),"task": {"$elemMatch": {"assignee.id": ObjectId(idUserOld)}}}, 
                                    {"$set": {"task.$.assignee.id": ObjectId(idUserNew),
                                             "task.$.assignee.account.id": ObjectId(accountIdNew), 
                                             "task.$.assignee.account.username.0.value": userValueNew,
                                             "task.$.assignee.fullname": fullNameNew}})
        print(f"{rsTask.modified_count} task đã được cập nhật.")
        rsCurrentTask = collection.update_one({"_id": ObjectId(id),"currentTask": {"$elemMatch": {"assignee.id": ObjectId(idUserOld)}}}, 
                                    {"$set": {"currentTask.$.assignee.id": ObjectId(idUserNew),
                                             "currentTask.$.assignee.account.id": ObjectId(accountIdNew), 
                                             "currentTask.$.assignee.account.username.0.value": userValueNew,
                                             "currentTask.$.assignee.fullname": fullNameNew}})
        print(f"{rsCurrentTask.modified_count} currentTask đã được cập nhật.")
# updateAssTask(userValueOld="ubtpcm.cbldtbxh",userValueNew="************")

def doiPhongban(codeDossier, idAgency):
    database_name = 'svcPadman'
    collection_name = 'dossier'
    database = client[database_name]
    collection = database[collection_name]

    database_name_dv = 'svcBasedata'
    collection_name_dv = 'agency'
    database_dv = client[database_name_dv]
    collection_dv = database_dv[collection_name_dv]

    agen = collection_dv.find_one(
        {'_id': ObjectId(idAgency)})
    

    agen_ = {
        'id': agen['_id'],
        'name': agen['name'],
        'parent': agen['parent'],
        'ancestors': agen['ancestors'],
    }
    agency = {
        'id': agen['_id'],
        'code':agen['parent']['code'],
        'name': agen['name'],
        'parent': agen['parent'],
        'ancestors': agen['ancestors'],
        'tag':agen['tag']
    }
    groupAg = []
    groupAg.append(agen_)
    # query ={'$and':[{'code': {'$regex': reCode,'$options': 'i'}}, {'procedure.code':'1.001978.000.00.00.H12'}, {'dossierStatus.id':4}]}
    query = {'code': codeDossier}
    dosser = collection.find(query)
    count = 0
    print(agen_['name'])
    for ds in dosser:
        count+=1
        rs = collection.update_one({"_id": ObjectId(ds['_id']), "task": {"$elemMatch": {"isCurrent": 1}}},
                                    {"$set": {"task.$.candidateGroup": groupAg, "task.$.agency": agen_}})
        print(f"{rs.modified_count} task đã được cập nhật.")
        CurentRs = collection.update_one({"_id": ObjectId(ds['_id']), "currentTask": {"$elemMatch": {"isCurrent": 1}}},
                                    {"$set": {"currentTask.$.candidateGroup": groupAg, "currentTask.$.agency": agen_}})
        print(f"{CurentRs.modified_count} currentTask đã được cập nhật.")
        # rsAg = collection.update_one({"_id": ObjectId(ds['_id'])},
        #                            {"$set": {"agency": agency}})
        # print(f"{rsAg.modified_count} hồ sơ đã được cập nhật.")
    # dosser = collection.update_one({'code': {'$regex': reCode,'$options': 'i'}}, 
    #                                {'$set': {
    #                                dosser['task'][3]['agency']: agen_}})
# doiPhongban("2000269533-002-09-20240408-0020", '6600ddf32104ac22d67c3f02') #Cà Mau
# doiPhongban("2000269533-002-02-20240402-0005", '660382cc2104ac22d67c3f5f') #Đầm Dơi

def removeDongBoQG():
    database_name = 'svcPadman'
    collection_name = 'dossier'
    database = client[database_name]
    collection = database[collection_name]
    query = {
    "$and": [
        {'sync.note': "Không đồng bộ do thủ tục không có nationCode"}
        ]
    }

    #db.getCollection("dossier").find({ "procedure.code": { $in: [ "13709", "7702" ] } }).limit(1000).skip(0)
    query2 = {
    "$and": [
        {'procedure.code': { "$in": ["13709","59214","7702","HUY.1.004203.000.00.00.H12","1.004227.000.00.00.H12.HUY","HUY.2.000889.000.00.00.H12","2.000488.000.00.00.H12","LVNGOAIVU81","LVNGOAIV7","STN-DDBD-01","CMU-291172","CMU-01","CMU-02"]}}
        ]
    }
    # Update operation to add dossierConvert
    update_action = {
        '$set': {"sync.id": 1}}
    
    
    collection.update_many(query, update_action)
    collection.update_many(query2, update_action)
    # Print the number of documents updated
    
    print(f"{collection.count_documents(query)} hồ sơ đã được cập nhật.")
    print(f"{collection.count_documents(query2)} hồ sơ đã được cập nhật.")
# removeDongBoQG()

def rename_keys(original_dict, renaming_map):
    # Debugging: Print the type and value of original_dict
    # print("Type of original_dict:", type(original_dict))
    # print("Value of original_dict:", original_dict)

    # Convert to dictionary if it's a string
    if isinstance(original_dict, str):
        try:
            original_dict = json.loads(original_dict)
        except json.JSONDecodeError:
            raise ValueError("String input must be a valid JSON")

    new_dict = {}
    for key, value in original_dict.items():
        new_key = renaming_map.get(key, key)
        new_dict[new_key] = value
    return new_dict

def postAPI(url, data):
    headers = {
        'Content-Type': 'application/json',
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    return response.json()

def dongBoTrangThaiThanhToanDVCQG (maHoSo):
    renaming_map = {
        "maTraCuuTT": "MaTraCuuTT",
        "maHoSo": "MaHoSo",
        "hoTenNguoiNopTien": "HoTenNguoiNopTien",
        "soCMTNguoiNopTien": "SoCMTNguoiNopTien",
        "diaChiNguoiNopTien": "DiaChiNguoiNopTien",
        "tinhNguoiNopTien": "TinhNguoiNopTien",
        "huyenNguoiNopTien": "HuyenNguoiNopTien",
        "xaNguoiNopTien": "XaNguoiNopTien",
        "thongTinThanhToan": "ThongTinThanhToan",
        "maPhiLePhi": "MaPhiLePhi",
        "thoiGianThanhToan": "ThoiGianThanhToan",
        "soTien": "SoTien",
        "trangThaiThanhToan": "TrangThaiThanhToan",
        "urlBienLai": "UrlBienLai"
    }

    
    database_name = 'svcAdapter'
    collection_name = 'integratedLogs'
    database = client[database_name]
    collection = database[collection_name]
    data = {}
    search_criteria = {
    "$and": [
            {"item.code": maHoSo},
            {"message": re.compile(".*PaymentPlatformConfirmMessage\":\"Received confirm payment with param.*", re.IGNORECASE)}
        ]
    }
  
    results = collection.find(search_criteria).limit(1).skip(0)

    if results:
        data = {}
        try:
            data = results[0].get('data')
        except:
            print("Hồ sơ chưa được đồng bộ về cổng Tỉnh")
            exit()

        # print(data)
        updated_data = rename_keys(data,renaming_map)
        # updated_data.ThongTinThanhToan = capitalize_keys(updated_data.thongTinThanhToan)
        # print(updated_data)
        thongtinThanhToan = updated_data.get('ThongTinThanhToan')
        # get value from array json
        thongtinThanhToanNew = []
        
        for item in thongtinThanhToan:
            r = rename_keys(item,renaming_map)
            # print("obj cũ:",r)
            
            
            #check phi
            urlCheckPhi = f"https://ketnoi.dichvucongcamau.gov.vn/ad/payment-platform/dptracuuthanhtoanhs?config-id=642fd00ee3c04a09c2339753&deploymentId=60ff693e8dbda12b96b63a4b"
            dataCheckPhi = {
                "MaHoSo": maHoSo
            }
            rsCheckPhi = postAPI(urlCheckPhi,dataCheckPhi)
            bodyCheckPhi = rsCheckPhi.get('message')
            # print("bodyCheckPhi: ",bodyCheckPhi)
            PhiLePhiCongQG = bodyCheckPhi.get('PhiLePhi')
            # print("PhiLePhiCongQG: ",PhiLePhiCongQG)
            for item1 in PhiLePhiCongQG:
                MaPhiLePhiQG = item1.get('MaPhiLePhi')
                # print("MaPhiLePhiQG: ",MaPhiLePhiQG)
                #update MaPhiLePhi to r
                r['MaPhiLePhi'] = MaPhiLePhiQG
                
            thongtinThanhToanNew.append(r)

            # print('obj mới:',thongtinThanhToanNew)

        updated_data.get('ThongTinThanhToan').clear()
        updated_data.get('ThongTinThanhToan').extend(thongtinThanhToanNew)
        print(updated_data)
        # Call API đồng bộ lại
        url = f"https://ketnoi.dichvucongcamau.gov.vn/ad/payment-platform/dpnhankqthanhtoanhs?config-id=642fd00ee3c04a09c2339753&deploymentId=60ff693e8dbda12b96b63a4b"
        rs = postAPI(url,updated_data)
        print(rs.get('message'))

    else:
        print("Hồ sơ chưa được đồng bộ về cổng Tỉnh")
# dongBoTrangThaiThanhToanDVCQG("000.21.21.H12-***********")

def updatePublicAdmin():
    database_name = 'svcBasepad'
    collection_name = 'procedure'
    database = client[database_name]
    collection = database[collection_name]
    query = {
    "$and": [
        {'agency.id': ObjectId("637d7dc1f217d52a06d6d0f6")}
        ]
    }

    # Update action put array object publicAdmin
    update_action = {
        '$set': {"publicAdmin": [
            {
                "id":ObjectId("660d0e69d17179e81af01d11"),
                "code": "SOBIENLAI_SXD",
                "name": "Sổ theo dõi biên lai giấy Sở Xây Dựng",
                "status": 1
            }
        ]}}
    
    collection.update_many(query, update_action)
    # Print the number of documents updated
    
    print(f"{collection.count_documents(query)} thủ tục đã được cập nhật.")
# updatePublicAdmin()
    
def updateProceadmin():
    database_name = 'svcBasepad'
    collection_name = 'procedure'
    database = client[database_name]
    collection = database[collection_name]
    query = {
    "$and": [
        {'agency.id': ObjectId("637d7dc1f217d52a06d6d0f6")}
        ]
    }

    # Update action put array object publicAdmin
    update_action = {
        '$set': {"proceAdmin": []}}
    
    collection.update_many(query, update_action)
    # Print the number of documents updated
    
    print(f"{collection.count_documents(query)} thủ tục đã được cập nhật.")
# updateProceadmin()

def checkBatBuoc():
    #read file excel
    df = pd.read_excel('input/maHoSo.xlsx')
    # get list code
    listCode = df['code'].tolist()
    for code in listCode:
        #get id
        database_name = 'svcPadman'
        collection_name = 'dossier'
        database = client[database_name]
        collection = database[collection_name]
        query = {'code': code.strip()}
        dossier = collection.find_one(query)
        if dossier:
            id = dossier['_id']
            #update set required = 1 in dossierFee collection
            collectionFee_name = 'dossierFee'
            collectionFee = database[collectionFee_name]
            rs = collectionFee.update_many({"dossier.id": ObjectId(id)}, {"$set": {"required": 1}})
            print(f"{rs.modified_count} khoảng phí của hồ sơ {code} đã được cập nhật.")
        else:
            print(f"Hồ sơ {code} không tồn tại.")


#define main function with optional call function
def main():
    #create menu list function
    menu = {
        1: updateAgency,
        2: checkUser,
        3: insertUser,
        4: deleteUser,
        5: updateUsser,
        6: updateProcedureForm,
        7: process,
        8: capNhatTenQt,
        9: deleteProcedureProcess,
        10: updateStatusFee,
        11: getListAgency,
        12: updateProcost,
        13: updateProcessTask,
        14: requiredProcost,
        15: up,
        16: capNhatEform,
        17: capNhatTassk,
        18: upDateEformDosser,
        19: cancelOTP,
        20: fix,
        21: huyQT,
        22: requiredFee,
        23: updateAssTask,
        24: doiPhongban,
        25: removeDongBoQG,
        26: dongBoTrangThaiThanhToanDVCQG,
        27: updatePublicAdmin,
        28: updateProceadmin,
        29: checkBatBuoc
    }
    # print menu
    print("Chọn chức năng:")
    for key, value in menu.items():
        print(f"{key}. {value.__name__}")
    # get input from user
    choice = int(input("Nhập số chức năng: "))
    # check parameter
    if choice == 1:
        parameter = input("Nhập mã cơ quan: ")
        menu[choice](parameter)
    elif choice == 7:
        codePro = input("Nhập mã quy trình: ")
        namePro = input("Nhập tên quy trình: ")
        menu[choice](codePro, namePro)
    elif choice == 8:
        codePro = input("Nhập mã quy trình: ")
        menu[choice](codePro)
    elif choice == 9:
        codeProcedure = input("Nhập mã thủ tục: ")
        idProcess = input("Nhập id quy trình: ")
        menu[choice](codeProcedure, idProcess)
    elif choice == 10:
        codeDossier = input("Nhập mã hồ sơ: ")
        menu[choice](codeDossier)
    elif choice == 12:
        menu[choice]()
    elif choice == 18:
        menu[choice]()
    elif choice == 19:
        menu[choice]()
    elif choice == 23:
        userValueOld = input("Nhập tên tài khoản cũ: ")
        userValueNew = input("Nhập tên tài khoản mới: ")
        menu[choice](userValueOld, userValueNew)
    elif choice == 24:
        codeDossier = input("Nhập mã hồ sơ: ")
        idAgency = input("Nhập mã cơ quan: ")
        menu[choice](codeDossier, idAgency)
    elif choice == 25:
        menu[choice]()
    elif choice == 26:
        maHoSo = input("Nhập mã hồ sơ: ")
        menu[choice](maHoSo)
    else:
        menu[choice]()

    # print result    
   
    

# Call the main function
if __name__ == "__main__":
    main()
    # Close the MongoDB connection
    

client.close()
