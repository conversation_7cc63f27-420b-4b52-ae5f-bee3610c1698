import pandas as pd
import json


def main(name):
    # Đọc dữ liệu từ file Excel
    # name = 'tvt'
    excel_data_df = pd.read_excel('v1/'+name+'.xlsx', sheet_name='Sheet1')

    # Chuyển đổi dữ liệu Excel thành chuỗi JSON
    json_str = excel_data_df.to_json(orient='records')

    # In kết quả
    print('Dữ liệu Excel đã được chuyển đổi thành JSON:\n', json_str)

    # Chuyển đổi chuỗi JSON thành danh sách để có thể nhập vào file JSON
    json_data = json.loads(json_str)

    # Định nghĩa file để ghi và chọn tùy chọn 'w' để ghi -> json.dump()
    # định nghĩa danh sách để ghi từ và file để ghi vào
    with open('v1/'+name+'.json', 'w') as json_file:
        json.dump(json_data, json_file)
        print('Đã ghi dữ liệu JSON vào file thành công.')


if __name__ == '__main__':
    user_input = input("Nhập tên file excel: ")
    main(user_input)

