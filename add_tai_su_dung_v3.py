from pymongo import MongoClient, DESCENDING, ASCENDING
from bson.objectid import ObjectId
import json

def get_log_by_code(code):
    mongo_uri = '**********************************************************************************************'
    client = MongoClient(mongo_uri)
    db = client['svcAdapter']
    collection = db['integratedLogs']
    serviceId="5f7c16069abb62f511890030"
    # query = {"service.id": ObjectId(serviceId), "item.code": code}
    #db.getCollection("integratedLogs").find({ $and : [{"item.code" : "000.00.14.H12-240117-0003"}, {"data" : /.*DongBoHoSoMC.*/i}, {"data" : /.*"isUpdating":"true".*/i}] }).sort({createdDate:-1})
    query = {"service.id": ObjectId(serviceId), "item.code": code, "data" : { "$regex" : ".*DongBoHoSoMC.*", "$options" : "i" }, "data" : { "$regex" : ".*\"isUpdating\":\"true\".*", "$options" : "i" }}
    ret = list(collection.find(query).sort("createdDate", DESCENDING))
   

    if len(ret) == 0:
        return None
    else:
        print(ret[0]['data'])
        return json.loads(ret[0]['data'])
    # for i in range(len(ret)):
    #     if ret[i]['data'] and "DongBoHoSoMC" in ret[i]['data'] and "\"isUpdating\":\"false\"" in ret[i]['data']:
    #         newValue = {}
    #         try:
    #             realmRoles = json.loads(ret[i]['data'])
    #             newValue['data'] = realmRoles
    #         except Exception as ex:
    #             newValue['data'] = ret[i]['data']
    #         newValue['message'] = ret[i]['message']
    #         newValue['createdDate'] = ret[i]['createdDate']
    #         if ret[i]['message'] and "\"error_code\":\"0\"" in ret[i]['message'] and checkCreated != 2:
    #             createdValue = newValue
    #             checkCreated = 2
    #         if (not ret[i]['message'] or ("\"error_code\":\"0\"" not in ret[i]['message'])) and checkCreated == 0:
    #             createdValue = newValue
    #             checkCreated = 1
    #     #get updated value last
    #     if ret[i]['data'] and "DongBoHoSoMC" in ret[i]['data'] and "\"isUpdating\":\"true\"" in ret[i]['data']:
    #         newValue = {}
    #         try:
    #             realmRoles = json.loads(ret[i]['data'])
    #             newValue['data'] = realmRoles
                
    #         except Exception as ex:
    #             newValue['data'] = ret[i]['data']
    #         newValue['message'] = ret[i]['message']
    #         newValue['createdDate'] = ret[i]['createdDate']
    #         if ret[i]['message'] and "\"error_code\":\"0\"" in ret[i]['message'] and checkCreated != 2:
    #             updatedValue = newValue
    #             checkUpdate = 2
    #         if (not ret[i]['message'] or ("\"error_code\":\"0\"" not in ret[i]['message'])) and checkCreated == 0:
    #             updatedValue = newValue
    #             checkUpdate = 1

    #     if ret[i]['data'] and "CapNhatTienDoHoSoMC" in ret[i]['data']:
    #         newValue = {}
    #         try:
    #             realmRoles = json.loads(ret[i]['data'])
    #             newValue['data'] = realmRoles
    #         except Exception as ex:
    #             newValue['data'] = ret[i]['data']
    #         newValue['message'] = ret[i]['message']
    #         newValue['createdDate'] = ret[i]['createdDate']
    #         if ret[i]['message'] and "\"error_code\":\"0\"" in ret[i]['message'] and checkCreated != 2:
    #             statusValue = newValue
    #             checkStatus = 2
    #         if (not ret[i]['message'] or ("\"error_code\":\"0\"" not in ret[i]['message'])) and checkCreated == 0:
    #             statusValue = newValue
    #             checkStatus = 1

    if type == 0:
        newList.append(createdValue)
        newList.append(updatedValue)
        newList.append(statusValue)
    elif type == 1:
        newList.append(createdValue)
    elif type == 2:
        newList.append(updatedValue)
    elif type == 3:
        newList.append(statusValue)
    
    print(newList)
    return newList

def converterDto(data):
    dataNew = {}
    if data:
        try:
            dataNew = {
                    "session": data['session'],
                    "madonvi": data['madonvi'],
                    "service": data['service'],
                    "isUpdating": data['isUpdating'],
                    "data": [
                        {
                            "MaHoSo": data['data'][0]['code'],
                            "MaTTHC": data['data'][0]['procedureCode'],
                            "TenTTHC": data['data'][0]['procedureName'],
                            "MaLinhVuc": data['data'][0]['sectorCode'],
                            "TenLinhVuc": data['data'][0]['sectorName'],
                            "KenhThucHien": data['data'][0]['receptionMethod'],
                            "ChuHoSo": data['data'][0]['applicantName'],
                            "LoaiDoiTuong": data['data'][0]['applicantType'],
                            "MaDoiTuong": data['data'][0]['applicantCode'],
                            "ThongTinKhac": data['data'][0]['applicantOtherInfo'],
                            "Email": data['data'][0]['applicantEmail'],
                            "Fax": data['data'][0]['applicantFax'],
                            "SoDienThoai": data['data'][0]['applicantPhoneNumber'],
                            "TrangThaiHoSo": data['data'][0]['status'],
                            "NgayTiepNhan": data['data'][0]['receptedDate'],
                            "NgayHenTra": data['data'][0]['appointmentDate'],
                            "NgayTra": data['data'][0]['completedDate'],
                            "HinhThuc": data['data'][0]['returnedMethod'],
                            "HoSoCoThanhPhanSoHoa": data['data'][0]['dossierDigitizing'],
                            "TaiKhoanDuocXacThucVoiVNeID": data['data'][0]['accountAuthenticatedWithVNeID'],
                            "DuocThanhToanTrucTuyen": data['data'][0]['onlinePayment'],
                            "DinhDanhCHS": [
                                {
                                    "LoaiDinhDanh": data['data'][0]['ownerAuthenticated'][0]['identityType'],
                                    "SoDinhDanh": data['data'][0]['ownerAuthenticated'][0]['identityNumber']
                                }
                            ],
                            "NgayNopHoSo": data['data'][0]['submissionDate'],
                            "DSKetNoiCSDL": [
                                {
                                    "MaCSDL": data['data'][0]['ConnectedDatabase'][0]['codeCSDL']
                                }
                            ],
                            "NoiNopHoSo": data['data'][0]['submissionPlace'],
                            "DonViXuLy": data['data'][0]['handlingAgency'],                            
                            "NgayYeuCau": data['data'][0]['requestDateCreated'] if 'requestDateCreated' in data['data'][0] else "null",
                            "NgayTuChoi": data['data'][0]['cancelDateCreated'] if 'cancelDateCreated' in data['data'][0] else "null",
                        }
                    ]
                }
            

            #TaiLieuNop
            TaiLieuNop = []
            if 'attachment' in data['data'][0]:
                for item in data['data'][0]['attachment']:
                    TaiLieuNop.append(
                        {
                            "TenTepDinhKem": item['name'],
                            "DuongDanTaiTepTin": item['fileLink'],
                            "DuocSoHoa": "1",
                            "DuocTaiSuDung": " 1",
                            "DuocLayTuKhoDMQG": "0"
                        })
            dataNew['data'][0]['TaiLieuNop'] = TaiLieuNop

            # add DanhSachLePhi
            # get all fees after add new array after add to dataNew
            DanhSachLePhi = []
            if 'fees' in data['data'][0]:
                for item in data['data'][0]['fees']:
                    DanhSachLePhi.append(
                        {"TenPhiLePhi": item['name'],
                        "MaPhiLePhi": item['code'],
                        "HinhThucThu": item['feeMethod'],
                        "Gia": item['price'],
                        "LoaiPhiLePhi": item['type']
                        }
                    )
            dataNew['data'][0]['DanhSachLePhi'] = DanhSachLePhi
            #add DanhSachGiayToKetQua
            # get all result after add new array after add to dataNew
            DanhSachGiayToKetQua = []
            if 'result' not in data['data'][0]:
                dataNew['data'][0]['DanhSachGiayToKetQua'] = DanhSachGiayToKetQua
            else:
                for item in data['data'][0]['result']:
                    DanhSachGiayToKetQua.append(
                        {
                            "TenGiayTo": item['name'],
                            "DuongDanTepTinKetQua": item['fileLink'],
                            "MaGiayToKetQua": item['resultCode'],
                        }
                    )    
                dataNew['data'][0]['DanhSachGiayToKetQua'] = DanhSachGiayToKetQua

            #add DanhSachHoSoBoSung
            DanhSachHoSoBoSung = []
            #check if additionalDossiers is exist in data
            if 'additionalDossiers' not in data['data'][0]:
                dataNew['data'][0]['DanhSachHoSoBoSung'] = DanhSachHoSoBoSung
                return dataNew
            else:
                for item in data['data'][0]['additionalDossiers']:
                    DanhSachHoSoBoSung.append(
                        {
                            "HoSoBoSungId": item['idAdditional'],
                            "NguoiYeuCauBoSung": item['name'],
                            "NoiDungBoSung": item['content'],
                            "NgayBoSung": item['additionalDate'],
                            "NguoiTiepNhanBoSung": item['additionalAcceptedName'],
                            "NgayTiepNhanBoSung": item['additionalAcceptedDate'],
                            "TrangThaiBoSung": item['status'],
                            "NgayHenTraTruoc": item['preAppointmentDate'],
                            "NgayHenTraMoi": item['laterAppointmentDate']
                        }
                    )
                dataNew['data'][0]['DanhSachHoSoBoSung'] = DanhSachHoSoBoSung
           
            
        except Exception as ex:
            print(ex)
            return None
    return dataNew

#main
def main():
    data = get_log_by_code("000.00.22.H12-***********")
    if data:
        dataNew = converterDto(data)
        print(dataNew)
    else:
        print("Not found data")
main()

