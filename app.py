from flask import Flask, render_template, request, flash
import mysql.connector
import requests
from datetime import datetime
from contants import *

app = Flask(__name__)
app.secret_key = '21312321321321312312312321321321321321321'

@app.route('/', methods=['GET', 'POST'])
def index():
    api_endpoint = 'https://dichvucong.gov.vn/jsp/rest.jsp'
    if request.method == 'POST':
        year = request.form['year']
        # quarter = request.form['quarter']
        # month = request.form['month']
        service = request.form['service']
        # tinh_id = request.form['tinh_id']
        # huyen_id = request.form['huyen_id']

        try:
            # Connect to MySQL
            # Connect to the database
            cnx = mysql.connector.connect(
                user='root',
                password='root',
                host='localhost',
                database='dvc'
            )
            cursor = cnx.cursor()
            #Get data from huyen table
            query = ("SELECT * FROM huyen")
            cursor.execute(query)
            huyen_data = cursor.fetchall()
            #add elemet to huyen_data
            huyen_data.append(('', 'Tỉnh Cà Mau',''))
            data_quarter = ['01','02','03','04']
            data_month = ['01','02','03','04','05','06','07','08','09','10','11','12']
            
            if(service==Constants.REPORT_YEAR_SERVICE_PART2):
                #Delete all data in table by year
                query = ("DELETE FROM "+service+" WHERE NAM = "+str(year))
                cursor.execute(query)
                cnx.commit()
                #Get data
                for row in huyen_data:
                    huyen_id = row[0]
                    # Save API response to the database
                    form_data = {
                        'params': '{"type":"ref","p_nam":"'+str(year)+'","p_6thang":0,"p_tinh_id":"11362","p_linhvuc":"0","p_huyen_id":"'+str(huyen_id)+'","p_thutuc_id":"0","pageIndex":1,"pageSize":100,"p_default":0,"p_xa_id":"0","p_quy":"","p_thang":"","service":"' + service +'"}'
                    }

                    # Make the POST request with form data
                    response = requests.post(api_endpoint, data=form_data)
                    if response.status_code == 200:
                        data = response.json()
                    else:
                        print('Error:', response.status_code)
                        exit()  # Exit the script if there was an error

                    for item in data:
                        # Extract the required data from the 'item' dictionary
                        column1_value = item['ID']
                        column2_value = item['MA_COQUAN']
                        column3_value = item['TEN']
                        column4_value = item['TEN_COQUAN']
                        column5_value = item['CAPDONVIID']
                        column6_value = item['LOAI_COQUAN']
                        column7_value = item['GEO_JSON']
                        column8_value = item['TOTAL']
                        column9_value = item['SCORE']
                        column10_value = item['ROW_STT']
                        column11_value = year
                        column12_value = datetime.now()
                        # ... extract other column values as needed
                                
                        # Execute an INSERT query to save the data
                        query = "INSERT INTO report_by_year_service_part2 (ID, MA_COQUAN, TEN, TEN_COQUAN, CAPDONVIID, LOAI_COQUAN, GEO_JSON, TOTAL, SCORE, ROW_STT, NAM, NGAY_TAO) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
                        values = (column1_value, column2_value, column3_value, column4_value, column5_value, column6_value, column7_value, column8_value, column9_value, column10_value, column11_value, column12_value)
                        cursor.execute(query, values)


            elif(service==Constants.REPORT_QUARTER_SERVICE_PART2):
                
                #Get data
                for q in data_quarter:
                    quy = q
                    #Delete all data in table by year
                    query = ("DELETE FROM "+service+" WHERE NAM = "+str(year)+" AND QUY = "+quy)
                    cursor.execute(query)
                    cnx.commit()
                    for row in huyen_data:
                        huyen_id = row[0]
                        # Save API response to the database
                        form_data = {
                            'params': '{"type":"ref","p_nam":"'+str(year)+'","p_6thang":0,"p_tinh_id":"11362","p_linhvuc":"0","p_huyen_id":"'+str(huyen_id)+'","p_thutuc_id":"0","pageIndex":1,"pageSize":100,"p_default":0,"p_xa_id":"0","p_quy":"'+quy+'","p_thang":"","service":"' + service +'"}'
                        }

                        # Make the POST request with form data
                        response = requests.post(api_endpoint, data=form_data)
                        if response.status_code == 200:
                            data = response.json()
                        else:
                            print('Error:', response.status_code)
                            exit()  # Exit the script if there was an error

                        for item in data:
                            # Extract the required data from the 'item' dictionary
                            column1_value = item['ID']
                            column2_value = item['MA_COQUAN']
                            column3_value = item['TEN']
                            column4_value = item['TEN_COQUAN']
                            column5_value = item['CAPDONVIID']
                            column6_value = item['LOAI_COQUAN']
                            column7_value = item['GEO_JSON']
                            column8_value = item['TOTAL']
                            column9_value = item['SCORE']
                            column10_value = item['ROW_STT']
                            column11_value = year
                            column12_value = datetime.now()
                            column13_value = quarter
                            # ... extract other column values as needed
                                    
                            # Execute an INSERT query to save the data
                            query = "INSERT INTO report_by_quarter_service_part2 (ID, MA_COQUAN, TEN, TEN_COQUAN, CAPDONVIID, LOAI_COQUAN, GEO_JSON, TOTAL, SCORE, ROW_STT, NAM, QUY, NGAY_TAO) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
                            values = (column1_value, column2_value, column3_value, column4_value, column5_value, column6_value, column7_value, column8_value, column9_value, column10_value, column11_value, column13_value, column12_value)
                            cursor.execute(query, values)
            else:
                #Get data
                for m in data_month:
                    thang = m
                    #Delete all data in table by year
                    query = ("DELETE FROM "+service+" WHERE NAM = "+str(year)+" AND THANG = "+thang)
                    cursor.execute(query)
                    cnx.commit()
                    for row in huyen_data:
                        huyen_id = row[0]
                        # Save API response to the database
                        form_data = {
                            'params': '{"type":"ref","p_nam":"'+str(year)+'","p_6thang":0,"p_tinh_id":"11362","p_linhvuc":"0","p_huyen_id":"'+str(huyen_id)+'","p_thutuc_id":"0","pageIndex":1,"pageSize":100,"p_default":0,"p_xa_id":"0","p_quy":"","p_thang":"'+thang+'","service":"' + service +'"}'
                        }

                        # Make the POST request with form data
                        response = requests.post(api_endpoint, data=form_data)
                        if response.status_code == 200:
                            data = response.json()
                        else:
                            print('Error:', response.status_code)
                            exit()  # Exit the script if there was an error

                        for item in data:
                            # Extract the required data from the 'item' dictionary
                            column1_value = item['ID']
                            column2_value = item['MA_COQUAN']
                            column3_value = item['TEN']
                            column4_value = item['TEN_COQUAN']
                            column5_value = item['CAPDONVIID']
                            column6_value = item['LOAI_COQUAN']
                            column7_value = item['GEO_JSON']
                            column8_value = item['TOTAL']
                            column9_value = item['SCORE']
                            column10_value = item['ROW_STT']
                            column11_value = year
                            column12_value = datetime.now()
                            column13_value = thang
                            # ... extract other column values as needed
                                    
                            # Execute an INSERT query to save the data
                            query = "INSERT INTO report_by_month_service_part2 (ID, MA_COQUAN, TEN, TEN_COQUAN, CAPDONVIID, LOAI_COQUAN, GEO_JSON, TOTAL, SCORE, ROW_STT, NAM, THANG, NGAY_TAO) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
                            values = (column1_value, column2_value, column3_value, column4_value, column5_value, column6_value, column7_value, column8_value, column9_value, column10_value, column11_value, column13_value, column12_value)
                            
                            cursor.execute(query, values)
            
            flash('Data saved to the database successfully.', 'success')

        except Exception as e:
            flash(f'An error occurred: {str(e)}', 'error')

    return render_template('index.html')

