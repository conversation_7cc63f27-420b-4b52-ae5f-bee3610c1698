import asyncio
import json
from datetime import datetime, timezone
from threading import Thread, Event
import httpx
import pymongo
import ttkbootstrap as ttk
from bson import ObjectId
from openpyxl import Workbook
from ttkbootstrap.constants import *
from tkinter import messagebox
from ttkbootstrap.widgets import DateEntry
import sys
import subprocess

# Kết nối tới MongoDB
mongo_uri = '********************************************************************************************************************************'
client = pymongo.MongoClient(mongo_uri)

# Lấy access token từ API
async def get_token():
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = 'grant_type=password&username=admin&password=iGate#Cmu@2024&client_id=web-onegate'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    try:
        async with httpx.AsyncClient(verify=ssl_context) as client:
            response = await client.post(url, data=payload, headers=headers)
            return response.json()['access_token']
    except Exception as e:
        print(f"Error getting token: {e}")
        return None

# Hàm đồng bộ hóa dữ liệu
async def sync_qg(code, token):
    url = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    }
    payload = json.dumps([code])
    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    try:
        async with httpx.AsyncClient(verify=ssl_context) as client:
            response = await client.post(url, data=payload, headers=headers)
            print(response.text.encode('utf8'))
            return response.status_code == 200 and response.json()['affectedRows'] == 1
    except Exception as e:
        print(f"Error syncing dossier {code}: {e}")
        return False

# Truy vấn database
def get_documents(fromDate, toDate):
    database_name = 'svcPadman'
    collection_name = 'dossier'
    database = client[database_name]
    collection = database[collection_name]

    dayfrom, monthfrom, yearfrom = map(int, fromDate.split("/"))
    dayto, monthto, yearto = map(int, toDate.split("/"))

    start_date = datetime(yearfrom, monthfrom, dayfrom, 0, 0, 0, tzinfo=timezone.utc)
    end_date = datetime(yearto, monthto, dayto, 23, 59, 59, tzinfo=timezone.utc)
    try:
        query = {
            "$and": [
                {"acceptedDate": {"$gte": start_date, "$lte": end_date}},
                {'procedure.code': {"$nin": [
                    "13709", "59214", "7702", "HUY.1.004203.000.00.00.H12",
                    "1.004227.000.00.00.H12.HUY", "HUY.2.000889.000.00.00.H12",
                    "2.000488.000.00.00.H12", "LVNGOAIVU81", "LVNGOAIV7",
                    "STN-DDBD-01", "CMU-291172", "CMU-01", "CMU-02",
                    "CMU-03", "CMU-04", "CMU-05", "CMU-06"
                ]}}
            ]
        }

         # Sử dụng projection để chỉ lấy các trường cần thiết
        projection = {"_id": 1, "code": 1}

        # Thực hiện truy vấn và giới hạn số lượng kết quả
        documents = collection.find(query, projection, no_cursor_timeout=True).limit(100000)

        # Chuyển đổi kết quả thành danh sách và trả về
        result = list(documents)
        return result
        
    except Exception as e:
        print(f"Error retrieving documents: {e}")
        return []

# Cập nhật file
def update_file(dossier_id):
    database_name = 'svcPadman'
    collection_name = 'dossierFormFile'
    database = client[database_name]
    collection = database[collection_name]
    try:
        result = collection.update_many(
            {"dossier.id": ObjectId(dossier_id)},
            {"$set": {"file.$[].reused": 1}}
        )
        print(f"Updated {result.modified_count} documents for dossier {dossier_id}")
        return result.modified_count > 0
    except Exception as e:
        print(f"Error updating file for dossier {dossier_id}: {e}")
        return False

# Kiểm tra hàng đợi
async def check_queue(code_value):
    database_name = 'svcAdapter'
    collection_name = 'integratedReCallEvent'
    database = client[database_name]
    collection = database[collection_name]

    query = {
        "data.dossier.code": code_value,
        "status": 0
        }

    while True:
        # Đếm số lượng tài liệu phù hợp
        count = collection.count_documents(query)
        print(f"Số lượng tài liệu hiện tại: {count}")

        # Kiểm tra nếu số lượng dòng là 0 thì thoát khỏi vòng lặp
        if count == 0:
            print("Không còn tài liệu nào phù hợp.")
            return True
        # Chờ một khoảng thời gian trước khi kiểm tra lại
        await asyncio.sleep(3)  # Kiểm tra lại sau 3 giây


class FileSearchApp(ttk.Frame):
    def __init__(self, master):
        super().__init__(master, padding=10)
        self.pack(fill=BOTH, expand=YES)
        style = ttk.Style()
        style.configure('.', font=('Arial', 10))

        self.pause_event = Event()
        self.stop_event = Event()

        self.create_input_section()
        self.create_progress_section()
        self.create_result_view()

    def create_input_section(self):
        input_frame = ttk.Labelframe(self, text="Thời gian cần xử lý", padding=(10, 5), bootstyle=INFO)
        input_frame.pack(fill=X, pady=10)

        from_date_label = ttk.Label(input_frame, text="Từ ngày:")
        from_date_label.pack(side=LEFT, padx=(0, 5))
        self.from_date_entry = DateEntry(input_frame, dateformat="%d/%m/%Y")
        self.from_date_entry.pack(side=LEFT, fill=X, expand=YES, padx=(0, 5))

        to_date_label = ttk.Label(input_frame, text="Đến ngày:")
        to_date_label.pack(side=LEFT, padx=(15, 5))
        self.to_date_entry = DateEntry(input_frame, dateformat="%d/%m/%Y")
        self.to_date_entry.pack(side=LEFT, fill=X, expand=YES, padx=(0, 5))

        start_button = ttk.Button(input_frame, text="Bắt đầu xử lý...", command=self.start_processing)
        start_button.pack(side=LEFT, padx=(15, 5))
        
        reset_button = ttk.Button(input_frame, text="Reset", command=self.reset)
        reset_button.pack(side=LEFT, padx=(5, 5))

    def create_progress_section(self):
        progress_frame = ttk.Labelframe(self, text="Tiến trình xử lý", padding=(10, 5), bootstyle=PRIMARY)
        progress_frame.pack(fill=X, pady=10)

        self.progressbar = ttk.Progressbar(progress_frame, length=400, mode='determinate', bootstyle="success-striped")
        self.progressbar.pack(fill=X, padx=(0, 5), pady=10)

        self.status_label = ttk.Label(progress_frame, text="Trạng thái xử lý:", anchor=W)
        self.status_label.pack(fill=X)

    def create_result_view(self):
        result_frame = ttk.Labelframe(self, text="Danh sách kết quả", padding=(10, 5), bootstyle=SUCCESS)
        result_frame.pack(fill=BOTH, expand=YES, pady=10)

        self.resultview = ttk.Treeview(
            master=result_frame,
            bootstyle=INFO,
            columns=[0, 1],
            show=HEADINGS
        )
        self.resultview.pack(side=LEFT, fill=BOTH, expand=YES)

        self.resultview.heading(0, text='Mã hồ sơ', anchor=W)
        self.resultview.heading(1, text='Trạng thái', anchor=W)
        self.resultview.column(0, anchor=W, width=150)
        self.resultview.column(1, anchor=W, width=100)

        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.resultview.yview)
        scrollbar.pack(side=RIGHT, fill=Y)
        self.resultview.configure(yscrollcommand=scrollbar.set)

    def start_processing(self):
        self.stop_event.clear()
        self.pause_event.clear()
        Thread(target=self.run_async).start()

    def run_async(self):
        asyncio.run(self.main())

    async def main(self):
        token = await get_token()
        if not token:
            messagebox.showerror("Error", "Failed to get token. Please check logs.")
            return
        from_date = self.from_date_entry.entry.get()
        to_date = self.to_date_entry.entry.get()
        documents = get_documents(from_date, to_date)
        if not documents:
            messagebox.showerror("Error", "No documents found or failed to retrieve documents.")
            return

        wb = Workbook()
        ws = wb.active
        ws.append(["code", "status"])
        total_docs = len(documents)

        for i, document in enumerate(documents):
            if self.stop_event.is_set():
                break  # Exit the loop if stop_event is set

            code = document['code']
            ws.append([code])
            dossier_id = document['_id']
            status = "Thành công"
            try:
                update_file(dossier_id)
                if await sync_qg(code, token):
                    if await check_queue(code):
                        ws.cell(row=i + 2, column=2, value="success")
                        status = "Thành công"
                    else:
                        ws.cell(row=i + 2, column=2, value="fail")
                        status = "Sync QG thất bại"
                else:
                    ws.cell(row=i + 2, column=2, value="fail")
                    status = "API thất bại"
            except Exception as e:
                print(f"Error processing dossier {code}: {e}")
                ws.cell(row=i + 2, column=2, value="error")
                status = "error"

            self.resultview.insert("", "end", values=(code, status))
            self.progressbar['value'] = (i + 1) / total_docs * 100
            self.status_label.config(text=f"Đã xử lý {i + 1}/{total_docs} hồ sơ")
            self.update_idletasks()

        filename = from_date.replace("/", "") + "_" + to_date.replace("/", "") + ".xlsx"
        wb.save(filename)
        messagebox.showinfo("Xử lý xong", f"Danh sách hồ sơ xử lý lưu vào file {filename}")

    def reset(self):
        """Exit the current process and stop ongoing operations"""
        self.stop_event.set()  # Set the stop event to exit the loop
        
         # Clear the progress bar and status label
        self.progressbar['value'] = 0
        self.status_label.config(text="Trạng thái xử lý:")
        # Clear the Treeview results
        self.resultview.delete(*self.resultview.get_children())
        # Optionally, clear the date entries as well
        self.from_date_entry.entry.delete(0, 'end')
        self.to_date_entry.entry.delete(0, 'end')
        # Optionally, you could clear the UI or perform other cleanup here
        messagebox.showinfo("Reset", "Đã dừng xử lý và reset trạng thái")

if __name__ == '__main__':
    app = ttk.Window("Tool hỗ trợ biểu đồ 766", "litera")
    FileSearchApp(app)
    app.mainloop()