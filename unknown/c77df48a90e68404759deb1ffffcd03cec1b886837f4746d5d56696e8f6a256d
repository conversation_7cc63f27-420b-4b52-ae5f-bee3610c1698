import sys
from PyQt5.QtWidgets import QA<PERSON><PERSON>, QWidget, QVBoxLayout, QPushButton, QTextEdit
from PyQt5.QtCore import pyqtSlot, QThreadPool, QRunnable
import httpx
import asyncio

class Worker(QRunnable):
    def __init__(self, url):
        super(Worker, self).__init__()
        self.url = url

    @pyqtSlot()
    def run(self):
        asyncio.run(self.fetch_url())

    async def fetch_url(self):
        async with httpx.AsyncClient() as client:
            response = await client.get(self.url)
            self.update_text(response.text)

    def update_text(self, text):
        # Cập nhật giao diện người dùng tại đây
        print(text)  # Thay thế bằng cách cập nhật giao diện người dùng

class MyWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.threadpool = QThreadPool()

    def initUI(self):
        layout = QVBoxLayout()
        self.textEdit = QTextEdit()
        self.button = QPushButton('Gửi')
        self.button.clicked.connect(self.on_click)

        layout.addWidget(self.textEdit)
        layout.addWidget(self.button)
        self.setLayout(layout)

    def on_click(self):
        url = self.textEdit.toPlainText()
        worker = Worker(url)
        self.threadpool.start(worker)

app = QApplication(sys.argv)
window = MyWindow()
window.show()
sys.exit(app.exec_())
