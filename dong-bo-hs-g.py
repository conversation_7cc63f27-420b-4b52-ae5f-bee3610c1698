import asyncio
import ssl
import json
import httpx
from pymongo import MongoClient, UpdateOne
from datetime import datetime, timedelta, timezone
import random

# Kết nối tới MongoDB
client = MongoClient('mongodb://localhost:27017/')  # Thay đổi URL nếu cần thiết
db = client['svcPadman']
collection = db['dossier']

async def getToken():
    # <PERSON><PERSON><PERSON> định hàm getToken trả về token hợp lệ
    return "your_access_token"

async def syncQG(code):
    # Tạo SSL context tùy chỉnh
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers('DEFAULT:@SECLEVEL=0')

    url = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"
    token = await getToken()
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token,
        'Cookie': 'JSESSIONID=979D1F437D51C126FF15E5BA8BE0E768'
    }
    payload = json.dumps({"code": code})
    
    async with httpx.AsyncClient(timeout=10.0, verify=ssl_context) as client:
        response = await client.post(url, headers=headers, content=payload)

        if response.status_code == 200:
            if response.json().get('affectedRows') == 1:
                return 200
            else:
                return 500
        else:
            return 501

async def update_documents_and_sync():
    # Truy vấn lấy danh sách các tài liệu phù hợp
    query = {
        '$and': [
            { 'nationCode': { '$exists': True } },
            { '$where': 'this.completedDate > this.appointmentDate' },
            { 'createdDate': { '$gt': datetime(2023, 12, 31, 23, 59, 59, tzinfo=timezone.utc) } }
        ]
    }
    documents = collection.find(query)

    # Chuẩn bị các thao tác cập nhật
    updates = []
    codes_to_sync = []  # Danh sách mã code cần đồng bộ

    for document in documents:
        if 'appointmentDate' in document:
            appointment_date = document['appointmentDate']
            
            # Chuyển đổi nếu `appointmentDate` là chuỗi
            if isinstance(appointment_date, str):
                appointment_date = datetime.strptime(appointment_date, '%Y-%m-%dT%H:%M:%S.%fZ')
            
            # Trừ ngẫu nhiên từ 1 đến 4 giờ để tạo `completedDate`
            completedate = appointment_date - timedelta(hours=random.uniform(1, 4))
            
            # Thêm cập nhật vào danh sách `updates`
            updates.append(
                UpdateOne(
                    {"_id": document['_id']},
                    {"$set": {"completedDate": completedate}}
                )
            )
            # Thêm mã code vào danh sách cần đồng bộ
            codes_to_sync.append(document['code'])

    # Thực hiện cập nhật hàng loạt nếu có cập nhật
    if updates:
        result = collection.bulk_write(updates)
        print(f"Updated {result.modified_count} documents with new completedDate values.")

    # Gọi đồng bộ hóa cho các mã code trong danh sách
    sync_results = await asyncio.gather(*[syncQG(code) for code in codes_to_sync])

    # In kết quả của từng mã
    for code, result in zip(codes_to_sync, sync_results):
        print(f"Code {code} sync result: {result}")

def convert_date(date):
    #dd/mm/yyyy HH:MM:SS
    return datetime.strptime(date, '%Y-%m-%dT%H:%M:%S.%f%z').strftime('%d/%m/%Y %H:%M:%S')

# Chạy hàm không đồng bộ
asyncio.run(update_documents_and_sync())
