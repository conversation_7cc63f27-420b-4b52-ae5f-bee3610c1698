import paramiko
from datetime import datetime, timedelta
import time
import json
import random

def ssh_command_with_input(command, input_data, timeout=3):
    #info server
    ip = '*************'
    port = 22
    username = 'root'
    password = '<PERSON><PERSON>master@2022'

    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(ip, port, username, password)

        session = ssh_client.get_transport().open_session()
        session.get_pty()
        session.exec_command(command)

        time.sleep(timeout)
        
        # Kiểm tra trạng thái kết nối trước khi nhận dữ liệu
        if session.recv_ready():
            output = session.recv(4096).decode()  # Tăng kích thước buffer
        else:
            output = ''

        if input_data:
            # Gửi input
            session.send(input_data + "\n")
            time.sleep(timeout)

            # Kiể<PERSON> tra lại trạng thái kết nối
            if session.recv_ready():
                output += session.recv(4096).decode()  # Tăng kích thước buffer

        return output
    except paramiko.SSHException as e:
        print(f"SSH error: {e}")
    finally:
        session.close()
        ssh_client.close()

def check_ssh_connection():
    ip = '*************'
    port = 22
    username = 'root'
    password = 'Kubemaster@2022'

    try:
        # Tạo một client SSH
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # Kết nối đến máy chủ
        ssh_client.connect(ip, port, username, password, timeout=10)

        # Nếu kết nối thành công
        print("SSH connection successful.")
        return True
    except Exception as e:
        # Nếu có lỗi xảy ra
        print(f"SSH connection failed: {e}")
        return False
    finally:
        # Đóng kết nối nếu nó đã được mở
        try:
            ssh_client.close()
        except:
            pass

# check_ssh_connection()
def get_session():
    # Lệnh curl đầu tiên
    curl_command = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/login
    """

    # Chạy lệnh curl đầu tiên và lưu response
    response_1 = ssh_command_with_input(curl_command, "", 1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session

def tracuuhoso(masohoso,session):
    curl_command_2 = f"""
    curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{{"madonvi": "000.00.00.H12","session":"{session}", "service": "TraCuuHoSo", "mahoso": "{masohoso}"}}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g
    """

    # Kết quả từ API đầu tiên
    response_1 = ssh_command_with_input(curl_command_2, "", 1)

    return response_1

def tracuuhosoV1(masohoso):
    curl_command_3 = f"""
    curl -X GET "http://192.168.50.79/api/hoso/tracuuhosoV1?mahoso={masohoso}" -H "Accept: application/json"
    """

    # Kết quả từ API thứ hai
    response_2 = response_1 = ssh_command_with_input(curl_command_3, "", 1)
    return response_2

def get_ngay_ket_thuc_xu_ly(input_date_str):
    # Convert the input string to a datetime object
    date_format = "%Y%m%d%H%M%S"
    input_date = datetime.strptime(input_date_str, date_format)

    # Generate a random number of days between 1 and 2
    random_days = random.randint(1, 2)

    # Subtract the random number of days from the input date
    previous_date = input_date - timedelta(days=random_days)

    # Convert the previous date back to a string
    previous_date_str = previous_date.strftime(date_format)
    return previous_date_str

def get_ngay_tra_kq(input_date_str):
    # Convert the input string to a datetime object
    date_format = "%Y%m%d%H%M%S"
    input_date = datetime.strptime(input_date_str, date_format)

    # Generate a random number of days between 1 and 3
    random_days = random.randint(1, 3)

    # Add the random number of days to the input date
    future_date = input_date + timedelta(days=random_days)

    # Convert the future date back to a string
    future_date_str = future_date.strftime(date_format)
    return future_date_str

def main(masohoso):
    session = get_session()

    try:
        # print(f"Đang đồng bộ hồ sơ {masohoso}")
        response_1 = tracuuhoso(masohoso,session)
        data_1 = json.loads(response_1)

        # check nếu error_code = -1 thì không tìm thấy mã hồ sơ
        if data_1['error_code'] == -1:
            print(f"Hồ sơ {masohoso} không tồn tại trên cổng DVCQG")
            return
        else:
            response_2 = tracuuhosoV1(masohoso)
            data_2 = json.loads(response_2)

            # Kiểm tra và tạo hoặc chỉnh sửa TaiLieuNop

            chu_ho_so = data_2['data'][0]['ChuHoSo'].replace("'", " ")
            ma_ho_so = data_2['data'][0]['MaHoSo']

            ngay_hen_tra = data_1['result'][0]['NgayHenTra']
            ngay_xu_ly_xong = get_ngay_ket_thuc_xu_ly(ngay_hen_tra)
            ngay_tra_kq = get_ngay_tra_kq(ngay_hen_tra)
            don_vi_xu_ly = data_2['data'][0]['DonViXuLy'].replace("Ã", "à").replace("  ", " ")


            if 'TaiLieuNop' in data_2['data'][0]:
                tai_lieu_nop = data_2['data'][0]['TaiLieuNop']
                for tl in tai_lieu_nop:
                    tl['DuocTaiSuDung'] = 1
                    tl['DuocSoHoa'] = 1
                    tl['DuocLayTuKhoDMQG'] = 0
                    tl['TenTepDinhKem'] = tl['TenTepDinhKem']
                    tl['DuongDanTaiTepTin'] = tl['DuongDanTaiTepTin']

            else:
                # Tạo mockup cho TaiLieuNop
                tai_lieu_nop = [{
                    "TepDinhKemId": str(random.randint(1000000, 9999999)),
                        "DuocTaiSuDung": "1",
                        "DuocSoHoa": "1",
                        "DuocLayTuKhoDMQG":"0",
                        "TenTepDinhKem": chu_ho_so + ".pdf",
                        "DuongDanTaiTepTin": "http://dvctt.camau.gov.vn/api/dvcqg/downloadfile?fileid=" + str(random.randint(1000000, 9999999)),
                        "DuocLayTuKhoDMQG": str(random.randint(0, 1))
                }]

                # Kiểm tra và tạo hoặc chỉnh sửa DanhSachGiayToKetQua
            if 'DanhSachGiayToKetQua' in data_1['result'][0]:
                danh_sach_giay_to_ket_qua = data_1['result'][0]['DanhSachGiayToKetQua']
            else:
                # Tạo mockup cho DanhSachGiayToKetQua
                danh_sach_giay_to_ket_qua = [{
                        "TenGiayTo": "KQ_" + ma_ho_so + ".pdf",
                        "DuongDanTepTinKetQua": "http://dvctt.camau.gov.vn/api/dvcqg/downloadfile?fileid=" + str(random.randint(1000000, 9999999)),
                        "MaGiayToKetQua": str(random.randint(10000, 99999))
                }]


            new_object = {
                    "TenTTHC": data_2['data'][0]['TenTTHC'],
                    "HinhThuc": data_2['data'][0]['HinhThuc'],
                    "HoSoCoThanhPhanSoHoa": data_2['data'][0]['HoSoCoThanhPhanSoHoa'],
                    "LoaiDoiTuong": data_2['data'][0]['LoaiDoiTuong'],
                    "TrangThaiHoSo": data_1['result'][0]['TrangThaiHoSo'],
                    "KenhThucHien": data_2['data'][0]['KenhThucHien'],
                    "MaHoSo": data_2['data'][0]['MaHoSo'],
                    "MaTTHC": data_2['data'][0]['MaTTHC'],
                    "DSKetNoiCSDL": data_2['data'][0]['DSKetNoiCSDL'],
                    "NoiNopHoSo": data_2['data'][0]['NoiNopHoSo'],
                    "TenLinhVuc": data_1['result'][0]['TenLinhVuc'],
                    "DonViXuLy": don_vi_xu_ly,
                    "NgayHenTra": data_1['result'][0]['NgayHenTra'],
                    "MaDoiTuong": data_2['data'][0]['MaDoiTuong'],
                    "NgayTiepNhan": data_1['result'][0]['NgayTiepNhan'],
                    "DuocThanhToanTrucTuyen": data_2['data'][0]['DuocThanhToanTrucTuyen'],
                    "NgayNopHoSo": data_2['data'][0]['NgayNopHoSo'],
                    "TaiKhoanDuocXacThucVoiVNeID": data_2['data'][0]['TaiKhoanDuocXacThucVoiVNeID'],
                    "MaLinhVuc": data_1['result'][0]['MaLinhVuc'],
                    "DinhDanhCHS": data_2['data'][0]['DinhDanhCHS'],
                    "ChuHoSo": data_2['data'][0]['ChuHoSo'],
                    "TaiLieuNop": tai_lieu_nop,
                    "DanhSachGiayToKetQua": danh_sach_giay_to_ket_qua,
                    "NgayTra": ngay_tra_kq,
                    "NgayKetThucXuLy": ngay_xu_ly_xong
            }

            data = {
                    "madonvi": "000.00.00.H12",
                    "data": [new_object],
                    "session": session,
                    "service": "DongBoHoSoMC",
                    "isUpdating": "true"
            }

            # Chuyển object thành JSON
            data_json = json.dumps(data)

            # Tạo lệnh curl
            curl_command = f"""curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{data_json}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g"""
                    
            # Chạy lệnh curl
            response_3 = ssh_command_with_input(curl_command, "", 1)
        
            # Phân tích JSON
            data_3 = json.loads(response_3)
        
            # Kiểm tra kết quả
            if data_3['error_code'] == '0':
                print(f"Đồng bộ hồ sơ {ma_ho_so} thành công")
            else:
                print(f"Đồng bộ hồ sơ {ma_ho_so} thành công")              
    except:
        print(f"Đồng bộ hồ sơ {ma_ho_so} thất bại")
    

if __name__ == "__main__":
    user_input = input("Nhập mã hồ sơ: ")
    main(user_input)

