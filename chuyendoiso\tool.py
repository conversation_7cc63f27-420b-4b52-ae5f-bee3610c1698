import requests
import json
import time

# Thông tin API
BASE_URL_COURSE = "https://chuyendoiso.mobiedu.vn/course/course"
BASE_URL_EXAM = "https://exam-api4t.mobiedu.vn/admin"
TOKEN_COURSE = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJJZCI6MjMxMjA5LCJGdWxsbmFtZSI6Ik5ndXnhu4VuIFRo4buLIEFuIETGsOG7oW5nIiwiSWRUeXBlIjo0LCJBdmF0YXJVcmwiOiIiLCJQaG9uZSI6IjA5MTM5OTg3NjgiLCJFbWFpbCI6ImFuZHVvbmcuY211QHZucHQudm4iLCJDb2RlIjoiIiwiVG9rZW5LZXkiOiI3YjE3YmRiZi1iZDJlLTQ0YzEtOWFiYS1mNDc3OTQxMjEzNzIiLCJSaWdodHMiOnt9fQ.h7nqndics7ntsxzwsznzrmk8jTomya72N8SGBPtSiW0"
TOKEN_EXAM = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NDE2NjI2ODcsImV4dF91c2VyX2lkIjoyMzEyMDksImZ1bGxuYW1lIjoiTmd1eeG7hW4gVGjhu4sgQW4gRMaw4buhbmciLCJyb2xlIjoic3R1ZGVudCIsInNpdGVfaWQiOiIyIiwic3RhdHVzIjp0cnVlLCJ1c2VyX2lkIjoiMjZjMTU0MDYtMTNkYy00NzY0LThkMmEtY2JkZTZiOTVhNTgxIn0._hE0YbgRx9wUPHAiXP4XDksasaJFlO-Cj-20RxHmuow"

# Headers cho API khóa học
headers_course = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en,vi;q=0.9,en-GB;q=0.8,en-US;q=0.7",
    "access-control-allow-origin": "*",
    "authorization": TOKEN_COURSE,
    "content-language": "vi-VN",
    "content-type": "application/json",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0",
    "x-requested-with": "XMLHttpRequest"
}

# Headers cho API bài thi
headers_exam = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5",
    "authorization": TOKEN_EXAM,
    "content-type": "application/json",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36"
}

# Hàm lấy danh sách khóa học từ API liststudent
def get_course_list(page=0, per_page=12):
    url = f"{BASE_URL_COURSE}/liststudent?page={page}&per_page={per_page}"
    response = requests.get(url, headers=headers_course)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Lỗi khi lấy danh sách khóa học: {response.status_code}")
        return None

# Hàm lấy danh sách lesson từ API getlessoncourse
def get_lessons(id_course, id_lesson):
    url = f"{BASE_URL_COURSE}/getlessoncourse?idCourse={id_course}&idLesson={id_lesson}"
    response = requests.get(url, headers=headers_course)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Lỗi khi lấy danh sách lesson: {response.status_code}")
        return None

# Hàm hoàn thành lesson
def complete_lesson(id_course, id_lesson):
    url = f"{BASE_URL_COURSE}/completelesson"
    payload = {
        "idLesson": str(id_lesson),
        "idCourse": str(id_course),
        "viewPercent": 100,
        "fromIframe": False
    }
    response = requests.post(url, headers=headers_course, json=payload)
    if response.status_code == 200:
        print(f"Hoàn thành lesson {id_lesson} thành công!")
    else:
        print(f"Lỗi khi hoàn thành lesson {id_lesson}: {response.status_code} - {response.text}")

# Hàm lấy danh sách câu hỏi bài thi từ API exam_details
def get_exam_questions(exam_id):
    url = f"{BASE_URL_EXAM}/exam_details.json?id={exam_id}"
    response = requests.get(url, headers=headers_exam)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Lỗi khi lấy danh sách câu hỏi bài thi: {response.status_code}")
        return None

# Hàm tự động hoàn thành khóa học
def auto_complete_course(id_course, first_lesson_id):
    data = get_lessons(id_course, first_lesson_id)
    if not data or data["error"]:
        print("Không thể lấy dữ liệu khóa học!")
        return

    lessons = []
    for topic in data["data"]["data"]:
        for lesson in topic["lessons"]:
            lessons.append(lesson["id"])

    for lesson_id in lessons:
        complete_lesson(id_course, lesson_id)
        time.sleep(2)

# Hàm tự động trả lời bài thi (in câu hỏi và đáp án dạng a, b, c, d)
# Hàm tự động trả lời bài thi (in câu hỏi và đáp án dạng a, b, c, d, sắp xếp theo index)
def auto_complete_exam(exam_id):
    data = get_exam_questions(exam_id)
    if not data or "data" not in data:
        print("Không thể lấy dữ liệu bài thi!")
        return

    questions = data["data"]["questions"]
    # Sắp xếp câu hỏi theo index
    sorted_questions = sorted(questions, key=lambda x: x["index"])
    print(f"\nTìm thấy {len(sorted_questions)} câu hỏi trong bài thi ID: {exam_id}")
    print("\n=== DANH SÁCH CÂU HỎI VÀ ĐÁP ÁN ===")

    for idx, question in enumerate(sorted_questions, 1):
        print(f"\nCâu {idx} (Index {question['index']}): {question['question']['content']}")
        print("Đáp án:")
        options = ['A', 'B', 'C','D']  # Nhãn cho 4 đáp án
        for i, answer in enumerate(question["answers"][:4]):  # Giới hạn 4 đáp án
            print(f"{options[i]}. {answer['content']}")
        print("-------------------")
    
    print("\nBạn có thể copy danh sách trên để tìm đáp án đúng.")

# Hàm chính
def main():
    print("Bắt đầu tool...")
    
    # Lấy danh sách khóa học
    course_data = get_course_list()
    if not course_data or course_data["error"]:
        print("Không thể lấy danh sách khóa học!")
        return
    
    courses = course_data["data"]
    print("\nDanh sách khóa học:")
    for idx, course in enumerate(courses):
        print(f"{idx + 1}. {course['name']} (ID: {course['id']}, Tiến độ: {course['completePercent']}, Lesson đầu tiên: {course['firstLessonId']})")

    # Yêu cầu người dùng chọn khóa học
    choice = int(input("\nChọn số thứ tự khóa học để tự động hoàn thành (0 để thoát): "))
    if choice == 0:
        print("Thoát chương trình.")
        return
    if choice < 1 or choice > len(courses):
        print("Lựa chọn không hợp lệ!")
        return

    selected_course = courses[choice - 1]
    id_course = selected_course["id"]
    first_lesson_id = selected_course["firstLessonId"]

    print(f"\nBắt đầu tự động hoàn thành khóa học: {selected_course['name']} (ID: {id_course})...")
    # auto_complete_course(id_course, first_lesson_id)

    # Yêu cầu tự động làm bài thi
    exam_id = input("\nNhập ID bài thi để trích xuất câu hỏi (Enter để bỏ qua): ")
    if exam_id:
        print(f"Trích xuất câu hỏi bài thi ID: {exam_id}...")
        auto_complete_exam(exam_id)
    else:
        print("Bỏ qua bài thi.")

    print("Đã hoàn thành!")

# Chạy tool
if __name__ == "__main__":
    main()