import json
from pymongo import MongoClient
import pandas as pd
from bson.objectid import ObjectId
from datetime import datetime, time, timedelta
import random
import time as t
import httpx
import json

url = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"

headers = {
  'Content-Type': 'application/json',
  'Authorization': 'bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
  'Cookie': 'JSESSIONID=979D1F437D51C126FF15E5BA8BE0E768'
}

# 1. Đọc dữ liệu từ file Excel
file_path = 'excel/20241113.xlsx'
df = pd.read_excel(file_path, engine='openpyxl')        


# 2. Map dữ liệu từ Excel vào object template
hs_success = []
hs_fail = []
ssl_context = httpx.create_ssl_context()
ssl_context.set_ciphers("HIGH:!DH:!aNULL")

for index, row in df.iterrows():
    payload = json.dumps([
        row['Code']
    ])
    try:
        response = httpx.post(url, headers=headers, data=payload, verify=ssl_context)
        print(response.text.encode('utf8'))
        if response.status_code == 200:
            if response.json()['affectedRows'] == 1:
                hs_success.append(row['Code'])
            else:
                hs_fail.append(row['Code'])
        else:
            hs_fail.append(row['Code'])
    except Exception as e:
        hs_fail.append(row['Code'])
        continue
        print(e)

print (hs_fail)

# 3. export data to json file
# with open('hs_success.json', 'w') as outfile:
#     json.dump(hs_success, outfile)
# with open('hs_fail.json', 'w') as outfile:
#     json.dump(hs_fail, outfile)


