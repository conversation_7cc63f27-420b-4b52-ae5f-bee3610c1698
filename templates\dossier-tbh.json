{"_id": {"$oid": "652e3697d8c7a64a11cfc594"}, "code": "000.00.25.H12-231017-0204", "codePattern": {"id": {"$oid": "644748debc8884294b473940"}}, "procedure": {"id": {"$oid": "652dedabe8b67510552d43e4"}, "code": "1.003003.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON><PERSON>ng ký và cấp <PERSON><PERSON><PERSON><PERSON> chứng nhận quyền sử dụng đất, quyền sở hữu nhà ở và tài sản khác gắn liền với đất lần đầu"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652dedfae8b67510552d4417"}, "processDefinition": {"id": {"$oid": "652deab0e1553b068aebf033"}, "processingTime": 21, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}}, "applicantEForm": {"id": {"$oid": "652d45cec56bc1001fc6e125"}}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf95af4d70261e45f230"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d45cec56bc1001fc6e125"}, "userId": {"$oid": "652e2f050ec8eb131c3c9f75"}, "data": {"birthday": "1964-01-01T00:00:00.000+0000", "note": "", "gender": "", "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện T<PERSON>", "value": "5def47c5f47614018c001967"}, "identityDate": "2021-09-01T00:00:00+07:00", "nycSoGiayToTuyThan": "096064006728", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON>h Tâm", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096064006728", "serialMap": "", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Tân Bằng", "value": "5def47c5f47614018c132069"}, "email": "", "address": "<PERSON><PERSON> kinh 6", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "Xã Tân Bằng", "value": "5def47c5f47614018c132069"}, "pdg": false, "serialLand": "", "phoneNumber": "0384278065", "declarationForm": {"identifyNo": "096064006728", "phone": "0384278065", "fullName": "<PERSON><PERSON>h Tâm", "birthDateStr": "01/01/1964", "email": "", "idTypeId": 3}, "district": {"label": "Huyện T<PERSON>", "value": "5def47c5f47614018c001967"}, "diaChiThuaDat": "", "fullname": "TỪ THANH TÂM-763"}}, "accepter": {"id": {"$oid": "652e3b0e0ec8eb131c3c9fc3"}, "fullname": "<PERSON><PERSON> <PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf95af4d70261e45f230"}, "code": "000.00.25.H12", "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a03af4d70261e45f1c8"}, "name": [{"languageId": 228, "name": "UBND huyện Thới Bình"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "64093a03af4d70261e45f1c8"}, "name": [{"languageId": 228, "name": "UBND huyện Thới Bình"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-10-17T08:01:23.000Z"}, "appliedDate": {"$date": "2023-10-17T07:24:07.908Z"}, "appointmentDate": {"$date": "2023-11-15T08:01:00.000Z"}, "completedDate": {"$date": "2023-10-23T03:09:23.011Z"}, "returnedDate": {"$date": "2023-10-27T09:52:12.791Z"}, "attachment": [{"id": {"$oid": "653b881026658a52baed1454"}, "filename": "000.00.25.H12-231017-0204-KQ.pdf", "size": {"$numberLong": "62464"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "652e3f54d8c7a64a11cfc969"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf95af4d70261e45f230"}, "code": "000.00.25.H12", "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a03af4d70261e45f1c8"}, "name": [{"languageId": 228, "name": "UBND huyện Thới Bình"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "64093a03af4d70261e45f1c8"}, "name": [{"languageId": 228, "name": "UBND huyện Thới Bình"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:339:eaf83710-6c90-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf95af4d70261e45f230"}, "code": "000.00.25.H12", "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a03af4d70261e45f1c8"}, "name": [{"languageId": 228, "name": "UBND huyện Thới Bình"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "64093a03af4d70261e45f1c8"}, "name": [{"languageId": 228, "name": "UBND huyện Thới Bình"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-10-17T08:01:23.000Z"}, "completedDate": {"$date": "2023-10-17T08:04:46.322Z"}, "dueDate": {"$date": "2023-10-18T02:01:00.000Z"}, "createdDate": {"$date": "2023-10-17T08:01:23.000Z"}, "updatedDate": {"$date": "2023-10-17T08:04:46.322Z"}, "activitiTask": {"id": "5db7e77d-6cc3-11ee-8dd7-a2137e48eaaf", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652deab0e1553b068aebf034"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}, {"id": {"$oid": "652e401dd8c7a64a11cfc9e2"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "64093a03af4d70261e45f1c8"}, "code": "000.00.25.H12", "name": [{"languageId": 228, "name": "UBND huyện Thới Bình"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:339:eaf83710-6c90-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "64093a03af4d70261e45f1c8"}, "code": "000.00.25.H12", "name": [{"languageId": 228, "name": "UBND huyện Thới Bình"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-10-17T08:04:45.334Z"}, "completedDate": {"$date": "2023-10-23T03:09:44.771Z"}, "dueDate": {"$date": "2023-11-14T08:04:00.000Z"}, "claimDate": {"$date": "2023-10-17T08:04:45.334Z"}, "createdDate": {"$date": "2023-10-17T08:04:45.334Z"}, "updatedDate": {"$date": "2023-10-23T03:09:44.771Z"}, "activitiTask": {"id": "d59cce9b-6cc3-11ee-8dd7-a2137e48eaaf", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652deab0e1553b068aebf035"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 20, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}, {"id": {"$oid": "6535e3f72da085662b4ff1c5"}, "assignee": {"id": {"$oid": "652e3b0e0ec8eb131c3c9fc3"}, "fullname": "<PERSON><PERSON> <PERSON><PERSON>", "account": {"id": {"$oid": "652e3b0e94d7072e39bbd9d2"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf95af4d70261e45f230"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a03af4d70261e45f1c8"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:339:eaf83710-6c90-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "652e401dd8c7a64a11cfc9e2"}}, "agency": {"id": {"$oid": "6411bf95af4d70261e45f230"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a03af4d70261e45f1c8"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-10-23T03:09:43.786Z"}, "completedDate": {"$date": "2023-10-27T09:52:12.791Z"}, "dueDate": {"$date": "2023-10-23T09:09:43.786Z"}, "createdDate": {"$date": "2023-10-23T03:09:43.786Z"}, "updatedDate": {"$date": "2023-10-27T09:52:12.791Z"}, "activitiTask": {"id": "9d20b48d-7151-11ee-a22e-d621fa40e592", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652deab0e1553b068aebf036"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6535e3f72da085662b4ff1c5"}, "assignee": {"id": {"$oid": "652e3b0e0ec8eb131c3c9fc3"}, "fullname": "<PERSON><PERSON> <PERSON><PERSON>", "account": {"id": {"$oid": "652e3b0e94d7072e39bbd9d2"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf95af4d70261e45f230"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a03af4d70261e45f1c8"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:339:eaf83710-6c90-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "652e401dd8c7a64a11cfc9e2"}}, "agency": {"id": {"$oid": "6411bf95af4d70261e45f230"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a03af4d70261e45f1c8"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-10-23T03:09:43.786Z"}, "completedDate": {"$date": "2023-10-27T09:52:12.791Z"}, "dueDate": {"$date": "2023-10-23T09:09:43.786Z"}, "createdDate": {"$date": "2023-10-23T03:09:43.786Z"}, "updatedDate": {"$date": "2023-10-27T09:52:12.791Z"}, "activitiTask": {"id": "9d20b48d-7151-11ee-a22e-d621fa40e592", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652deab0e1553b068aebf036"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}], "activitiProcessInstance": {"_id": "5db7c068-6cc3-11ee-8dd7-a2137e48eaaf", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1964-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096064006728", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096064006728", "phone": "0384278065", "fullName": "<PERSON><PERSON>h Tâm", "birthDateStr": "01/01/1964", "email": "", "idTypeId": 3}, "phoneNumber": "0384278065", "nycHoTen": "<PERSON><PERSON>h Tâm", "identityNumber": "096064006728", "fullname": "<PERSON><PERSON>h Tâm", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T07:24:07.908Z"}, "updatedDate": {"$date": "2023-10-27T09:52:23.263Z"}, "sync": {"id": 1, "sourceCode": "000.00.25.H12-231017-0204", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: DBzxj8oxAoY+QasO18zMSzzwVlGsxn+l9qxT3gvp4P/ZOPPvfOaV7A\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": true, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 0, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "_class": "vn.vnpt.digo.padman.document.Dossier"}