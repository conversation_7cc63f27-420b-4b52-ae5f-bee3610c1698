import pandas as pd
import httpx
import ssl
from openpyxl import Workbook
from openpyxl.styles import Alignment, Border, Side, Font
from openpyxl.utils.dataframe import dataframe_to_rows

# API endpoint and headers
url = "https://apidvc.baclieu.gov.vn/integration/check-citizen-log/--get-check-citizen-log"
headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "vi",
    "Authorization" : "bearer *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
}

# SSL context
ssl_context = ssl.create_default_context()
ssl_context.set_ciphers("HIGH:!DH:!aNULL")

# Dynamic date range
from_date = "2024-06-01T00:00:00.000Z"  # Start date
to_date = "2024-07-27T23:59:59.999Z"    # End date

# Initialize list to hold all data
all_data = []

# Pagination variables
page = 0
size = 50

# Fetch data from all pages
while True:
    params = {
        "page": page,
        "size": size,
        "from-date": from_date,
        "to-date": to_date
    }
    response = httpx.get(url, headers=headers, params=params, verify=ssl_context)
    data = response.json()
    
    # Extract content data
    content = data.get('content', [])
    
    # If no more content, break the loop
    if not content:
        break
    
    # Append content to all_data
    all_data.extend(content)
    
    # Increment page number
    page += 1

# Prepare data for the DataFrame
processed_data = []
for item in all_data:
    birth_date = pd.to_datetime(item.get('birtDay'), errors='coerce')
    citizen_info = (
        f"- CCCD/CMND: {item.get('identityNumber', '')};\n"
        f"- Họ tên công dân: {item.get('name', '')};\n"
        f"- Ngày sinh: {birth_date.strftime('%d/%m/%Y') if not pd.isna(birth_date) else ''}"
    )
    create_date = pd.to_datetime(item.get("createDate"), errors='coerce')
    record = {
        "ID cán bộ": item.get("accountId"),
        "Họ và tên cán bộ": item.get("username"),
        "Mã cơ quan": item['agency'].get("code") if item.get("agency") else None,
        "Tên cơ quan": item['agency'].get("name") if item.get("agency") else None,
        "Tên API": item.get("url") or item.get("function"),
        "Thông tin tra cứu công dân": citizen_info,
        "Ngày thao tác": create_date.strftime('%d/%m/%Y') if not pd.isna(create_date) else None
    }
    processed_data.append(record)

# Create DataFrame
df = pd.DataFrame(processed_data)

# Create a new workbook and select the active sheet
wb = Workbook()
ws = wb.active

# Title
ws.merge_cells('B1:H1')
ws['B1'] = "THỐNG KÊ CHI TIẾT LƯỢT TRA CỨU THÔNG TIN CÔNG DÂN QUA CSDLQGVDC"
ws['B1'].font = Font(bold=True, size=14)
ws['B1'].alignment = Alignment(horizontal='center')

# Subtitle with dynamic dates
ws.merge_cells('B2:H2')
ws['B2'] = f"Từ ngày {pd.to_datetime(from_date).strftime('%d/%m/%Y')} đến ngày {pd.to_datetime(to_date).strftime('%d/%m/%Y')}"
ws['B2'].alignment = Alignment(horizontal='center')

# Department
ws.merge_cells('B3:H3')
ws['B3'] = "Đơn vị: Sở kiểm thử"
ws['B3'].alignment = Alignment(horizontal='center')

# Headers
headers = ["STT", "ID cán bộ", "Họ và tên cán bộ", "Mã cơ quan", "Tên cơ quan", "Tên API", "Thông tin tra cứu công dân", "Ngày thao tác"]
ws.append(headers)

# Adding data rows
for index, row in enumerate(dataframe_to_rows(df, index=False, header=False), start=1):
    ws.append([index] + list(row))

# Formatting
thin = Side(border_style="thin", color="000000")
for row in ws.iter_rows(min_row=5, max_row=ws.max_row, min_col=2, max_col=8):
    for cell in row:
        cell.border = Border(left=thin, right=thin, top=thin, bottom=thin)
        cell.alignment = Alignment(wrap_text=True, vertical='top')
        if cell.column == 'B':
            cell.font = Font(bold=True)

# Adjust column widths
column_widths = [5, 15, 25, 15, 30, 50, 50, 15]
for i, col_width in enumerate(column_widths, start=2):
    ws.column_dimensions[chr(64 + i)].width = col_width

# Save the workbook
output_path = "Thong_ke_tra_cuu_csdl_dan_cu.xlsx"
wb.save(output_path)

print("The file has been saved with the specified layout!")
