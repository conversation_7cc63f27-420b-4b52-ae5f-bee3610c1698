{"_id": {"$oid": "652e2debd8c7a64a11cfc2ad"}, "code": "000.00.20.H12-231017-0202", "codePattern": {"id": {"$oid": "61ace3a710a95e0997dd2eab"}}, "procedure": {"id": {"$oid": "652dedaae8b67510552d43b4"}, "code": "1.003003.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON><PERSON>ng ký và cấp <PERSON><PERSON><PERSON><PERSON> chứng nhận quyền sử dụng đất, quyền sở hữu nhà ở và tài sản khác gắn liền với đất lần đầu"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652dee38e8b67510552d441b"}, "processDefinition": {"id": {"$oid": "652de7ffe1553b068aebf01f"}, "processingTime": 21, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d40a6c56bc1001fc6e11b"}, "name": "HCC iLis thong tin chung CNC"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "64099d52af4d70261e45f1d5"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d40a6c56bc1001fc6e11b"}, "userId": {"$oid": "652d07760ec8eb131c3c9e52"}, "data": {"birthday": "1961-02-10T00:00:00.000+0000", "note": "", "gender": "", "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyệ<PERSON>", "value": "5def47c5f47614018c001969"}, "identityDate": "2021-08-30T00:00:00+07:00", "nycSoGiayToTuyThan": "096061003181", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096061003181", "serialMap": "01", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Thạnh Phú", "value": "5def47c5f47614018c132130"}, "email": "<EMAIL>", "address": "<PERSON><PERSON>, <PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "Xã Thạnh Phú", "value": "5def47c5f47614018c132130"}, "pdg": false, "serialLand": "252", "phoneNumber": "0815968073", "declarationForm": {"identifyNo": "096061003181", "phone": "0815968073", "fullName": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "birthDateStr": "10/02/1961", "email": "<EMAIL>", "idTypeId": 3}, "district": {"label": "Huyệ<PERSON>", "value": "5def47c5f47614018c001969"}, "diaChiThuaDat": "<PERSON><PERSON>, <PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON>", "fullname": "NGUYỄN VĂN NHA VÀ NGUYỄN THỊ QUAN"}}, "accepter": {"id": {"$oid": "642e398e6fb24b5e08cc6771"}, "fullname": "<PERSON><PERSON>"}, "agency": {"id": {"$oid": "64099d52af4d70261e45f1d5"}, "code": "000.00.20.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả KQ UBND huyện Cái Nước"}], "parent": {"id": {"$oid": "637d8bf2f217d52a06d6d104"}, "code": "000.00.20.H12", "name": [{"languageId": 228, "name": "UBND huyện C<PERSON>"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "637d8bf2f217d52a06d6d104"}, "name": [{"languageId": 228, "name": "UBND huyện C<PERSON>"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-10-23T02:33:31.000Z"}, "appliedDate": {"$date": "2023-10-17T06:47:07.280Z"}, "appointmentDate": {"$date": "2023-11-21T02:33:00.000Z"}, "completedDate": {"$date": "2023-11-09T02:02:43.474Z"}, "returnedDate": {"$date": "2023-11-21T10:11:22.615Z"}, "attachment": [{"id": {"$oid": "654c3dd3c33ba14a18ca1c95"}, "filename": "000.00.20.H12-231017-0202-KQ(1).pdf", "size": {"$numberLong": "3720043"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "6535db7c2da085662b4fed21"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "64099d52af4d70261e45f1d5"}, "code": "000.00.20.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả KQ UBND huyện Cái Nước"}], "parent": {"id": {"$oid": "637d8bf2f217d52a06d6d104"}, "name": [{"languageId": 228, "name": "UBND huyện C<PERSON>"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "637d8bf2f217d52a06d6d104"}, "name": [{"languageId": 228, "name": "UBND huyện C<PERSON>"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:335:4fe3182a-6c8f-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "64099d52af4d70261e45f1d5"}, "code": "000.00.20.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả KQ UBND huyện Cái Nước"}], "parent": {"id": {"$oid": "637d8bf2f217d52a06d6d104"}, "name": [{"languageId": 228, "name": "UBND huyện C<PERSON>"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "637d8bf2f217d52a06d6d104"}, "name": [{"languageId": 228, "name": "UBND huyện C<PERSON>"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-10-23T02:33:31.000Z"}, "completedDate": {"$date": "2023-10-23T07:19:16.031Z"}, "dueDate": {"$date": "2023-10-23T08:33:31.000Z"}, "createdDate": {"$date": "2023-10-23T02:33:31.000Z"}, "updatedDate": {"$date": "2023-10-23T07:19:16.031Z"}, "activitiTask": {"id": "8ee9e437-714c-11ee-a22e-d621fa40e592", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652de7ffe1553b068aebf020"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}, {"id": {"$oid": "65361e732da085662b5005d3"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "637d8bf2f217d52a06d6d104"}, "code": "000.00.20.H12", "name": [{"languageId": 228, "name": "UBND huyện C<PERSON>"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:335:4fe3182a-6c8f-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "6535db7c2da085662b4fed21"}}, "agency": {"id": {"$oid": "637d8bf2f217d52a06d6d104"}, "code": "000.00.20.H12", "name": [{"languageId": 228, "name": "UBND huyện C<PERSON>"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-10-23T07:19:15.181Z"}, "completedDate": {"$date": "2023-11-09T02:02:49.444Z"}, "dueDate": {"$date": "2023-11-20T07:19:00.000Z"}, "claimDate": {"$date": "2023-10-23T07:19:15.181Z"}, "createdDate": {"$date": "2023-10-23T07:19:15.181Z"}, "updatedDate": {"$date": "2023-11-09T02:02:49.444Z"}, "activitiTask": {"id": "78b1b0c8-7174-11ee-a22e-d621fa40e592", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652de7ffe1553b068aebf021"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 20, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}, {"id": {"$oid": "654c3dc8e4af0a2ae16604e8"}, "assignee": {"id": {"$oid": "642e398e6fb24b5e08cc6771"}, "fullname": "<PERSON><PERSON>", "account": {"id": {"$oid": "642e398e16e6207094eea7e2"}, "username": [{"value": "*********"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "64099d52af4d70261e45f1d5"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "637d8bf2f217d52a06d6d104"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:335:4fe3182a-6c8f-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "65361e732da085662b5005d3"}}, "agency": {"id": {"$oid": "64099d52af4d70261e45f1d5"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "637d8bf2f217d52a06d6d104"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-09T02:02:48.456Z"}, "completedDate": {"$date": "2023-11-21T10:11:22.615Z"}, "dueDate": {"$date": "2023-11-09T08:02:48.456Z"}, "createdDate": {"$date": "2023-11-09T02:02:48.456Z"}, "updatedDate": {"$date": "2023-11-21T10:11:22.615Z"}, "activitiTask": {"id": "14d5181d-7ea4-11ee-9c8f-32cf687ee280", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652de7ffe1553b068aebf022"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}], "currentTask": [], "previousTask": [{"id": {"$oid": "654c3dc8e4af0a2ae16604e8"}, "assignee": {"id": {"$oid": "642e398e6fb24b5e08cc6771"}, "fullname": "<PERSON><PERSON>", "account": {"id": {"$oid": "642e398e16e6207094eea7e2"}, "username": [{"value": "*********"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "64099d52af4d70261e45f1d5"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "637d8bf2f217d52a06d6d104"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:335:4fe3182a-6c8f-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "65361e732da085662b5005d3"}}, "agency": {"id": {"$oid": "64099d52af4d70261e45f1d5"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "637d8bf2f217d52a06d6d104"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-09T02:02:48.456Z"}, "completedDate": {"$date": "2023-11-21T10:11:22.615Z"}, "dueDate": {"$date": "2023-11-09T08:02:48.456Z"}, "createdDate": {"$date": "2023-11-09T02:02:48.456Z"}, "updatedDate": {"$date": "2023-11-21T10:11:22.615Z"}, "activitiTask": {"id": "14d5181d-7ea4-11ee-9c8f-32cf687ee280", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652de7ffe1553b068aebf022"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}], "activitiProcessInstance": {"_id": "8ee9e432-714c-11ee-a22e-d621fa40e592", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1961-02-10T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096061003181", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096061003181", "phone": "0815968073", "fullName": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "birthDateStr": "10/02/1961", "email": "<EMAIL>", "idTypeId": 3}, "phoneNumber": "0815968073", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "identityNumber": "096061003181", "fullname": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "email": "<EMAIL>", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T06:47:07.280Z"}, "updatedDate": {"$date": "2023-11-21T10:11:35.009Z"}, "sync": {"id": 1, "sourceCode": "000.00.20.H12-231017-0202", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: uDCXsUMaqHrm76Vq3m9aBTq+MVIKmlNiFI0XyAB9+To83yPd2V806g\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 0, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "_class": "vn.vnpt.digo.padman.document.Dossier"}