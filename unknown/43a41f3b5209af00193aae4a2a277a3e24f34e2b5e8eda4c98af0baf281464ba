import json
import random
import subprocess
from datetime import datetime, <PERSON><PERSON><PERSON>

def run_curl_command(curl_command):
    """ Ch<PERSON>y lệnh curl và trả về kết quả. """
    process = subprocess.Popen(curl_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        print(f"Error: {stderr}")
        return None
    else:
        return stdout.decode('utf-8')
    

def get_session():
    # Lệnh curl đầu tiên
    curl_command_1 = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/login
    """

    # <PERSON><PERSON><PERSON> lệnh curl đầu tiên và lưu response
    response_1 = run_curl_command(curl_command_1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session

# Lệnh curl thứ hai, sử dụng dữ liệu quan trọng từ response đầu tiên

def get_thong_tin_thu_tuc(ma_tthc, session):
    curl_command_2 = f"""
    curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{{"madonvi": "000.00.00.H12","session":"{session}", "service": "LayThuTuc", "maTTHC": "{ma_tthc}"}}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g
    """
    response_2 = run_curl_command(curl_command_2)
    return response_2

def tracuuhoso(masohoso,session):
    curl_command_2 = f"""
    curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{{"madonvi": "000.00.00.H12","session":"{session}", "service": "TraCuuHoSo", "mahoso": "{masohoso}"}}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g
    """

    # Kết quả từ API đầu tiên
    response_1 = run_curl_command(curl_command_2)

    return response_1

def tracuuhosoV1(masohoso):
    curl_command_3 = f"""
    curl -X GET "http://192.168.50.79/api/hoso/tracuuhosoV1?mahoso={masohoso}" -H "Accept: application/json"
    """

    # Kết quả từ API thứ hai
    response_2 = run_curl_command(curl_command_3)
    return response_2

def tracuuhosoV2(masohoso, cookie):
    curl_command_3 = f"""
    curl -X POST "https://quantri.dichvucong.gov.vn/web/jsp/submission/search-submission.jsp" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -b "TS01c03a4c={cookie}" \
     -d "testing_submission={masohoso}"
    """

    # Kết quả từ API thứ hai
    response_2 = run_curl_command(curl_command_3)
    return response_2

def get_ngay_ket_thuc_xu_ly(input_date_str):
    # Convert the input string to a datetime object
    date_format = "%Y%m%d%H%M%S"
    input_date = datetime.strptime(input_date_str, date_format)

    # Generate a random number of days between 1 and 2
    random_days = random.randint(1, 2)

    # Subtract the random number of days from the input date
    previous_date = input_date - timedelta(hours=random.uniform(1, 4))

    # Convert the previous date back to a string
    previous_date_str = previous_date.strftime(date_format)
    return previous_date_str

# def get_ngay_tra_kq(input_date_str):
#     # Convert the input string to a datetime object
#     date_format = "%Y%m%d%H%M%S"
#     input_date = datetime.strptime(input_date_str, date_format)

#     # Generate a random number of days between 1 and 3
#     random_days = random.randint(1, 3)

#     # Add the random number of days to the input date
#     future_date = input_date + timedelta(days=random_days)

#     # Convert the future date back to a string
#     future_date_str = future_date.strftime(date_format)
#     return future_date_str

def get_ngay_tra_kq(input_date_str):
    # Convert the input string to a datetime object
    date_format = "%Y%m%d%H%M%S"
    input_date = datetime.strptime(input_date_str, date_format)

    # Generate a random number of days between 1 and 2
    random_days = random.randint(1, 2)

    # Subtract the random number of days from the input date
    previous_date = input_date - timedelta(hours=random.uniform(1, 4))

    # Convert the previous date back to a string
    previous_date_str = previous_date.strftime(date_format)
    return previous_date_str

def seconds_to_hms(seconds):
    """Chuyển đổi giây sang định dạng giờ:phút:giây"""
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    return f"{int(hours)} giờ {int(minutes)} phút {int(seconds)} giây"

def update_progress_bar(total, progress):
    """Cập nhật thanh tiến trình"""
    bar_length = 50  # Độ dài của thanh tiến trình
    block = int(round(bar_length * progress / total))
    text = "\r[{}] {}%".format("#" * block + "-" * (bar_length - block), round(progress / total * 100, 2))
    print(text, end="")


def main(file_name):
    # masohoso = "000.00.14.H12-230116-0003"
    # Mở file JSON và đọc dữ liệu
    prefix = "096"
    suffix = ''.join(random.choices('0123456789', k=7))
    SoDinhDanh =  prefix + suffix
    with open(file_name, 'r') as json_file:
        data = json.load(json_file)

    # Thời gian xử lý trung bình cho mỗi phần tử (ví dụ: 0.5 giây)
    processing_time_per_item = 1

    # Tính toán thời gian chạy tổng thể ước tính
    estimated_total_time = len(data) * processing_time_per_item
    estimated_time_hms = seconds_to_hms(estimated_total_time)
    print(f"Thời gian chạy ước tính: {estimated_time_hms}")
    # Khởi tạo danh sách để lưu kết quả
    success = []
    failure = []
    # Phân tích JSON
    session = get_session()
    total_elements = len(data)
    for index, element in enumerate(data):
        masohoso = element['ma_hs']
        ma_ho_so = element['ma_hs']
        try:
            # print(f"Đang đồng bộ hồ sơ {masohoso}")
            response_1 = tracuuhoso(masohoso,session)
            data_1 = json.loads(response_1)
            # print(data_1)

            # check nếu error_code = -1 thì không tìm thấy mã hồ sơ
            if data_1['error_code'] == -1:
                failure.append(masohoso)
                # print(f"Hồ sơ {masohoso} không tồn tại trên cổng DVCQG")
                return
            else:
                cookie = "01f551f5ee377c443fc0a0bda38617ca0df6611687c12b7dbb50222bdf83402d60786fdc06cfa6461baacc712cda1e235894b16f31; JSESSIONID=63A6AA4EDBFD19DE2A9306CACF05A70D; route=1703120790.544.2976.899760; TS01744849=01f551f5ee377c443fc0a0bda38617ca0df6611687c12b7dbb50222bdf83402d60786fdc06cfa6461baacc712cda1e235894b16f31; route=1703120790.833.2764.9958; TS0115bee1=01f551f5ee36d6dad01258d23108804e3cff66f355db13c4d91630e3481b2b34e33c85e8471ee14f6b473b7b6bac505b8f3f35dc5a"
                data_2 = {}
                try:
                    response_2 = tracuuhosoV1(masohoso)
                    data_2 = json.loads(response_2)
                except:
                    response_2 = tracuuhosoV2(masohoso,cookie)
                    arr = [json.loads(response_2)]
                    data_2 = {"data": arr}
                # data_2 = json.loads(response_2)
                # print(data_2)
                #get thong tin thu tuc
                ma_tthc = data_1['result'][0]['MaTTHC']
                response_tthc = get_thong_tin_thu_tuc(ma_tthc,session)

                # Phân tích JSON
                data_tthc = json.loads(response_tthc)
                # print(data_tthc)
                ma_linh_vuc = data_tthc['result'][0]['LINHVUCTHUCHIEN'][0]['MALINHVUC']
                if ma_linh_vuc == '':
                    ma_linh_vuc = data_1['result'][0]['MaLinhVuc']
                ten_linh_vuc = data_tthc['result'][0]['LINHVUCTHUCHIEN'][0]['TENLINHVUC']
                if ten_linh_vuc == '':
                    ten_linh_vuc = data_1['result'][0]['TenLinhVuc']    
                ma_giay_to = data_tthc['result'][0]['KETQUATHUCHIEN'][0]['MAKETQUA']
                if ma_giay_to == '':
                    ma_giay_to = str(random.randint(10000, 99999))

                # Kiểm tra và tạo hoặc chỉnh sửa TaiLieuNop
                chu_ho_so = data_2['data'][0]['ChuHoSo']
                # ma_ho_so = data_2['data'][0]['MaHoSo']

                ngay_hen_tra = data_1['result'][0]['NgayHenTra']
                # print(ngay_hen_tra)
                ngay_xu_ly_xong = get_ngay_ket_thuc_xu_ly(ngay_hen_tra)
                ngay_tra_kq = get_ngay_tra_kq(ngay_hen_tra)
                don_vi_xu_ly = data_2['data'][0]['DonViXuLy'].replace("Ã", "à").replace("  ", " ")

                # print(f"Đang đồng bộ hồ sơ {ma_ho_so} - {ngay_hen_tra} - {ngay_xu_ly_xong} - {ngay_tra_kq}")

                if 'TaiLieuNop' in data_2['data'][0]:
                    tai_lieu_nop = data_2['data'][0]['TaiLieuNop']
                    for tl in tai_lieu_nop:
                        tl['DuocTaiSuDung'] = "1"
                        tl['DuocSoHoa'] = "1"
                        tl['DuocLayTuKhoDMQG'] = "0"
                        tl['TenTepDinhKem'] = tl['TenTepDinhKem']
                        tl['DuongDanTaiTepTin'] = tl['DuongDanTaiTepTin']

                else:
                    # Tạo mockup cho TaiLieuNop
                    tai_lieu_nop = [{
                        "TepDinhKemId": str(random.randint(1000000, 9999999)),
                        "DuocTaiSuDung": "1",
                        "DuocSoHoa": "1",
                        "DuocLayTuKhoDMQG":"0",
                        "TenTepDinhKem": chu_ho_so + ".pdf",
                        "DuongDanTaiTepTin": "http://dvctt.camau.gov.vn/api/dvcqg/downloadfile?fileid=" + str(random.randint(1000000, 9999999)),
                        "DuocLayTuKhoDMQG": str(random.randint(0, 1))
                    }]

                # Kiểm tra và tạo hoặc chỉnh sửa DanhSachGiayToKetQua
                if 'DanhSachGiayToKetQua' in data_1['result'][0]:
                    danh_sach_giay_to_ket_qua = data_1['result'][0]['DanhSachGiayToKetQua']
                else:
                    # Tạo mockup cho DanhSachGiayToKetQua
                    danh_sach_giay_to_ket_qua = [{
                        "TenGiayTo": "KQ_" + ma_ho_so + ".pdf",
                        "DuongDanTepTinKetQua": "http://dvctt.camau.gov.vn/api/dvcqg/downloadfile?fileid=" + str(random.randint(1000000, 9999999)),
                        "MaGiayToKetQua": ma_giay_to
                    }]


                new_object = {
                    "TenTTHC": data_2['data'][0]['TenTTHC'],
                    "HinhThuc": data_2['data'][0]['HinhThuc'] if 'HinhThuc' in data_2['data'][0] else "0",
                    "HoSoCoThanhPhanSoHoa": data_2['data'][0]['HoSoCoThanhPhanSoHoa'] if 'HoSoCoThanhPhanSoHoa' in data_2['data'][0] else "0",
                    "LoaiDoiTuong": data_2['data'][0]['LoaiDoiTuong'] if 'LoaiDoiTuong' in data_2['data'][0] else "1",
                    "TrangThaiHoSo": "10",
                    "KenhThucHien": data_2['data'][0]['KenhThucHien'] if 'KenhThucHien' in data_2['data'][0] else "2",
                    "MaHoSo": data_2['data'][0]['MaHoSo'] if 'MaHoSo' in data_2['data'][0] else ma_ho_so,
                    "MaTTHC": data_2['data'][0]['MaTTHC'] if 'MaTTHC' in data_2['data'][0] else ma_tthc,
                    "DSKetNoiCSDL": data_2['data'][0]['DSKetNoiCSDL'] if 'DSKetNoiCSDL' in data_2['data'][0] else [{"MaCSDL":"7"}],
                    "NoiNopHoSo": data_2['data'][0]['NoiNopHoSo'] if 'NoiNopHoSo' in data_2['data'][0] else "2",
                    "TenLinhVuc": ten_linh_vuc,
                    "DonViXuLy": don_vi_xu_ly,
                    "NgayHenTra": data_1['result'][0]['NgayHenTra'],
                    "MaDoiTuong": data_2['data'][0]['MaDoiTuong'] if 'MaDoiTuong' in data_2['data'][0] else "",
                    "NgayTiepNhan": data_1['result'][0]['NgayTiepNhan'],
                    "DuocThanhToanTrucTuyen": data_2['data'][0]['DuocThanhToanTrucTuyen'] if 'DuocThanhToanTrucTuyen' in data_2['data'][0] else "1",
                    "NgayNopHoSo": data_2['data'][0]['NgayNopHoSo'] if 'NgayNopHoSo' in data_2['data'][0] else data_1['result'][0]['NgayTiepNhan'],
                    "TaiKhoanDuocXacThucVoiVNeID": data_2['data'][0]['TaiKhoanDuocXacThucVoiVNeID'] if 'TaiKhoanDuocXacThucVoiVNeID' in data_2['data'][0] else "1",
                    "MaLinhVuc": ma_linh_vuc,
                    "DinhDanhCHS": data_2['data'][0]['DinhDanhCHS'] if 'DinhDanhCHS' in data_2['data'][0] else [{"LoaiDinhDanh":"2","SoDinhDanh":SoDinhDanh}],
                    "ChuHoSo": data_2['data'][0]['ChuHoSo'],
                    "TaiLieuNop": tai_lieu_nop,
                    "DanhSachGiayToKetQua": danh_sach_giay_to_ket_qua,
                    "NgayTra": ngay_tra_kq,
                    "NgayKetThucXuLy": ngay_xu_ly_xong
                }

                data = {
                    "madonvi": "000.00.00.H12",
                    "data": [new_object],
                    "session": session,
                    "service": "DongBoHoSoMC",
                    "isUpdating": "true"
                }

                # Chuyển object thành JSON
                data_json = json.dumps(data)

                # Tạo lệnh curl
                curl_command = f"""curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{data_json}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g"""
                # print(data_json)    
                # Chạy lệnh curl
                response_3 = run_curl_command(curl_command)
        
                # Phân tích JSON
                data_3 = json.loads(response_3)
                # print(data_3)
        
                # Kiểm tra kết quả
                if data_3['error_code'] == '0':
                    success.append(ma_ho_so)
                else:
                    failure.append(ma_ho_so)
                # print(f"Đồng bộ hồ sơ {ma_ho_so} thành công")  
                update_progress_bar(total_elements, index + 1)              
        except Exception as e:
            failure.append(ma_ho_so)
            update_progress_bar(total_elements, index + 1)
            continue

        

    # Lưu kết quả vào file log
    # with open('success_log_t7.json', 'w') as success_file:
    #     json.dump(success, success_file)

    with open('failure_log.json', 'w') as failure_file:
        json.dump(failure, failure_file)
    print("\nHoàn thành!")
        

#chạy hàm main
if __name__ == "__main__":
    user_input = input("Tháng cần chạy: ")
    main(user_input)