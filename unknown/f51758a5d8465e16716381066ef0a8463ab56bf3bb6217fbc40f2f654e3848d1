import requests
import json
import pandas as pd

def check_api (ma_hs):
    # Replace with the URL you're sending the request to
    url = "https://quantri.dichvucong.gov.vn/web/jsp/submission/search-submission.jsp"

    # Replace these with the actual cookies and form data you want to send
    cookies = {
        'TS01c03a4c': '01f551f5ee5b4e3bfdb3a55c6c23a4c247eb72c6139d5017144e5fb0892857a523b47f9f019ab06d7287357ec5a45d0b6b42e2e603; JSESSIONID=74F34B031658E0C6821805FE14941217; TS01744849=01f551f5ee5b4e3bfdb3a55c6c23a4c247eb72c6139d5017144e5fb0892857a523b47f9f019ab06d7287357ec5a45d0b6b42e2e603; route=**********.213.974.477583; _ga=GA1.3.**********.**********; route=**********.555.1435.458743; TS0115bee1=01f551f5eea02df0669b6a98e49fc5f185823558d85fc778ce185c3f18c6fe1e6cdc7700546a7a722ba03784e70f78bcf281a46e90',
    }
    form_data = {
        'testing_submission': ma_hs,
    }

    # Make the POST request
    response = requests.post(url, cookies=cookies, data=form_data)

    # Convert the response to a Python dictionary
    data = json.loads(response.text)

    # Check for the existence of 'DanhSachGiayToKetQua' and 'DuongDanTepTinKetQua'
    result = 0
    
    if data:
        if 'DanhSachGiayToKetQua' in data and 'DuongDanTepTinKetQua' in data:
            result = 1
        else:
            result = 2
    else:
        result = 0            

    print(f'Kết quả kiểm tra hồ sơ {ma_hs}: {result}')
    return result


def main():
    # 1. Đọc dữ liệu từ file Excel
    file_path = 'v1/ds_thang_1.xlsx'
    df = pd.read_excel(file_path)

   # Danh sách để lưu kết quả
    results = []

    # Kiểm tra từng hàng trong DataFrame
    for index, row in df.iterrows():
        ma_hs = row['ma_hs']
        result = check_api(ma_hs)
        if result == 2:
            results.append(ma_hs)

    # Tạo DataFrame mới từ kết quả
    df_result = pd.DataFrame(results, columns=['ma_hs'])

    # Ghi kết quả vào file mới
    output_path = 'v1/ket_qua.xlsx'
    df_result.to_excel(output_path, index=False)

    print("Đã ghi kết quả vào file", output_path)

# check_api('000.00.14.H12-***********')
main()