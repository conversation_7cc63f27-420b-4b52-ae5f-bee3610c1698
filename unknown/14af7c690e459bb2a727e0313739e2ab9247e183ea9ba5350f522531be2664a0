import json
import random
import subprocess

def run_curl_command(curl_command):
    """ Ch<PERSON>y lệnh curl và trả về kết quả. """
    process = subprocess.Popen(curl_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        print(f"Error: {stderr}")
        return None
    else:
        return stdout.decode('utf-8')
    

def get_session():
    # Lệnh curl đầu tiên
    curl_command_1 = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/login
    """

    # Ch<PERSON><PERSON> lệnh curl đầu tiên và lưu response
    response_1 = run_curl_command(curl_command_1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session

def main(mahoso):
    session = get_session()

    #curl_command tra cứu hồ sơ
    #curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{"session":"MQ6p89Z6NHGUGtW/2rAUZWugIqQs2Lk3vyplQWmqmujutx/9DE8RSEUD194XgJDUljHIBk1BFeMYUQNLMGCHYkTPQcQRK6+FOse8T13WhH9PY/76nkp4HfJtFSQiDTA3","madonvi": "000.00.00.H12", "service": "TraCuuHoSo", "mahoso": "000.00.07.H12-241125-1750"}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g

    curl_command_2 = f"""
    curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8 -d '{{"session":"{session}","madonvi": "000.00.00.H12", "service": "TraCuuHoSo", "mahoso": "{mahoso}"}}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g """


    # Kết quả từ API đầu tiên
    response_1 = run_curl_command(curl_command_2)
    data = json.loads(response_1)
    #beautify json
    json_formatted_str = json.dumps(data, indent=2)
    print(json_formatted_str)

if __name__ == "__main__":
    user_input = input("Nhập mã hồ sơ: ")
    main(user_input)
