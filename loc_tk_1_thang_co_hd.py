from pymongo import MongoClient
import pandas as pd
from datetime import datetime, timedelta

# Kết nối tới MongoDB
client = MongoClient('*********************************************************************************************************************************')
sys_log_db = client['sysLog']
svc_human_db = client['svcHuman']
svc_sysman_db = client['svcSysman']

# Tính toán ngày từ 1 tháng trước
three_months_ago = datetime.now() - timedelta(days=30)

# Lấy danh sách userEvents.id từ bảng userEventsLog trong collection sysLog
user_events_log_collection = sys_log_db['userEventsLog']

user_events_ids = user_events_log_collection.find(
    {"createdDate": {"$gte": three_months_ago}, "clientId": "web-onegate"},
    {"userEvents.id": 1, "_id": 0}
)

user_events_ids_list = [event['userEvents']['id'] for event in user_events_ids]

#print count of user_events_ids_list\
print(len(user_events_ids_list))


# Lọc danh sách tài khoản trong bảng user của collection svcHuman
user_collection = svc_human_db['user']

filtered_users = user_collection.find(
    {
        # "role": {"$in": ["role/IGATE_MOT_CUA", "role/IGATE_XU_LY_HO_SO"]},
        "id": {"$nin": user_events_ids_list}
    }
)

# Lọc danh sách tài khoản trong bảng user của collection svcHuman
user_collection = svc_human_db['user']

# Tạo danh sách id của các user không có agency
# user_ids_without_agency = []
# for user in filtered_users:
#     if not user.get("experience", [{}])[0].get("agency"):
#         user_ids_without_agency.append(user["_id"])

# Cập nhật trạng thái enable:false trong collection svcSysman, bảng account
account_collection = svc_sysman_db['account']

# user_ids_without_agency_parent = []
# for user in filtered_users:
#     parent_name = user.get("experience", [{}])[0].get("agency", {}).get("parent", {}).get("name", [{}])[0].get("name", "")
#     if parent_name != "":
#         user_ids_without_agency_parent.append(user["_id"])

# print(f"Updated {len(user_ids_without_agency)} accounts to enable: false")


#count filtered_users
# count = 0
# for user in user_events_ids_list:
#     # distinct user
#     count += 1
# print(count)

# Đóng kết nối
client.close()