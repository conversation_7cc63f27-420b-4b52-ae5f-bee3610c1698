from datetime import datetime
from pymongo import MongoClient
from bson.son import SON
from bson import ObjectId
import pytz
import json
import os

# Connect to MongoDB
def connect_mongodb():
    client = MongoClient('********************************************************************************************************************************')
    db = client['svcPadman']
    collection = db['dossier']
    return collection

# Build criteria for MongoDB query
def build_criteria_statistics_sync_dvcqg(input):
    criteria_list = []

    timezone = pytz.timezone("Asia/Ho_Chi_Minh")
    from_date = datetime.strptime(input['fromDate'], "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)
    to_date = datetime.strptime(input['toDate'], "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)

    criteria_list.append({
        '$or': [
            {
                '$and': [
                    {'dossierStatus.id': {'$in': [6, 12, 13]}},
                    {'appliedDate': {'$gte': from_date}},
                    {'appliedDate': {'$lte': to_date}}
                ]
            },
            {
                '$and': [
                    {'appliedDate': {'$gte': from_date}},
                    {'appliedDate': {'$lte': to_date}}
                ]
            },
            {
                '$and': [
                    {'acceptedDate': {'$exists': True}},
                    {'acceptedDate': {'$lte': from_date}},
                    {'$or': [
                        {'dossierStatus.id': {'$in': [0, 1, 2, 3, 8, 9, 10, 11]}},
                        {
                            '$and': [
                                {'dossierStatus.id': {'$in': [4, 5]}},
                                {'completedDate': {'$gte': from_date}}
                            ]
                        }
                    ]}
                ]
            }
        ]
    })

    agency_temp = ObjectId(input['agencyId'])
    if input.get('isAncestors', False):
        criteria_list.append({
            '$or': [
                {'agency.id': agency_temp},
                {'agency.ancestors.id': agency_temp},
                {'agency.parent.id': agency_temp}
            ]
        })
    else:
        criteria_list.append({'agency.id': agency_temp})

    if criteria_list:
        criteria = {'$and': criteria_list}
    else:
        criteria = {}

    return criteria

# Build projection for MongoDB query
def build_projection_operation_sync_dvcqg(from_date_str, to_date_str):
    timezone = pytz.timezone("Asia/Ho_Chi_Minh")
    from_date = datetime.strptime(from_date_str, "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)
    to_date = datetime.strptime(to_date_str, "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)

    projection = {
        "procedureId": "$procedure.sector.id",
        "tiepNhanTrongKy": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                            ]
                        },
                        {
                            "$and": [
                                {"$gte": ["$acceptedDate", from_date]},
                                {"$lte": ["$acceptedDate", to_date]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "tiepNhanTrucTuyen": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$eq": ["$applyMethodId", 0]}
                            ]
                        },
                        {
                            "$and": [
                                {"$gte": ["$acceptedDate", from_date]},
                                {"$lte": ["$acceptedDate", to_date]},
                                {"$eq": ["$applyMethodId", 0]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "tiepNhanTrucTiep": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$eq": ["$applyMethodId", 1]}
                            ]
                        },
                        {
                            "$and": [
                                {"$gte": ["$acceptedDate", from_date]},
                                {"$lte": ["$acceptedDate", to_date]},
                                {"$eq": ["$applyMethodId", 1]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "kyTruocChuyenSang": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [0, 1, 2, 3, 8, 9, 10, 11]]},
                        {"$lt": ["$acceptedDate", from_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLy": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$in": ["$dossierStatusId", [6, 12, 13]]}
                            ]
                        },
                        {
                            "$and": [
                                {"$in": ["$dossierStatusId", [4, 5]]},
                                {"$lte": ["$completedDate", to_date]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLyDungHan": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$in": ["$dossierStatusId", [6, 12, 13]]}
                            ]
                        },
                        {
                            "$and": [
                                {"$in": ["$dossierStatusId", [4, 5]]},
                                {"$lte": ["$completedDate", to_date]},
                                {"$eq": ["$completedDate", "$appointmentDate"]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLySomHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [4, 5]]},
                        {"$lte": ["$completedDate", to_date]},
                        {"$lt": ["$completedDate", "$appointmentDate"]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLyQuaHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [4, 5]]},
                        {"$lte": ["$completedDate", to_date]},
                        {"$gt": ["$completedDate", "$appointmentDate"]},
                        {"$gt": ["$processTime", 0]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "dangXuLy": {
            "$cond": {
                "if": {
                    "$or": [
                        {"$in": ["$dossierStatusId", [0, 1, 2, 3, 8, 9, 10, 11]]},
                        {
                            "$and": [
                                {"$in": ["$dossierStatusId", [4, 5]]},
                                {"$gt": ["$completedDate", to_date]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "dangXuLyTrongHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {
                            "$or": [
                                {"$in": ["$dossierStatusId", [0, 1, 2, 3, 8, 9, 10, 11]]},
                                {
                                    "$and": [
                                        {"$in": ["$dossierStatusId", [4, 5]]},
                                        {"$gt": ["$completedDate", to_date]}
                                    ]
                                }
                            ]
                        },
                        {
                            "$or": [
                                {"$eq": ["$undefindedCompleteTime", 1]},
                                {"$eq": ["$dossierMenuTaskRemindId", ObjectId("60f6364e09cbf91d41f88859")]},
                                {"$in": ["$dossierStatusId", [1, 3]]},
                                {"$lt": ["$appointmentDate", 0]},
                                {
                                    "$and": [
                                        {"$eq": ["$undefindedCompleteTime", 0]},
                                        {"$gte": ["$appointmentDate", to_date]}
                                    ]
                                }
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "dangXuLyQuaHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {
                            "$or": [
                                {"$in": ["$dossierStatusId", [0, 2, 8, 9, 10, 11]]},
                                {
                                    "$and": [
                                        {"$in": ["$dossierStatusId", [4, 5]]},
                                        {"$gt": ["$completedDate", to_date]},
                                        {"$gt": ["$processTime", 0]}
                                    ]
                                }
                            ]
                        },
                        {"$ne": ["$dossierMenuTaskRemindId", ObjectId("60f6364e09cbf91d41f88859")]},
                        {"$eq": ["$undefindedCompleteTime", 0]},
                        {"$gt": ["$appointmentDate", 0]},
                        {"$lt": ["$appointmentDate", to_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "DungXuLy": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$eq": ["$dossierMenuTaskRemindId", ObjectId("60f52e6a09cbf91d41f88836")]},
                        {"$gte": ["$appliedDate", from_date]},
                        {"$lte": ["$appliedDate", to_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "TraLai": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [6]]},
                        {"$gte": ["$appliedDate", from_date]},
                        {"$lte": ["$appliedDate", to_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
    }

    return projection

# Process data for a specific agency and period
def process_data(collection, id_don_vi, id_ky, procedure_ids, input):
    # Define the aggregation pipeline
    pipeline = [
        {
            '$addFields': {
                'dossierStatusId': '$dossierStatus.id',
                'dossierMenuTaskRemindId': '$dossierMenuTaskRemind.id',
                'applyMethodId': '$applyMethod.id',
                'processTime': '$procedureProcessDefinition.processDefinition.processTime',
            }
        },
        {
            '$match': build_criteria_statistics_sync_dvcqg(input)
        },
        {
            '$project': build_projection_operation_sync_dvcqg(input['fromDate'], input['toDate'])
        },
        {
            '$group': {
                '_id': '$procedureId',
                'procedureId': {'$first': '$procedureId'},
                'received': {'$sum': '$tiepNhanTrongKy'},
                'onlineReceived': {'$sum': '$tiepNhanTrucTuyen'},
                'directReceived': {'$sum': '$tiepNhanTrucTiep'},
                'receivedOld': {'$sum': '$kyTruocChuyenSang'},
                'resolved': {'$sum': '$daXuLy'},
                'resolvedOnTime': {'$sum': '$daXuLyDungHan'},
                'resolvedEarly': {'$sum': '$daXuLySomHan'},
                'resolvedOverdue': {'$sum': '$daXuLyQuaHan'},
                'unresolved': {'$sum': '$dangXuLy'},
                'unresolvedOnTime': {'$sum': '$dangXuLyTrongHan'},
                'unresolvedOverdue': {'$sum': '$dangXuLyQuaHan'},
                'passed': {'$sum': '$DungXuLy'},
                'returned': {'$sum': '$TraLai'}
            }
        }
    ]

    # Perform the aggregation
    result = list(collection.aggregate(pipeline))
    
    # Check if the result is empty
    if not result:
        result = []
    
    # Prepare result data
    result_data = []
    
    for procedure_id in procedure_ids:
        # Initialize with default values
        stats = {
            "id_ky": id_ky,
            "id_don_vi": id_don_vi,
            "id_linh_vuc": procedure_id,
            "received": 0,
            "received_online": 0,
            "received_direct": 0,
            "received_old": 0,
            "total_processing": 0,
            "total_resolved": 0,
            "total_resolved_on_time": 0,
            "total_resolved_early": 0,
            "total_resolved_over_due": 0,
            "total_unresolved": 0,
            "total_unresolved_on_due": 0,
            "total_unresolved_over_due": 0,
            "total_paused": 0,
            "total_returned": 0
        }
        
        # Look for matching data in the result
        for result_item in result:
            procedure_id_str = str(result_item['procedureId'])
            if procedure_id_str == procedure_id:
                stats["received"] = result_item['received']
                stats["received_online"] = result_item['onlineReceived']
                stats["received_direct"] = result_item['directReceived']
                stats["received_old"] = result_item['receivedOld']
                stats["total_processing"] = result_item['received'] + result_item['receivedOld']
                stats["total_resolved"] = result_item['resolved']
                stats["total_resolved_on_time"] = result_item['resolvedOnTime']
                stats["total_resolved_early"] = result_item['resolvedEarly']
                stats["total_resolved_over_due"] = result_item['resolvedOverdue']
                stats["total_unresolved"] = result_item['unresolved']
                stats["total_unresolved_on_due"] = result_item['unresolvedOnTime']
                stats["total_unresolved_over_due"] = result_item['unresolvedOverdue']
                stats["total_returned"] = result_item['returned']
                stats["total_paused"] = result_item['passed']
                break
        
        result_data.append(stats)
    
    return result_data

def main():
    start_time = datetime.now()
    
    # Create output directory if it doesn't exist
    os.makedirs("data/results", exist_ok=True)
    
    # Load agencies data
    print("Loading agencies data...")
    with open("data/agencies.json", "r", encoding="utf-8") as f:
        agencies_data = json.load(f)
    
    # Load periods data
    print("Loading periods data...")
    with open("data/periods.json", "r", encoding="utf-8") as f:
        periods_data = json.load(f)
    
    # Connect to MongoDB
    print("Connecting to MongoDB...")
    collection = connect_mongodb()
    
    # Process data for each agency and period
    all_results = []
    
    for agency in agencies_data:
        id_don_vi = agency["id_don_vi"]
        ten_don_vi = agency["ten_don_vi"]
        procedure_ids = agency["procedure_ids"]
        
        print(f"Processing agency: {ten_don_vi}")
        
        for period in periods_data:
            id_ky = period["id_ky"]
            from_date = f"{period['hieu_luc_tu']}T00:00"
            to_date = f"{period['hieu_luc_den']}T23:59"
            
            print(f"  Processing period: {id_ky} ({from_date} to {to_date})")
            
            input_data = {
                'fromDate': from_date,
                'toDate': to_date,
                'agencyId': id_don_vi,
                'isAncestors': True
            }
            
            # Process data for this agency and period
            result_data = process_data(collection, id_don_vi, id_ky, procedure_ids, input_data)
            all_results.extend(result_data)
            
            print(f"  Processed {len(result_data)} procedure records")
    
    # Save all results to a single JSON file
    results_file = "data/results/all_results.json"
    with open(results_file, "w", encoding="utf-8") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    end_time = datetime.now()
    print(f"Time run script: {end_time - start_time}")
    print(f"Results saved to '{results_file}'")

if __name__ == '__main__':
    main()