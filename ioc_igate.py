from datetime import datetime
from pymongo import MongoClient
from bson.son import SON
from bson import ObjectId
import pytz
import mysql.connector
from mysql.connector import Error

def get_list_procedure_id_from_db(id_don_vi):
    connection = None
    try:
        connection = mysql.connector.connect(
            host="*************",
            user="rpt_cmu",
            password="Ioc@2024",
            database="cmu_db_dtm"
        )

        cursor = connection.cursor()
        query = "SELECT ID_LINH_VUC FROM igate_dm_linh_vuc_v2_0 WHERE ID_DON_VI = %s"
        cursor.execute(query, (id_don_vi,))
        result = cursor.fetchall()
        list_procedure_id = [row[0] for row in result]
        return list_procedure_id

    except Error as e:
        print(f"Error: {e}")
        return []


# Connect to MongoDB
client = MongoClient('********************************************************************************************************************************')
db = client['svcPadman']
collection = db['dossier']

# Define the helper functions `build_criteria_statistics_sync_dvcqg` and `build_projection_operation_sync_dvcqg`
def build_criteria_statistics_sync_dvcqg(input):
    criteria_list = []

    timezone = pytz.timezone("Asia/Ho_Chi_Minh")
    from_date = datetime.strptime(input['fromDate'], "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)
    to_date = datetime.strptime(input['toDate'], "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)

    criteria_list.append({
        '$or': [
            {
                '$and': [
                    {'dossierStatus.id': {'$in': [6, 12, 13]}},
                    {'appliedDate': {'$gte': from_date}},
                    {'appliedDate': {'$lte': to_date}}
                ]
            },
            {
                '$and': [
                    {'appliedDate': {'$gte': from_date}},
                    {'appliedDate': {'$lte': to_date}}
                ]
            },
            {
                '$and': [
                    {'acceptedDate': {'$exists': True}},
                    {'acceptedDate': {'$lte': from_date}},
                    {'$or': [
                        {'dossierStatus.id': {'$in': [0, 1, 2, 3, 8, 9, 10, 11]}},
                        {
                            '$and': [
                                {'dossierStatus.id': {'$in': [4, 5]}},
                                {'completedDate': {'$gte': from_date}}
                            ]
                        }
                    ]}
                ]
            }
        ]
    })

    agency_temp = ObjectId(input['agencyId'])
    if input.get('isAncestors', False):
        criteria_list.append({
            '$or': [
                {'agency.id': agency_temp},
                {'agency.ancestors.id': agency_temp},
                {'agency.parent.id': agency_temp}
            ]
        })
    else:
        criteria_list.append({'agency.id': agency_temp})

    if criteria_list:
        criteria = {'$and': criteria_list}
    else:
        criteria = {}

    return criteria

def build_projection_operation_sync_dvcqg(from_date_str, to_date_str):
    timezone = pytz.timezone("Asia/Ho_Chi_Minh")
    from_date = datetime.strptime(from_date_str, "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)
    to_date = datetime.strptime(to_date_str, "%Y-%m-%dT%H:%M").replace(tzinfo=timezone)

    projection = {
        "procedureId": "$procedure.sector.id",
        "tiepNhanTrongKy": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                # {"$in": ["$dossierStatusId", [6, 12, 13]]}
                            ]
                        },
                        {
                            "$and": [
                                {"$gte": ["$acceptedDate", from_date]},
                                {"$lte": ["$acceptedDate", to_date]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "tiepNhanTrucTuyen": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$eq": ["$applyMethodId", 0]}
                            ]
                        },
                        {
                            "$and": [
                                {"$gte": ["$acceptedDate", from_date]},
                                {"$lte": ["$acceptedDate", to_date]},
                                {"$eq": ["$applyMethodId", 0]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "tiepNhanTrucTiep": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$eq": ["$applyMethodId", 1]}
                            ]
                        },
                        {
                            "$and": [
                                {"$gte": ["$acceptedDate", from_date]},
                                {"$lte": ["$acceptedDate", to_date]},
                                {"$eq": ["$applyMethodId", 1]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "kyTruocChuyenSang": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [0, 1, 2, 3, 8, 9, 10, 11]]},
                        {"$lt": ["$acceptedDate", from_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLy": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$in": ["$dossierStatusId", [6, 12, 13]]}
                            ]
                        },
                        {
                            "$and": [
                                {"$in": ["$dossierStatusId", [4, 5]]},
                                {"$lte": ["$completedDate", to_date]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLyDungHan": {
            "$cond": {
                "if": {
                    "$or": [
                        {
                            "$and": [
                                {"$gte": ["$appliedDate", from_date]},
                                {"$lte": ["$appliedDate", to_date]},
                                {"$in": ["$dossierStatusId", [6, 12, 13]]}
                            ]
                        },
                        {
                            "$and": [
                                {"$in": ["$dossierStatusId", [4, 5]]},
                                {"$lte": ["$completedDate", to_date]},
                                {"$eq": ["$completedDate", "$appointmentDate"]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLySomHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [4, 5]]},
                        {"$lte": ["$completedDate", to_date]},
                        {"$lt": ["$completedDate", "$appointmentDate"]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLyQuaHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [4, 5]]},
                        {"$lte": ["$completedDate", to_date]},
                        {"$gt": ["$completedDate", "$appointmentDate"]},
                        {"$gt": ["$processTime", 0]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "dangXuLy": {
            "$cond": {
                "if": {
                    "$or": [
                        {"$in": ["$dossierStatusId", [0, 1, 2, 3, 8, 9, 10, 11]]},
                        {
                            "$and": [
                                {"$in": ["$dossierStatusId", [4, 5]]},
                                {"$gt": ["$completedDate", to_date]}
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "dangXuLyTrongHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {
                            "$or": [
                                {"$in": ["$dossierStatusId", [0, 1, 2, 3, 8, 9, 10, 11]]},
                                {
                                    "$and": [
                                        {"$in": ["$dossierStatusId", [4, 5]]},
                                        {"$gt": ["$completedDate", to_date]}
                                    ]
                                }
                            ]
                        },
                        {
                            "$or": [
                                {"$eq": ["$undefindedCompleteTime", 1]},
                                {"$eq": ["$dossierMenuTaskRemindId", ObjectId("60f6364e09cbf91d41f88859")]},
                                {"$in": ["$dossierStatusId", [1, 3]]},
                                {"$lt": ["$appointmentDate", 0]},
                                {
                                    "$and": [
                                        {"$eq": ["$undefindedCompleteTime", 0]},
                                        {"$gte": ["$appointmentDate", to_date]}
                                    ]
                                }
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "sdjkfl": {
            "$cond": {
                "if": {
                    "$and": [
                        {
                            "$or": [
                                {"$in": ["$dossierStatusId", [0, 2, 8, 9, 10, 11]]},
                                {
                                    "$and": [
                                        {"$in": ["$dossierStatusId", [4, 5]]},
                                        {"$gt": ["$completedDate", to_date]},
                                        {"$gt": ["$processTime", 0]}
                                    ]
                                }
                            ]
                        },
                        {"$ne": ["$dossierMenuTaskRemindId", ObjectId("60f6364e09cbf91d41f88859")]},
                        {"$eq": ["$undefindedCompleteTime", 0]},
                        {"$gt": ["$appointmentDate", 0]},
                        {"$lt": ["$appointmentDate", to_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "daXuLyTruocHan": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [4, 5]]},
                        {"$lte": ["$completedDate", to_date]},
                        {
                            "$or": [
                                {"$eq": ["$undefindedCompleteTime", 1]},
                                {"$lt": ["$appointmentDate", 0]},
                                {"$lt": ["$completedDate", 0]},
                                {
                                    "$and": [
                                        {"$eq": ["$undefindedCompleteTime", 0]},
                                        {"$lt": ["$completedDate", "$appointmentDate"]}
                                    ]
                                }
                            ]
                        }
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "DungXuLy": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$eq": ["$dossierMenuTaskRemindId", ObjectId("60f52e6a09cbf91d41f88836")]},
                        {"$gte": ["$appliedDate", from_date]},
                        {"$lte": ["$appliedDate", to_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
        "TraLai": {
            "$cond": {
                "if": {
                    "$and": [
                        {"$in": ["$dossierStatusId", [6]]},
                        {"$gte": ["$appliedDate", from_date]},
                        {"$lte": ["$appliedDate", to_date]}
                    ]
                },
                "then": 1,
                "else": 0
            }
        },
    }

    return projection

def insert_documents_to_mysql(docs):
    connection = None
    try:
        connection = mysql.connector.connect(
            host="*************",
            user="rpt_cmu",
            password="Ioc@2024",
            database="cmu_db_dtm"
        )
        cursor = connection.cursor()

        insert_query = """
        INSERT INTO igate_tk_don_vi_linh_vuc_v2_0 (
            ID_KY,
            ID_DONVI_LINHVUC,
            TIEP_NHAN,
            TIEP_NHAN_TRUC_TIEP,
            TIEP_NHAN_TRUC_TUYEN,
            DANG_XU_LY,
            DANG_XU_LY_TRONG_HAN,
            DANG_XU_LY_QUA_HAN,
            HOAN_THANH,
            HOAN_THANH_TRONG_HAN,
            HOAN_THANH_TRUOC_HAN,
            HOAN_THANH_TRE_HAN,
            DUNG_XU_LY,
            TRA_LAI
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            TIEP_NHAN = VALUES(TIEP_NHAN),
            TIEP_NHAN_TRUC_TIEP = VALUES(TIEP_NHAN_TRUC_TIEP),
            TIEP_NHAN_TRUC_TUYEN = VALUES(TIEP_NHAN_TRUC_TUYEN),
            DANG_XU_LY = VALUES(DANG_XU_LY),
            DANG_XU_LY_TRONG_HAN = VALUES(DANG_XU_LY_TRONG_HAN),
            DANG_XU_LY_QUA_HAN = VALUES(DANG_XU_LY_QUA_HAN),
            HOAN_THANH = VALUES(HOAN_THANH),
            HOAN_THANH_TRONG_HAN = VALUES(HOAN_THANH_TRONG_HAN),
            HOAN_THANH_TRUOC_HAN = VALUES(HOAN_THANH_TRUOC_HAN),
            HOAN_THANH_TRE_HAN = VALUES(HOAN_THANH_TRE_HAN),
            DUNG_XU_LY = VALUES(DUNG_XU_LY),
            TRA_LAI = VALUES(TRA_LAI)
        """
        for doc in docs:
            # print(doc.received)
            cursor.execute(insert_query, (
                doc.id_ky,
                doc.id_don_vi + doc.id_linh_vuc,
                doc.received,
                doc.received_direct,
                doc.received_online,
                doc.total_unresolved,
                doc.total_unresolved_on_due,
                doc.total_unresolved_over_due,
                doc.total_resolved,
                doc.total_resolved_on_time,
                doc.total_resolved_early,
                doc.total_resolved_over_due,
                doc.total_paused,
                doc.total_returned
            ))
        connection.commit()
    except Error as e:
        print(f"Error: {e}")
    
class DossierStatisticsAllReportOUTDVCQGDto:
    def __init__(self):

        self.id_ky = None
        self.id_don_vi = None
        self.id_linh_vuc = None
        self.received = 0
        self.received_online = 0
        self.received_direct = 0
        self.received_old = 0
        self.total_processing = 0
        self.total_resolved = 0
        self.total_resolved_on_time = 0
        self.total_resolved_early = 0
        self.total_resolved_over_due = 0
        self.total_unresolved = 0
        self.total_unresolved_on_due = 0
        self.total_unresolved_over_due = 0
        self.total_paused = 0
        self.total_returned = 0

    def set_id_ky(self, value):
        self.id_ky = value
    def get_id_ky(self):
        return self.id_ky    

    def set_id_don_vi(self, value):
        self.id_don_vi = value
    def get_id_don_vi(self):
        return self.id_don_vi

    def set_id_linh_vuc(self, value):
        self.id_linh_vuc = value
    def get_id_linh_vuc(self):
        return self.id_linh_vuc    

    def set_received(self, value):
        self.received = value
    def get_received(self):
        return self.received
    
    def set_received_online(self, value):
        self.received_online = value
    def get_received_online(self):
        return self.received_online
    
    def set_received_direct(self, value):
        self.received_direct = value
    def get_received_direct(self):
        return self.received_direct

    def set_received_old(self, value):
        self.received_old = value
    def get_received_old(self):
        return self.received_old

    def set_total_processing(self, value):
        self.total_processing = value
    def get_total_processing(self):
        return self.total_processing

    def set_total_resolved(self, value):
        self.total_resolved = value
    def get_total_resolved(self):
        return self.total_resolved

    def set_total_resolved_on_time(self, value):
        self.total_resolved_on_time = value
    def get_total_resolved_on_time(self):
        return self.total_resolved_on_time

    def set_total_resolved_early(self, value):
        self.total_resolved_early = value
    def get_total_resolved_early(self):
        return self.total_resolved_early

    def set_total_resolved_over_due(self, value):
        self.total_resolved_over_due = value
    def get_total_resolved_over_due(self):
        return self.total_resolved_over_due

    def set_total_unresolved(self, value):
        self.total_unresolved = value
    def get_total_unresolved(self):
        return self.total_unresolved

    def set_total_unresolved_on_due(self, value):
        self.total_unresolved_on_due = value
    def get_total_unresolved_on_due(self):
        return self.total_unresolved_on_due

    def set_total_unresolved_over_due(self, value):
        self.total_unresolved_over_due = value
    def get_total_unresolved_over_due(self):
        return self.total_unresolved_over_due
    
    def set_total_paused(self, value):
        self.total_paused = value
    def get_total_paused(self):
        return self.total_paused

    def set_total_returned(self, value):
        self.total_returned = value
    def get_total_returned(self):
        return self.total_returned
    
def get_list_dm_ky(thang, nam):
    connection = None
    try:
        connection = mysql.connector.connect(
            host="*************",
            user="etl_cmu",
            password="Ioc@2024",
            database="cmu_db_dtm"
        )
        cursor = connection.cursor()
        query = "SELECT ID_KY, GIA_TRI_NGAY, HIEU_LUC_TU, HIEU_LUC_DEN FROM dm_ky WHERE GIA_TRI_THANG = %s and GIA_TRI_NAM = %s and LOAI_KY = 'M'"
        cursor.execute(query, (thang, nam))
        result = cursor.fetchall()
        return result
    except Error as e:
        print(f"Error: {e}")
        return []

def process_data(id_don_vi, id_ky, input):
    # Define the aggregation pipeline
    pipeline = [
        {
            '$addFields': {
                'dossierStatusId': '$dossierStatus.id',
                'dossierMenuTaskRemindId': '$dossierMenuTaskRemind.id',
                'applyMethodId': '$applyMethod.id',
                'processTime': '$procedureProcessDefinition.processDefinition.processTime',
            }
        },
        {
            '$match': build_criteria_statistics_sync_dvcqg(input)
        },
        {
            '$project': build_projection_operation_sync_dvcqg(input['fromDate'], input['toDate'])
        },
        {
            '$group': {
                '_id': '$procedureId',
                'procedureId': {'$first': '$procedureId'},
                'received': {'$sum': '$tiepNhanTrongKy'},
                'onlineReceived': {'$sum': '$tiepNhanTrucTuyen'},
                'directReceived': {'$sum': '$tiepNhanTrucTiep'},
                'receivedOld': {'$sum': '$kyTruocChuyenSang'},
                'resolved': {'$sum': '$daXuLy'},
                'resolvedOnTime': {'$sum': '$daXuLyDungHan'},
                'resolvedEarly': {'$sum': '$daXuLySomHan'},
                'resolvedOverdue': {'$sum': '$daXuLyQuaHan'},
                'unresolved': {'$sum': '$dangXuLy'},
                'unresolvedOnTime': {'$sum': '$dangXuLyTrongHan'},
                'unresolvedOverdue': {'$sum': '$dangXuLyQuaHan'},
                'passed': {'$sum': '$DungXuLy'},
                'returned': {'$sum': '$TraLai'}
            }
        }
    ]

    # Perform the aggregation
    result = list(collection.aggregate(pipeline))
    # print(result)

    # Get list procedure id from database
    list_procedure_id = get_list_procedure_id_from_db(id_don_vi)
    # print(list_procedure_id)

    # Check if the result is empty
    if not result:
        result = []
    
    # list DossierStatisticsAllReportOUTDVCQGDto
    list_rs = []

    for procedure_id in list_procedure_id:
        dossier_statistics_all_report_dto = DossierStatisticsAllReportOUTDVCQGDto()
        dossier_statistics_all_report_dto.set_id_ky(id_ky)
        dossier_statistics_all_report_dto.set_id_don_vi(id_don_vi)
        dossier_statistics_all_report_dto.set_id_linh_vuc(procedure_id)
        
        #for result_item in result:
        for result_item in result:
            procedure_id_str = str(result_item['procedureId'])
            if procedure_id_str == procedure_id:
                dossier_statistics_all_report_dto.set_received(
                    result_item['received']
                )
                dossier_statistics_all_report_dto.set_received_online(
                    result_item['onlineReceived']
                )
                dossier_statistics_all_report_dto.set_received_direct(
                    result_item['directReceived']
                )
                dossier_statistics_all_report_dto.set_received_old(
                   result_item['receivedOld']
                )
                dossier_statistics_all_report_dto.set_total_processing(
                    result_item['received'] + result_item['receivedOld']
                )
                dossier_statistics_all_report_dto.set_total_resolved(
                    result_item['resolved']
                )
                dossier_statistics_all_report_dto.set_total_resolved_on_time(
                    result_item['resolvedOnTime']
                )
                dossier_statistics_all_report_dto.set_total_resolved_early(
                    result_item['resolvedEarly']
                )
                dossier_statistics_all_report_dto.set_total_resolved_over_due(
                    result_item['resolvedOverdue']
                )
                dossier_statistics_all_report_dto.set_total_unresolved(
                    result_item['unresolved']
                )
                dossier_statistics_all_report_dto.set_total_unresolved_on_due(
                    result_item['unresolvedOnTime']
                )
                dossier_statistics_all_report_dto.set_total_unresolved_over_due(
                    result_item['unresolvedOverdue']
                )
                dossier_statistics_all_report_dto.set_total_returned(
                    result_item['returned']
                )
                dossier_statistics_all_report_dto.set_total_paused(
                    result_item['passed']
                )
                break
           
                
        list_rs.append(dossier_statistics_all_report_dto)   

    # Insert documents to MySQL
    insert_documents_to_mysql(list_rs)
    return 1

def get_list_co_quan():
    connection = None 
    try:
        connection = mysql.connector.connect(
            host="*************",
            user="etl_cmu",
            password="Ioc@2024",
            database="cmu_db_dtm"
        )
        cursor = connection.cursor()
        query = "SELECT ID_DON_VI, TEN_DON_VI FROM igate_api_dm_co_quan_v2_0"
        cursor.execute(query)
        result = cursor.fetchall()
        return result
    except Error as e:
        print(f"Error: {e}")
        return []

def get_list_don_vi():
    connection = None 
    try:
        connection = mysql.connector.connect(
            host="*************",
            user="etl_cmu",
            password="Ioc@2024",
            database="cmu_db_dtm"
        )
        cursor = connection.cursor()

        query = "SELECT ID_DON_VI, TEN_DON_VI FROM igate_api_dm_don_vi_v2_0_1 WHERE ID_DON_VI NOT IN ('637d7434f217d52a06d6d0f4')"
        cursor.execute(query)
        result = cursor.fetchall()
        return result
    except Error as e:
        print(f"Error: {e}")
        return []


def main():
    start_time = datetime.now()
    thang = 3
    list_don_vi = get_list_co_quan()
    
    for don_vi in list_don_vi:
        id_don_vi = don_vi[0]
        print(f"Đang chạy đơn vị: {don_vi[1]}")
        
        list_ky = get_list_dm_ky(thang, 2025)
        for ky in list_ky:
            id_ky = ky[0]
            # ngay = ky[1].strftime("%Y-%m-%d")
            tu_ngay = ky[2].strftime("%Y-%m-%d")
            den_ngay = ky[3].strftime("%Y-%m-%d")
            from_date = f"{tu_ngay}T00:00"
            to_date = f"{den_ngay}T23:59"
            input = {
                'fromDate': from_date,
                'toDate': to_date,
                'agencyId': id_don_vi,
                'isAncestors': True
            }
            rs = process_data(id_don_vi, id_ky, input)
            if rs == 1:
                print(f"Process data for ky {id_ky} {don_vi[1]} success")
            else:
                print(f"Process data for ky {id_ky} {don_vi[1]} failed")
        
    end_time = datetime.now()
    print(f"Time run script: {end_time - start_time}")
    

if __name__ == '__main__':
    main()
