from pymongo import MongoClient
import pandas as pd
from datetime import datetime
import pytz
import json

# <PERSON>ết nối tới MongoDB
client = MongoClient('*********************************************************************************************************************************')
sys_log_db = client['sysLog']
svc_human_db = client['svcHuman']
svc_sysman_db = client['svcSysman']

list_user = []
with open('input/userEventsLog.json') as f:
    user_events_ids_list = json.load(f)
    for user in user_events_ids_list:
        try:
            # Check if 'userEvents' and 'id' exist and access '$oid'
            if 'userEvents' in user and 'id' in user['userEvents']:
                list_user.append(user['userEvents']['id']['$oid'])
           
        except KeyError as e:
            print(f"KeyError: {e} in user: {user}")

# remmove duplicate id
list_user = list(set(list_user))

# print(f"List user: {list_user}. Length: {len(list_user)}")
# Lọc danh sách tài khoản trong bảng user của collection svcHuman
user_collection = svc_human_db['user']

filtered_users = user_collection.find(
    {
        "role": {"$in": ["role/IGATE_MOT_CUA", "role/IGATE_XU_LY_HO_SO"]},
        "account.enable": True,

    }
)

user_data = []
so_tt = 0
for user in filtered_users:
    # check if user in user_events_ids_list then check x
    status = ""
    for id in list_user:
        if str(user["_id"]) == id:
            status = ""
            break
        else:
            status = "X"
    fullname = user.get("fullname", "")
    username = user.get("account", {}).get("username", [{}])[0].get("value", "")
    agency_name = user.get("experience", [{}])[0].get("agency", {}).get("name", [{}])[0].get("name", "")
    parent_name = user.get("experience", [{}])[0].get("agency", {}).get("parent", {}).get("name", [{}])[0].get("name", "")
    if parent_name != "":
        so_tt += 1
        user_data.append([so_tt,fullname, username, agency_name, parent_name,status])

# Chuyển dữ liệu thành DataFrame
df = pd.DataFrame(user_data, columns=["Số TT","Họ tên cán bộ", "Tài khoản", "Phòng ban", "Đơn vị", "3 tháng không hoạt động"])

# Sắp xếp theo "Parent Name"
df_sorted = df.sort_values(by=["Đơn vị"])

# Xuất ra file Excel
output_file = "Danh_sach_tai_khoan_mot_cua.xlsx"
df_sorted.to_excel(output_file, index=False)

print(f"Data has been exported to {output_file}")

# Đóng kết nối
client.close()