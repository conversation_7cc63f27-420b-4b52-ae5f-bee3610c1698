{"_id": {"$oid": "6531e0e4f6651a49865b4142"}, "code": "000.00.26.H12-231020-0201", "codePattern": {"id": {"$oid": "644746c6bc8884294b47393a"}}, "procedure": {"id": {"$oid": "652dedabe8b67510552d43f0"}, "code": "1.003003.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON><PERSON>ng ký và cấp <PERSON><PERSON><PERSON><PERSON> chứng nhận quyền sử dụng đất, quyền sở hữu nhà ở và tài sản khác gắn liền với đất lần đầu"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}]}}, "procedureProcessDefinition": {"id": {"$oid": "652dedeee8b67510552d4416"}, "processDefinition": {"id": {"$oid": "652dec54e1553b068aebf03d"}, "processingTime": 21, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d474ac56bc1001fc6e127"}, "name": "HCC iLis thong tin chung TVT"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bfc3af4d70261e45f231"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "activiti": {"id": "Process_aqxi1eXIi:341:e4d95585-6c91-11ee-8dd7-a2137e48eaaf", "model": {"_id": "28a8edd0-44fa-4473-a08d-7fc0aff607fd", "name": "qui-trinh-ilis", "project": {"id": "4058239e-3d80-436d-acb6-21b67a2793e2"}}}}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca83b80e603d5300dcf4"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "applicant": {"eformId": {"$oid": "652d474ac56bc1001fc6e127"}, "userId": {"$oid": "6531dfba0ec8eb131c3ca563"}, "data": {"birthday": "1960-02-19T00:00:00.000+0000", "note": "", "gender": "", "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON><PERSON>", "value": "5def47c5f47614018c001968"}, "identityDate": "2021-10-03T00:00:00+07:00", "nycSoGiayToTuyThan": "096160005180", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "NGUYỄN THỊ TÉM", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096160005180", "serialMap": "", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phong Lạc", "value": "5def47c5f47614018c132125"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "Xã Phong Lạc", "value": "5def47c5f47614018c132125"}, "pdg": false, "serialLand": "", "phoneNumber": "0813489823", "declarationForm": {"identifyNo": "096160005180", "phone": "0813489823", "fullName": "NGUYỄN THỊ TÉM", "birthDateStr": "19/02/1960", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON><PERSON>", "value": "5def47c5f47614018c001968"}, "diaChiThuaDat": "<PERSON><PERSON>", "fullname": "NGUYỄN THỊ TÉM"}}, "accepter": {"id": {"$oid": "6529f9b662b6b74b459bbc56"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bfc3af4d70261e45f231"}, "code": "000.00.26.H12", "name": [{"languageId": 228, "name": "Bộ phận tiếp nhận và trả kết quả UB TVT"}], "parent": {"id": {"$oid": "64093a91af4d70261e45f1c9"}, "code": "000.00.26.H12", "name": [{"languageId": 228, "name": "UBND huyện T<PERSON>ần <PERSON>"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "64093a91af4d70261e45f1c9"}, "name": [{"languageId": 228, "name": "UBND huyện T<PERSON>ần <PERSON>"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-10-20T02:16:17.000Z"}, "appliedDate": {"$date": "2023-10-20T02:07:32.872Z"}, "appointmentDate": {"$date": "2023-12-26T08:55:00.000Z"}, "completedDate": {"$date": "2023-12-06T09:48:45.475Z"}, "returnedDate": {"$date": "2023-12-08T07:19:04.065Z"}, "attachment": [{"id": {"$oid": "6570438464628e6c0f9d94dd"}, "filename": "000.00.26.H12-231020-0201-KQ.pdf", "size": {"$numberLong": "453711"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "656459749b3937670ff75f8c"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bfc3af4d70261e45f231"}, "code": "000.00.26.H12", "name": [{"languageId": 228, "name": "Bộ phận tiếp nhận và trả kết quả UB TVT"}], "parent": {"id": {"$oid": "64093a91af4d70261e45f1c9"}, "name": [{"languageId": 228, "name": "UBND huyện T<PERSON>ần <PERSON>"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "64093a91af4d70261e45f1c9"}, "name": [{"languageId": 228, "name": "UBND huyện T<PERSON>ần <PERSON>"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:341:e4d95585-6c91-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bfc3af4d70261e45f231"}, "code": "000.00.26.H12", "name": [{"languageId": 228, "name": "Bộ phận tiếp nhận và trả kết quả UB TVT"}], "parent": {"id": {"$oid": "64093a91af4d70261e45f1c9"}, "name": [{"languageId": 228, "name": "UBND huyện T<PERSON>ần <PERSON>"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "64093a91af4d70261e45f1c9"}, "name": [{"languageId": 228, "name": "UBND huyện T<PERSON>ần <PERSON>"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-27T08:55:15.000Z"}, "completedDate": {"$date": "2023-11-27T08:55:33.474Z"}, "dueDate": {"$date": "2023-11-28T02:55:00.000Z"}, "createdDate": {"$date": "2023-11-27T08:55:15.000Z"}, "updatedDate": {"$date": "2023-11-27T08:55:33.474Z"}, "activitiTask": {"id": "af1935f3-8d02-11ee-a9b4-def07e7d02be", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652dec54e1553b068aebf03e"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "656459845c87b40c1fb39f34"}, "assignee": {"id": {"$oid": "000000000000000000001111"}, "fullname": "<PERSON><PERSON> b<PERSON> ILIS", "account": {"id": {"$oid": "6510fd5e2d64b442285e5360"}, "username": [{"value": "canbo_ilis"}]}}, "candidateGroup": [{"id": {"$oid": "64093a91af4d70261e45f1c9"}, "code": "000.00.26.H12", "name": [{"languageId": 228, "name": "UBND huyện T<PERSON>ần <PERSON>"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:341:e4d95585-6c91-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "656459749b3937670ff75f8c"}}, "agency": {"id": {"$oid": "64093a91af4d70261e45f1c9"}, "code": "000.00.26.H12", "name": [{"languageId": 228, "name": "UBND huyện T<PERSON>ần <PERSON>"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-27T08:55:32.480Z"}, "completedDate": {"$date": "2023-12-06T09:48:48.149Z"}, "dueDate": {"$date": "2023-12-25T08:55:00.000Z"}, "claimDate": {"$date": "2023-11-27T08:55:32.480Z"}, "createdDate": {"$date": "2023-11-27T08:55:32.480Z"}, "updatedDate": {"$date": "2023-12-06T09:48:48.149Z"}, "activitiTask": {"id": "b8c4cbb6-8d02-11ee-a8fd-6e9105284082", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652dec54e1553b068aebf03f"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 20, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6570437fc3d6eb2da2c4bdbf"}, "assignee": {"id": {"$oid": "6529f9b662b6b74b459bbc56"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "6529f9b694d7072e39bb9f4c"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bfc3af4d70261e45f231"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a91af4d70261e45f1c9"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:341:e4d95585-6c91-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "656459845c87b40c1fb39f34"}}, "agency": {"id": {"$oid": "6411bfc3af4d70261e45f231"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a91af4d70261e45f1c9"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-12-06T09:48:47.165Z"}, "completedDate": {"$date": "2023-12-08T07:19:04.065Z"}, "dueDate": {"$date": "2023-12-07T03:48:00.000Z"}, "createdDate": {"$date": "2023-12-06T09:48:47.165Z"}, "updatedDate": {"$date": "2023-12-08T07:19:04.065Z"}, "activitiTask": {"id": "a6aaa37b-941c-11ee-bb10-323030e15a49", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652dec54e1553b068aebf040"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6570437fc3d6eb2da2c4bdbf"}, "assignee": {"id": {"$oid": "6529f9b662b6b74b459bbc56"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "6529f9b694d7072e39bb9f4c"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bfc3af4d70261e45f231"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a91af4d70261e45f1c9"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:341:e4d95585-6c91-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "656459845c87b40c1fb39f34"}}, "agency": {"id": {"$oid": "6411bfc3af4d70261e45f231"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "64093a91af4d70261e45f1c9"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-12-06T09:48:47.165Z"}, "completedDate": {"$date": "2023-12-08T07:19:04.065Z"}, "dueDate": {"$date": "2023-12-07T03:48:00.000Z"}, "createdDate": {"$date": "2023-12-06T09:48:47.165Z"}, "updatedDate": {"$date": "2023-12-08T07:19:04.065Z"}, "activitiTask": {"id": "a6aaa37b-941c-11ee-bb10-323030e15a49", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652dec54e1553b068aebf040"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "af190ede-8d02-11ee-a9b4-def07e7d02be", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1960-02-19T00:00:00.000+0000", "idMoneyReceipt": "", "identityDate": "", "nycSoGiayToTuyThan": "096160005180", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096160005180", "phone": "0813489823", "fullName": "NGUYỄN THỊ TÉM", "birthDateStr": "19/02/1960", "email": "", "idTypeId": 3}, "phoneNumber": "0813489823", "nycHoTen": "NGUYỄN THỊ TÉM", "identityNumber": "096160005180", "fullname": "NGUYỄN THỊ TÉM", "email": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-20T02:07:32.872Z"}, "updatedDate": {"$date": "2023-12-08T07:19:14.456Z"}, "sync": {"id": 1, "sourceCode": "000.00.26.H12-231020-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: o8AvcWoIKDEeoPPkxmQ2uYJ9Ud/G47gY0TW4QdNfQaygC6PoUaGkuQ\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5f39f4335224cf235e134c5b"}, "name": [{"languageId": 228, "name": "Cấp Tỉnh"}, {"languageId": 46, "name": "Province"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "approvalData": {"type": 8, "date": {"$date": "2023-10-30T03:22:52.192Z"}, "attachment": [{"id": {"$oid": "653f218f26658a52baed1ee5"}, "filename": "6681_ 6682_<PERSON><PERSON><PERSON><PERSON> (CM).pdf", "size": 8042844, "additionalFlag": 0, "updateCount": 0}], "status": 0}, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": true, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 0, "additionalRequirementDetail": {"count": 1, "acceptedDate": {"$date": "2023-11-27T08:55:15.000Z"}, "latestAdditionalDate": {"$date": "2023-10-20T02:07:58.341Z"}, "numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}