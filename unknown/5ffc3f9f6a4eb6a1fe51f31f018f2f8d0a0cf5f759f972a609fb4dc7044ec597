import pandas as pd
from pymongo import MongoClient

# Step 1: Read the Excel file
file_path = 'excel/csdldc.xlsx'
df = pd.read_excel(file_path)

# Step 2: Connect to MongoDB
client = MongoClient('*********************************************************************************************************************************')  # Adjust your MongoDB connection string
db = client['svcHuman']
collection = db['user']

# Step 3: Query MongoDB and update the DataFrame
for index, row in df.iterrows():
    full_name = row['Họ và tên']  # Assuming column B contains the full names
    print(f"Processing record for: {full_name}")
    query_result = collection.find_one({"fullname": full_name,"type":3})
    print(query_result)
    
    if query_result:
        identity_number = query_result.get('identity', {}).get('number', '')
        # Handle 'account' as a list
        username_value = ''
        account_info = query_result.get('account', {})
        username_list = account_info.get('username', [])
        if isinstance(username_list, list) and len(username_list) > 0:
            username_value = username_list[0].get('value', '')
        # Update columns C and F in the DataFrame
        df.at[index, 'Số CCCD'] = identity_number
        df.at[index, 'Tên tài khoản'] = username_value

# Step 4: Save the updated Excel file
output_file_path = 'excel/updated_csdldc.xlsx'
df.to_excel(output_file_path, index=False)

print(f"Updated Excel file saved as {output_file_path}")
