import asyncio
import json
import random
import pymongo
import traceback
from datetime import datetime, timezone, timedelta
from bson import ObjectId
import httpx
from openpyxl import Workbook

# Connect to MongoDB
mongo_uri = '*********************************************************************************************************************************'
client = pymongo.MongoClient(mongo_uri)

async def getToken():
    # from api
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = 'grant_type=password&username=admin&password=iGate#Cmu@2024&client_id=web-onegate'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }


    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.post(url, data=payload, headers=headers)
        access_token = response.json()['access_token']
    return access_token

async def syncQG(code, token):
    url = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"
    # token = await getToken()
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer '+token,
        'Cookie': 'JSESSIONID=979D1F437D51C126FF15E5BA8BE0E768'
    }

    payload = json.dumps([code])

    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.post(url, data=payload, headers=headers)
        if response.status_code == 200:
            if response.json()['affectedRows'] == 1:
                return 200
            else:
                return 500
        else:
            return 501

# Function query the database and return a list of documents
def get_documents():
    database_name = 'svcPadman' 
    collection_name = 'dossier'  
    database = client[database_name]
    collection = database[collection_name]

    with client.start_session() as session:
        documents = collection.find({
            '$and': [
                { 'nationCode': { '$exists': True } },
                { '$where': 'this.completedDate > this.appointmentDate' },
                { 'createdDate': { '$gt': datetime(2023, 12, 31, 23, 59, 59, tzinfo=timezone.utc) } }
            ]
        }, no_cursor_timeout=True, session=session).limit(10000)
        return list(documents)

def update_file(code):
    database_name = 'svcPadman' 
    collection_name = 'dossier'  
    database = client[database_name]
    collection = database[collection_name]
    rs = "fail"
    if code:
        document = collection.find_one({"code": code})
        if 'appointmentDate' in document:
            appointment_date = document['appointmentDate']
            print(f"Updating document with code: {document['appointmentDate']}")
                
            # Check if the appointment_date is not already a datetime object
            if isinstance(appointment_date, str):
                appointment_date = datetime.strptime(appointment_date, '%Y-%m-%dT%H:%M:%S.%fZ')
                
            # Subtract a random number of hours between 1 and 4 from the appointment date
            completedate = appointment_date - timedelta(hours=random.uniform(1, 4))
                
            # Update the document with the new completedate (which is a datetime object)
            collection.update_one({"_id": document['_id']}, {"$set": {"completedDate": completedate}})
            rs = "ok"
           
        else:
            rs = "fail"
    else:
        rs = "fail"
    return rs

async def check_queue(code_value):
    database_name = 'svcAdapter'
    collection_name = 'integratedReCallEvent'
    database = client[database_name]
    collection = database[collection_name]

    query = {
        "data.dossier.code": code_value,
        "status": 0
        }

    while True:
        # Đếm số lượng tài liệu phù hợp
        count = collection.count_documents(query)
        print(f"Số lượng tài liệu hiện tại: {count}")

        # Kiểm tra nếu số lượng dòng là 0 thì thoát khỏi vòng lặp
        if count == 0:
            print("Không còn tài liệu nào phù hợp.")
            return True
        # Chờ một khoảng thời gian trước khi kiểm tra lại
        await asyncio.sleep(3)  # Kiểm tra lại sau 3 giây

async def main():
    date = datetime.now().strftime("%d/%m/%Y")
    count = 0
    count_sync = 0
    #call get token one time
    token = await getToken()
    try:
        # Get the list of documents with no_cursor_timeout
        documents = get_documents()

        # Create a new Excel workbook
        wb = Workbook()
        ws = wb.active
        ws.append(["code", "status"])  # Add a header row

        for document in documents:
            try:
                code = document['code']
                ws.append([code])  # Append code to the Excel sheet
                print(f"Processing dossier with code: {code}")
                # dossier_id = document['_id']
                rs = update_file(code)
                if rs == "ok":
                    response = await syncQG(code, token)
                    if response == 200:
                        if await check_queue(code):
                            count_sync += 1
                            print(f"Sync dossier with code: {code} success")
                            ws.cell(row=count_sync+1, column=2, value="success")
                        else:
                            print(f"Sync dossier with code: {code} fail")
                            ws.cell(row=count_sync+1, column=2, value="fail")
                    else:
                        print(f"Sync dossier with code: {code} fail")
                        ws.cell(row=count_sync+1, column=2, value="fail")
                    count += 1
                else:
                    print(f"Failed to update for dossier: {code}")
                    continue
            except Exception as doc_exception:
                print(f"Failed to process document: {doc_exception}")
                traceback.print_exc()
                # Bạn có thể đặt mã xử lý khác tại đây nếu cần
                
    except Exception as e:
        print(f"Failed to get documents: {e}")
        traceback.print_exc()
        # Bạn có thể xử lý ngoại lệ chung cho vòng lặp ở đây nếu cần
    finally:
        # Save the workbook to a file
        filename = date.split("/")[0]+"_"+date.split("/")[1]+"_"+date.split("/")[2]
        wb.save("excel/"+filename+".xlsx")
        print(f"Processed {count} documents.")
        print(f"Sync {count_sync} documents.")

        
# Run the main function at 18:00
asyncio.run(main())
