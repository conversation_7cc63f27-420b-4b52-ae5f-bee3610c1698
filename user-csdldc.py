import pandas as pd
from pymongo import MongoClient

# Step 1: Read the Excel file
file_path = 'excel/csdldc.xlsx'
df = pd.read_excel(file_path)

# Step 2: Connect to MongoDB
client = MongoClient('*************************************************************************************************************************************') 
db = client['svcHuman']
collection = db['user']

# Step 3: Query MongoDB and update the DataFrame
for index, row in df.iterrows():
    full_name = row['Họ và tên']  # Assuming column B contains the full names
    print(f"Processing record for: {full_name}")
    query_result = collection.find_one({"fullname": full_name, "type": 3, "role": "role/CSDLDC"})
    print(query_result)

    if query_result:
        identity_number = query_result.get('identity', {}).get('number', '')
        # Handle 'account' as a list
        username_value = ''
        account_info = query_result.get('account', {})
        username_list = account_info.get('username', [])
        if isinstance(username_list, list) and len(username_list) > 0:
            username_value = username_list[0].get('value', '')

        # Get experience information
        parent_name = ''
        agency_name = ''
        experience_list = query_result.get('experience', [])
        if isinstance(experience_list, list) and len(experience_list) > 0:
            experience = experience_list[0]  # Get first experience
            agency = experience.get('agency', {})

            # Get agency name
            agency_name_list = agency.get('name', [])
            if isinstance(agency_name_list, list) and len(agency_name_list) > 0:
                agency_name = agency_name_list[0].get('name', '')

            # Get parent name
            parent = agency.get('parent', {})
            parent_name_list = parent.get('name', [])
            if isinstance(parent_name_list, list) and len(parent_name_list) > 0:
                parent_name = parent_name_list[0].get('name', '')

        # Update columns in the DataFrame
        df.at[index, 'Số CCCD'] = identity_number
        df.at[index, 'Tên tài khoản'] = username_value
        df.at[index, 'Đơn vị cha'] = parent_name
        df.at[index, 'Đơn vị'] = agency_name

# Step 4: Save the updated Excel file
output_file_path = 'excel/updated_csdldc.xlsx'
df.to_excel(output_file_path, index=False)

print(f"Updated Excel file saved as {output_file_path}")
