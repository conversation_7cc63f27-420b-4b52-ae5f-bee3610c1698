import pandas as pd
from pymongo import MongoClient

# Connect to MongoDB
client = MongoClient('*************************************************************************************************************************************') 
db = client['svcHuman']
collection = db['user']

# Query MongoDB with the specified conditions
query = {"type": 3, "role": "role/CSDLDC"}
print(f"Querying MongoDB with: {query}")

# Find all documents matching the query
query_results = collection.find(query)

# Prepare data for DataFrame
data = []

for result in query_results:
    print(f"Processing user: {result.get('fullname', 'Unknown')}")
    
    # Get basic information
    fullname = result.get('fullname', '')
    identity_number = result.get('identity', {}).get('number', '')
    
    # Handle 'account' as a list to get username
    username_value = ''
    account_info = result.get('account', {})
    username_list = account_info.get('username', [])
    if isinstance(username_list, list) and len(username_list) > 0:
        username_value = username_list[0].get('value', '')
    
    # Get experience information
    parent_name = ''
    agency_name = ''
    parent_code = ''
    agency_id = ''
    
    experience_list = result.get('experience', [])
    if isinstance(experience_list, list) and len(experience_list) > 0:
        experience = experience_list[0]  # Get first experience
        agency = experience.get('agency', {})
        
        # Get agency name
        agency_name_list = agency.get('name', [])
        if isinstance(agency_name_list, list) and len(agency_name_list) > 0:
            agency_name = agency_name_list[0].get('name', '')
        
        # Get agency ID
        agency_id_obj = agency.get('id', {})
        if isinstance(agency_id_obj, dict):
            agency_id = agency_id_obj.get('$oid', '')
        
        # Get parent information
        parent = agency.get('parent', {})
        parent_name_list = parent.get('name', [])
        if isinstance(parent_name_list, list) and len(parent_name_list) > 0:
            parent_name = parent_name_list[0].get('name', '')
        
        parent_code = parent.get('code', '')
    
    # Add to data list
    data.append({
        'Họ và tên': fullname,
        'Số CCCD': identity_number,
        'Tên tài khoản': username_value,
        'Đơn vị cha': parent_name,
        'Mã đơn vị cha': parent_code,
        'Đơn vị': agency_name,
        'ID đơn vị': agency_id
    })

# Create DataFrame
df = pd.DataFrame(data)

# Save to Excel file
output_file_path = 'excel/csdldc_from_query.xlsx'
df.to_excel(output_file_path, index=False)

print(f"Created Excel file with {len(data)} records: {output_file_path}")
print("\nColumns created:")
for col in df.columns:
    print(f"- {col}")

# Display first few records
if len(data) > 0:
    print(f"\nFirst few records:")
    print(df.head())
else:
    print("\nNo records found matching the query criteria.")

# Close MongoDB connection
client.close()
