import json
import random
import subprocess
from datetime import datetime, <PERSON><PERSON><PERSON>

def run_curl_command(curl_command):
    """ Ch<PERSON>y lệnh curl và trả về kết quả. """
    process = subprocess.Popen(curl_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        print(f"Error: {stderr}")
        return None
    else:
        return stdout.decode('utf-8')
    

def get_session():
    # Lệnh curl đầu tiên
    curl_command_1 = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/login
    """

    # <PERSON><PERSON><PERSON> lệnh curl đầu tiên và lưu response
    response_1 = run_curl_command(curl_command_1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session
def capitalize_key(key):
    # Viết hoa chữ cái đầu tiên của key
    return key[0].upper() + key[1:] if key else key

def transform_keys(data):
    if isinstance(data, dict):
        # Xử lý từng key trong dictionary
        return {capitalize_key(k): transform_keys(v) if isinstance(v, (dict, list)) else v 
                for k, v in data.items()}
    elif isinstance(data, list):
        # Xử lý từng item trong list
        return [transform_keys(item) if isinstance(item, (dict, list)) else item 
                for item in data]
    return data

def convert_numbers(obj):
    # Nếu là list
    if isinstance(obj, list):
        return [convert_numbers(item) for item in obj]
    
    # Nếu là dict 
    elif isinstance(obj, dict):
        return {key: convert_numbers(value) for key, value in obj.items()}
    
    # Nếu là số thì chuyển thành string
    elif isinstance(obj, (int, float)):
        return str(obj)
    
    # Các kiểu dữ liệu khác giữ nguyên
    else:
        return obj
    
def main(file_path: str):
    with open(file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
        transformed_data = convert_numbers(transform_keys(data))

    # Khởi tạo danh sách để lưu kết quả
    success = []
    failure = []
    # Phân tích JSON
    session = get_session()

    for element in transformed_data:
        masohoso = element['MaHoSo']
        element["DSKetNoiCSDL"] = [{"MaCSDL": "7"}]
        
        try:
            print(f"Đang đồng bộ hồ sơ {masohoso}")
            data = {
                    "madonvi": "000.00.00.H12",
                    "data": [element],
                    "session": session,
                    "service": "DongBoHoSoMC",
                    "isUpdating": "true"
                }

            # Chuyển object thành JSON
            data_json = json.dumps(data)
            # print(element)
            # print("=====================================")
            # print(data_json)

            # Tạo lệnh curl
            curl_command = f"""curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{data_json}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g"""
                    
            # Chạy lệnh curl
            response_3 = run_curl_command(curl_command)
        
            # Phân tích JSON
            data_3 = json.loads(response_3)
            # print(data_3)
        
            # Kiểm tra kết quả
            if data_3['error_code'] == '0':
                success.append(masohoso)
            else:
                failure.append(masohoso)
            print(f"Đồng bộ hồ sơ {masohoso} thành công")
        except:
            failure.append(masohoso)
            continue

    # Lưu kết quả vào file log
    with open('success_log_01.json', 'w') as success_file:
        json.dump(success, success_file)

    with open('failure_log_01.json', 'w') as failure_file:
        json.dump(failure, failure_file)
        

#chạy hàm main
#chạy hàm main
if __name__ == "__main__":
    user_input = input("file ds chạy: ")
    main(user_input)