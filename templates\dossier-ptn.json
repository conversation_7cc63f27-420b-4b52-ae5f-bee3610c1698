{"_id": {"$oid": "652e532ed8c7a64a11cfcf79"}, "code": "000.00.22.H12-231017-0201", "codePattern": {"id": {"$oid": "6447478cbc8884294b47393c"}}, "procedure": {"id": {"$oid": "652d4c9fe8b67510552d4187"}, "code": "1.000798.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON>h<PERSON> tục chuyển mục đích sử dụng đất phải đư<PERSON><PERSON> phép của cơ quan nhà nước có thẩm quyền đối với hộ gia đình, cá nhân"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "652d50d9e8b67510552d419d"}, "processDefinition": {"id": {"$oid": "652d4fb1e1553b068aebeff9"}, "processingTime": 13, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "name": "HCC chi tiết cmu"}, "applicantEForm": {"id": {"$oid": "652d44e9c56bc1001fc6e123"}, "name": "HCC iLis thong tin chung PTN"}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"processingTimeAfterAddition": "", "constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "652d44e9c56bc1001fc6e123"}, "userId": {"$oid": "652deafa0ec8eb131c3c9eb5"}, "data": {"birthday": "1956-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "identityDate": "2022-11-19T00:00:00+07:00", "nycSoGiayToTuyThan": "096056000323", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096056000323", "serialMap": "3", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Phú Mỹ", "value": "5def47c5f47614018c132215"}, "email": "", "address": "<PERSON><PERSON>", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "<PERSON><PERSON>", "value": "5def47c5f47614018c132230"}, "pdg": false, "serialLand": "611", "phoneNumber": "0948177060", "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "district": {"label": "Huyện <PERSON>", "value": "5def47c5f47614018c001972"}, "diaChiThuaDat": "ấp <PERSON><PERSON><PERSON> Nhỏ, xã <PERSON><PERSON><PERSON><PERSON>, huy<PERSON><PERSON>, tỉnh <PERSON><PERSON>", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "accepter": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "appliedDate": {"$date": "2023-10-17T09:26:06.767Z"}, "appointmentDate": {"$date": "2023-11-27T07:13:00.000Z"}, "completedDate": {"$date": "2023-11-14T03:32:20.685Z"}, "returnedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "attachment": [{"id": {"$oid": "6552ea4f489d0d1efbb8ad5f"}, "filename": "000.00.22.H12-231017-0201-KQ.pdf", "size": {"$numberLong": "1900198"}, "group": {"$oid": "5f9bd9692994dc687e68b5a6"}}], "task": [{"id": {"$oid": "654b3515e4af0a2ae165d658"}, "assignee": {}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "<PERSON>ộ phận tiếp nhận và trả kết quả UB Phú Tân"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:13:24.000Z"}, "completedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "dueDate": {"$date": "2023-11-09T01:13:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:13:24.000Z"}, "createdDate": {"$date": "2023-11-08T07:13:24.000Z"}, "updatedDate": {"$date": "2023-11-08T07:15:31.188Z"}, "activitiTask": {"id": "4ea27fd3-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffa"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "654b3592e4af0a2ae165d69c"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3515e4af0a2ae165d658"}}, "agency": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}, "code": "000.00.22.H12", "name": [{"languageId": 228, "name": "UBND huyện Phú Tân"}], "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-11-08T07:15:30.201Z"}, "completedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "dueDate": {"$date": "2023-11-24T07:15:00.000Z"}, "claimDate": {"$date": "2023-11-08T07:15:30.201Z"}, "createdDate": {"$date": "2023-11-08T07:15:30.201Z"}, "updatedDate": {"$date": "2023-11-14T03:32:30.017Z"}, "activitiTask": {"id": "994455c5-7e06-11ee-a35d-fe0c6524430e", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffb"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 12, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}, {"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6552ea4d7fd5c210baca626b"}, "assignee": {"id": {"$oid": "642e3a776fb24b5e08cc6773"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e3a7716e6207094eea7f4"}, "username": [{"value": "************"}]}}, "candidateUser": [], "candidateGroup": [{"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:329:885bf0e5-6c34-11ee-8dd7-a2137e48eaaf"}, "parent": {"id": {"$oid": "654b3592e4af0a2ae165d69c"}}, "agency": {"id": {"$oid": "6411bf32af4d70261e45f22e"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "640939cbaf4d70261e45f1c1"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-11-14T03:32:29.015Z"}, "completedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "dueDate": {"$date": "2023-11-14T09:32:29.015Z"}, "claimDate": {"$date": "2023-11-14T03:32:29.015Z"}, "createdDate": {"$date": "2023-11-14T03:32:29.015Z"}, "updatedDate": {"$date": "2023-12-07T15:00:55.321Z"}, "activitiTask": {"id": "6ff5d7ac-829e-11ee-a84c-d2a7156e2c32", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "652d4fb1e1553b068aebeffc"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 0, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 0, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 0, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 1, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": false}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 4, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0, "notSumPauseDayToProcessingTime": false}}], "activitiProcessInstance": {"_id": "4ea258be-7e06-11ee-a35d-fe0c6524430e", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1956-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096056000323", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096056000323", "phone": "0948177060", "fullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "birthDateStr": "01/01/1956", "email": "", "idTypeId": 3}, "phoneNumber": "0948177060", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identityNumber": "096056000323", "fullname": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-10-17T09:26:06.767Z"}, "updatedDate": {"$date": "2023-12-07T15:01:06.793Z"}, "sync": {"id": 1, "sourceCode": "000.00.22.H12-231017-0201", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: 8ImUwO06akWsODGdIF4NbaoDxgL/SW/bUhm6y2L7HemOjzk/Lop1xA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5febfe2295002b5c79f0fc9f"}, "name": [{"languageId": 228, "name": "Phường/ Xã"}]}, "procedureLevel": {"id": {"$oid": "5f5b2c2b4e1bd312a6f3ae23"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}, "isAutoReceiveQBH": 0, "processType": 0, "cauHoi1": 0, "cauHoi2": 0, "cauHoi3": 0, "cauHoi4": 0, "cauHoi5": 0, "loaiPhieu": 0, "isPostOfficerApplyOnline": 0}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 2, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "notifyTitle": "", "notifyContent": "", "_class": "vn.vnpt.digo.padman.document.Dossier"}