import time
import json
from datetime import datetime, timedelta
import random

# Refined function to correctly handle both single and multiple days scenarios

def calculate_refined_milestones(start_date_str, end_date_str):
    # Define working hours
    start_hour = 7  # 07:00 AM
    end_hour = 16   # 04:00 PM
    date_format = "%Y%m%d%H%M%S"

    # Calculate the total number of days
    #string to time
    start_date  = datetime.strptime(start_date_str, date_format)
    end_date  = datetime.strptime(end_date_str, date_format)

    total_days = (end_date - start_date).days

    # Initialize the list for milestones
    milestones = []

    if total_days > 1:
        # For multiple days, ensure milestones are on different days
        # Milestone 1: Full day on the start date
        milestone_1_start = datetime(start_date.year, start_date.month, start_date.day, start_hour, 0)
        milestone_1_end = datetime(start_date.year, start_date.month, start_date.day, end_hour, 0)
        milestones.append((milestone_1_start, milestone_1_end))

        # Milestone 2: Choose a day in the middle
        middle_day = start_date + timedelta(days=total_days // 2)
        milestone_2_start = datetime(middle_day.year, middle_day.month, middle_day.day, start_hour, 0)
        milestone_2_end = datetime(middle_day.year, middle_day.month, middle_day.day, end_hour, 0)
        milestones.append((milestone_2_start, milestone_2_end))

        # Milestone 3: Full day before the end date
        day_before_end_date = end_date - timedelta(days=1)
        milestone_3_start = datetime(day_before_end_date.year, day_before_end_date.month, day_before_end_date.day, start_hour, 0)
        milestone_3_end = datetime(day_before_end_date.year, day_before_end_date.month, day_before_end_date.day, end_hour, 0)
        milestones.append((milestone_3_start, milestone_3_end))
    else:
        # For a single day, divide the working hours of the day into three parts
        hours_per_part = (end_hour - start_hour) / 3
        for i in range(3):
            part_start = start_date + timedelta(hours=hours_per_part * i)
            part_end = part_start + timedelta(hours=hours_per_part)
            milestones.append((part_start, part_end))
            
    return [(m[0].strftime("%Y-%m-%d %H:%M:%S"), m[1].strftime("%Y-%m-%d %H:%M:%S")) for m in milestones]

# Recalculating the milestones with the refined logic
def main():
    rs = calculate_refined_milestones("20231010070000","20231016160000")
    print(rs)

main()

