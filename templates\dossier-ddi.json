{"_id": {"$oid": "65203c89b8c4ffd7f83bf0e7"}, "code": "000.00.21.H12-230918-0015", "codePattern": {"id": {"$oid": "6499536bbada5a59432409e5"}}, "procedure": {"id": {"$oid": "64969cca1463284dbab50392"}, "code": "1.003003.000.00.00.H12", "translate": [{"languageId": 228, "name": "<PERSON><PERSON>ng ký và cấp <PERSON><PERSON><PERSON><PERSON> chứng nhận quyền sử dụng đất, quyền sở hữu nhà ở và tài sản khác gắn liền với đất lần đầu"}], "sector": {"id": {"$oid": "62b1691d6d754349f444bd50"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> đai"}], "code": "G13-TN02"}}, "procedureProcessDefinition": {"id": {"$oid": "64ae1bdf320c4203ca29b511"}, "processDefinition": {"id": {"$oid": "64ae1b2cb5e5620e1db181fa"}, "processingTime": 30, "processingTimeUnit": "d", "timesheet": {"id": {"$oid": "61259906817da1666a240739"}, "name": "8 gi<PERSON> làm việc"}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}}, "applicantEForm": {"id": {"$oid": "641ac0f0d40110001e41f1b1"}}, "firstTask": {"agencyType": {"id": {"$oid": "62593044fff2d74e9a34221d"}}}, "dynamicVariable": {"constraintTotalProcessTime": true, "isProcessingTimeUnitType": false, "processType": 0, "isRequiredInputEForm": true}, "appliedAgency": [{"id": {"$oid": "6411be6aaf4d70261e45f22b"}, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}}, "applyMethod": {"id": 0, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dossierReceivingKind": {"id": {"$oid": "5f8968888fffa53e4c073ded"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp"}]}, "dossierStatus": {"id": 5, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "Returned result"}], "numberDateRequire": 0}, "dossierTaskStatus": {"id": {"$oid": "60ebf17309cbf91d41f87f8e"}, "name": [{"languageId": 228, "name": "Đ<PERSON> trả kết quả"}, {"languageId": 46, "name": "<PERSON><PERSON><PERSON> returned"}]}, "paymentMethod": {"id": {"$oid": "5f7fca9fb80e603d5300dcf5"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}, "applicant": {"eformId": {"$oid": "641ac0f0d40110001e41f1b1"}, "userId": {"$oid": "65125f19bd4fd20f8be47f19"}, "data": {"birthday": "1980-01-01T00:00:00.000+0000", "note": "", "gender": 1, "nation": {"label": "Việt Nam", "value": "5f39f4a95224cf235e134c5c"}, "district2": {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "5def47c5f47614018c001970"}, "identityDate": "2021-07-12T00:00:00+07:00", "nycSoGiayToTuyThan": "096180006710", "chonDoiTuong": 1, "nycLoaiGiayToTuyThan": 3, "nycHoTen": "<PERSON><PERSON><PERSON><PERSON> Hằng", "province": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "identityNumber": "096180006710", "serialMap": "", "identityAgency": {"name": "<PERSON><PERSON><PERSON> sát quản lý hành chính về trật tự xã hội", "id": "648ab87db6e17a7c177b0d08", "tag": [{"id": "0000591c4e1bd312a6f00002"}], "order": 10000}, "village": {"label": "Xã Tân Tiến", "value": "5def47c5f47614018c132176"}, "email": "", "address": "<PERSON>ân Long A", "province2": {"label": "Tỉnh Cà Mau", "value": "5def47c5f47614018c000096"}, "village2": {"label": "Xã Tân Tiến", "value": "5def47c5f47614018c132176"}, "serialLand": "", "phoneNumber": "0834954678", "declarationForm": {"identifyNo": "096180006710", "phone": "0834954678", "fullName": "<PERSON><PERSON><PERSON><PERSON> Hằng", "birthDateStr": "01/01/1980", "email": "", "idTypeId": 3}, "district": {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "5def47c5f47614018c001970"}, "diaChiThuaDat": "", "fullname": "<PERSON><PERSON><PERSON><PERSON> Hằng"}}, "accepter": {"id": {"$oid": "642e375b6fb24b5e08cc676f"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>"}, "agency": {"id": {"$oid": "6411be6aaf4d70261e45f22b"}, "code": "000.00.21.H12", "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}], "parent": {"id": {"$oid": "637d8b2af217d52a06d6d101"}, "name": [{"languageId": 228, "name": "UBND huyện <PERSON>"}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "ancestors": [{"id": {"$oid": "637d8b2af217d52a06d6d101"}, "name": [{"languageId": 228, "name": "UBND huyện <PERSON>"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}, {"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}]}, "description": "", "receivingPlace": {"rcSend": false, "rcReceive": false}, "dossierProgress": 1, "acceptedDate": {"$date": "2023-09-18T04:48:10.000Z"}, "appliedDate": {"$date": "2023-09-18T04:35:24.009Z"}, "appointmentDate": {"$date": "2023-09-21T06:00:00.000Z"}, "completedDate": {"$date": "2023-09-20T10:06:58.233Z"}, "returnedDate": {"$date": "2023-10-20T03:29:32.163Z"}, "attachment": [{"size": {"$numberLong": "0"}}], "task": [{"id": {"$oid": "6512628cad43c3631e4a13ee"}, "assignee": {}, "candidateUser": [{"id": {"$oid": "642e375b6fb24b5e08cc676f"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e375b16e6207094eea7cb"}, "username": [{"value": "**********"}]}}, {"id": {"$oid": "642e372d6fb24b5e08cc676e"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e372d16e6207094eea7c8"}, "username": [{"value": "*********"}]}}, {"id": {"$oid": "64cafdd4a63171731f5da92f"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "64cafdd47d2d6226795ed366"}, "username": [{"value": "************"}]}}], "candidateGroup": [{"id": {"$oid": "6411be6aaf4d70261e45f22b"}, "code": "000.00.21.H12", "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả huyện Đ<PERSON><PERSON>"}], "parent": {"id": {"$oid": "637d8b2af217d52a06d6d101"}, "name": [{"languageId": 228, "name": "<PERSON> nhánh Văn phòng đăng ký đất đai huyện Đầm Dơi"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "637d8b2af217d52a06d6d101"}, "name": [{"languageId": 228, "name": "<PERSON> nhánh Văn phòng đăng ký đất đai huyện Đầm Dơi"}]}, {"id": {"$oid": "637d7dc1f217d52a06d6d0f7"}, "name": [{"languageId": 228, "name": "Sở Tài nguyên và Môi trường"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}], "processDefinition": {"id": "Process_aqxi1eXIi:181:90ab6716-2062-11ee-9771-f6205563ba6e"}, "agency": {"id": {"$oid": "6411be6aaf4d70261e45f22b"}, "code": "000.00.21.H12", "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả huyện Đ<PERSON><PERSON>"}], "parent": {"id": {"$oid": "637d8b2af217d52a06d6d101"}, "name": [{"languageId": 228, "name": "<PERSON> nhánh Văn phòng đăng ký đất đai huyện Đầm Dơi"}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "ancestors": [{"id": {"$oid": "637d8b2af217d52a06d6d101"}, "name": [{"languageId": 228, "name": "<PERSON> nhánh Văn phòng đăng ký đất đai huyện Đầm Dơi"}]}, {"id": {"$oid": "637d7dc1f217d52a06d6d0f7"}, "name": [{"languageId": 228, "name": "Sở Tài nguyên và Môi trường"}]}, {"id": {"$oid": "000000000191c4e1bd300012"}, "name": [{"languageId": 228, "name": "UBND Tỉnh Cà Mau"}]}], "tag": [{"id": {"$oid": "0000591c4e1bd312a6f00004"}, "name": [{"languageId": 228, "name": "Phòng ban chức năng"}, {"languageId": 46, "name": "Function room, funtion Department"}]}, {"id": {"$oid": "62593044fff2d74e9a34221d"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả"}]}]}, "isCurrent": 0, "isFirst": 1, "isLast": 0, "assignedDate": {"$date": "2023-09-26T04:48:10.000Z"}, "completedDate": {"$date": "2023-09-26T04:49:05.565Z"}, "dueDate": {"$date": "2023-09-26T08:00:00.000Z"}, "createdDate": {"$date": "2023-09-26T04:48:10.000Z"}, "updatedDate": {"$date": "2023-09-26T04:49:05.565Z"}, "activitiTask": {"id": "e574f02d-5c27-11ee-baab-aa1dfe038115", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "64ae1b2cb5e5620e1db181fb"}, "name": {"id": {"$oid": "631e4a0967411b0b0d000001"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "remind": {"id": {"$oid": "5ff8256594a7fa67df706ef0"}, "name": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 1, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": true}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_08zbueq", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "processingTime": 2, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": true, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}, {"id": {"$oid": "651262c02889df74df6bb6df"}, "assignee": {}, "candidateGroup": [{"id": {"$oid": "6494fb84b6e17a7c177b0fc3"}, "code": "", "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận xử lý"}], "parent": {"id": {"$oid": "637d8b2af217d52a06d6d101"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:181:90ab6716-2062-11ee-9771-f6205563ba6e"}, "agency": {"id": {"$oid": "6494fb84b6e17a7c177b0fc3"}, "code": "", "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận xử lý"}], "parent": {"id": {"$oid": "637d8b2af217d52a06d6d101"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 0, "assignedDate": {"$date": "2023-09-26T04:49:04.579Z"}, "completedDate": {"$date": "2023-09-27T10:07:11.376Z"}, "dueDate": {"$date": "2023-11-06T06:00:00.000Z"}, "claimDate": {"$date": "2023-09-26T04:49:04.579Z"}, "createdDate": {"$date": "2023-09-26T04:49:04.579Z"}, "updatedDate": {"$date": "2023-09-27T10:07:11.376Z"}, "activitiTask": {"id": "04e13825-5c28-11ee-baab-aa1dfe038115", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "64ae1b2cb5e5620e1db181fc"}, "name": {"id": {"$oid": "5ff81c2e94a7fa67df706eef"}, "name": "<PERSON><PERSON> lý"}, "remind": {"id": {"$oid": "60f63a7009cbf91d41f88871"}, "name": "<PERSON><PERSON> lý hồ sơ"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 1, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": true}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_002ystd", "name": "<PERSON><PERSON> lý"}, "processingTime": 29, "processingTimeUnit": "d", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}, {"id": {"$oid": "6513fece2889df74df6bbfc3"}, "assignee": {"id": {"$oid": "642e375b6fb24b5e08cc676f"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e375b16e6207094eea7cb"}, "username": [{"value": "*********"}]}}, "candidateUser": [{"id": {"$oid": "642e375b6fb24b5e08cc676f"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e375b16e6207094eea7cb"}, "username": [{"value": "**********"}]}}, {"id": {"$oid": "642e372d6fb24b5e08cc676e"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e372d16e6207094eea7c8"}, "username": [{"value": "*********"}]}}, {"id": {"$oid": "64cafdd4a63171731f5da92f"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "64cafdd47d2d6226795ed366"}, "username": [{"value": "************"}]}}], "candidateGroup": [{"id": {"$oid": "6411be6aaf4d70261e45f22b"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả huyện Đ<PERSON><PERSON>"}], "parent": {"id": {"$oid": "637d8b2af217d52a06d6d101"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:181:90ab6716-2062-11ee-9771-f6205563ba6e"}, "agency": {"id": {"$oid": "6411be6aaf4d70261e45f22b"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả huyện Đ<PERSON><PERSON>"}], "parent": {"id": {"$oid": "637d8b2af217d52a06d6d101"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-09-27T10:07:10.388Z"}, "completedDate": {"$date": "2023-10-02T03:29:32.163Z"}, "dueDate": {"$date": "2023-09-28T02:00:00.000Z"}, "createdDate": {"$date": "2023-09-27T10:07:10.388Z"}, "updatedDate": {"$date": "2023-10-02T03:29:32.163Z"}, "activitiTask": {"id": "9f51157c-5d1d-11ee-baab-aa1dfe038115", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "64ae1b2cb5e5620e1db181fd"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 1, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": true}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 2, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}], "currentTask": [], "previousTask": [{"id": {"$oid": "6513fece2889df74df6bbfc3"}, "assignee": {"id": {"$oid": "642e375b6fb24b5e08cc676f"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e375b16e6207094eea7cb"}, "username": [{"value": "*********"}]}}, "candidateUser": [{"id": {"$oid": "642e375b6fb24b5e08cc676f"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e375b16e6207094eea7cb"}, "username": [{"value": "**********"}]}}, {"id": {"$oid": "642e372d6fb24b5e08cc676e"}, "fullname": "<PERSON><PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "642e372d16e6207094eea7c8"}, "username": [{"value": "*********"}]}}, {"id": {"$oid": "64cafdd4a63171731f5da92f"}, "fullname": "<PERSON><PERSON><PERSON>", "account": {"id": {"$oid": "64cafdd47d2d6226795ed366"}, "username": [{"value": "************"}]}}], "candidateGroup": [{"id": {"$oid": "6411be6aaf4d70261e45f22b"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả huyện Đ<PERSON><PERSON>"}], "parent": {"id": {"$oid": "637d8b2af217d52a06d6d101"}}, "ancestors": []}], "processDefinition": {"id": "Process_aqxi1eXIi:181:90ab6716-2062-11ee-9771-f6205563ba6e"}, "agency": {"id": {"$oid": "6411be6aaf4d70261e45f22b"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON> phận tiếp nhận và trả kết quả huyện Đ<PERSON><PERSON>"}], "parent": {"id": {"$oid": "637d8b2af217d52a06d6d101"}}, "ancestors": []}, "isCurrent": 0, "isFirst": 0, "isLast": 1, "assignedDate": {"$date": "2023-09-27T10:07:10.388Z"}, "completedDate": {"$date": "2023-10-02T03:29:32.163Z"}, "dueDate": {"$date": "2023-09-28T02:00:00.000Z"}, "createdDate": {"$date": "2023-09-27T10:07:10.388Z"}, "updatedDate": {"$date": "2023-10-02T03:29:32.163Z"}, "activitiTask": {"id": "9f51157c-5d1d-11ee-baab-aa1dfe038115", "status": "COMPLETED"}, "bpmProcessDefinitionTask": {"id": {"$oid": "64ae1b2cb5e5620e1db181fd"}, "name": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "remind": {"id": {"$oid": "64116c60e2c0611917fa0df9"}, "name": "Chờ trả kết quả"}, "variable": {"canPaused": 1, "canPrintDossiersCostReport": 1, "canUploadResult": 1, "canIncreaseDue": 1, "canUseDigitalSign": 0, "canUpdateDossierComp": 1, "canUpdateApplicant": 1, "canCancelDosssier": 1, "canChoiceNextStep": 1, "canUpdateDosssierDetail": 1, "canResendDossierToPriviousStep": 1, "canDeleteDossier": 0, "canPrintReceiptTicket": 1, "canEvictDossier": 1, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignNEAC": 0, "mustRatingOfficials": 0, "mustPublishInvoice": 0, "mustChooseAssignee": 0, "mustSendSMSToApplicant": 0, "mustAttachResultsFile": 0, "mustConfirm": 0, "processComments": "", "citizenSMSTemplate": {"id": {"$oid": "64a4d63eb29f442bb462232e"}, "name": "<PERSON><PERSON><PERSON> thông báo cho công dân", "templateId": "", "defaultSend": true}, "attachResultFileToEmail": false}, "definitionTask": {"definitionKey": "UserTask_16ytl9e", "name": "<PERSON><PERSON><PERSON> kết quả"}, "processingTime": 2, "processingTimeUnit": "h", "dynamicVariable": {"canEvictDossier": 1, "canConfirmDossierPaper": 0, "canUseDigitalSignSmartCA": 0, "canUseDigitalSignVGCA": 1, "canUseDigitalSignVNPTCA": 0, "canUseDigitalSignVNPTSim": 0, "canUseDigitalSignNEAC": 0, "canUseDigitalSignQNM": 0, "vnPost": false, "businessLookup": false, "criminalRecord": false, "civilStatusJustice": false, "hcmLGSP": false, "hcmLyLichTuPhap": false, "hcmHoTichTuPhap": false, "vbdlisProvince": false, "vbdlisDistrict": false, "tbnohtttlQni": false, "deadlineForAdditionalRequests": false, "iLis": false, "bhtn": false, "asxh": false, "mustPrintCoupons": 0, "mustSignTypeOrg": 0, "attachResultFileToEmail": false}, "processingId": 0}}], "activitiProcessInstance": {"_id": "e574c918-5c27-11ee-baab-aa1dfe038115", "name": ""}, "eForm": {"id": {"$oid": "641ac959d40110001e41f1b3"}, "data": {"select1": "", "textField": "", "select2": "", "select3": "", "textField1": "", "textField2": "", "select": "", "bhyt": "", "birthday": "1980-01-01T00:00:00.000+0000", "identityDate": "", "nycSoGiayToTuyThan": "096180006710", "nycLoaiGiayToTuyThan": 3, "declarationForm": {"identifyNo": "096180006710", "phone": "0834954678", "fullName": "<PERSON><PERSON><PERSON><PERSON> Hằng", "birthDateStr": "01/01/1980", "email": "", "idTypeId": 3}, "phoneNumber": "0834954678", "nycHoTen": "<PERSON><PERSON><PERSON><PERSON> Hằng", "identityNumber": "096180006710", "fullname": "<PERSON><PERSON><PERSON><PERSON> Hằng", "email": "", "idMoneyReceipt": ""}}, "deploymentId": {"$oid": "60ff693e8dbda12b96b63a4b"}, "status": 0, "createdDate": {"$date": "2023-09-26T04:35:24.009Z"}, "updatedDate": {"$date": "2023-10-02T03:29:37.892Z"}, "sync": {"id": 1, "sourceCode": "000.00.21.H12-230918-0015", "note": "{\"error_code\":\"0\",\"message\":\"Thành công. Signature: ll4knn69ykRGti9vsuwlaJcQYAyc5DCSWuQz1DSqnR6N/h4flFVaSA\\u003d\\u003d\"}"}, "agencyLevel": {"id": {"$oid": "5f39f4155224cf235e134c59"}, "name": [{"languageId": 228, "name": "Quận/ Huyện"}, {"languageId": 46, "name": "District"}]}, "procedureLevel": {"id": {"$oid": "612596f4817da1666a240737"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON>"}]}, "countExtendTime": 0, "countPauses": 0, "dossierMenuTaskRemind": {"id": {"$oid": "60f52f0109cbf91d41f88839"}, "name": [{"languageId": 228, "name": "<PERSON><PERSON><PERSON> kết quả"}]}, "undefindedCompleteTime": 0, "isWithdraw": 0, "countDutypaidCert": 0, "countSendIoffice": 0, "useEditedAppointmentDate": false, "syncStatusCivil": 0, "extendQNI": {"markStatusSync": 0, "isIlis": true, "dossierStatusInIlis": 5}, "extendQBH": {"notify": {"isSendSMS": 0, "isSendZalo": 0, "isSendGmail": 0}}, "fromStoreNum": 0, "toStorageNum": 0, "degitalResult": false, "digitizingStatus": 0, "additionalRequirementDetail": {"numberOfDays": {"$numberLong": "0"}}, "checkHasFileTPHS": true, "checkAsxh": false, "_class": "vn.vnpt.digo.padman.document.Dossier"}