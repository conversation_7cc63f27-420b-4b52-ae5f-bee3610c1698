import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from tkinter import messagebox, PhotoImage, StringVar
import configparser
import requests
import json
import random

# Sample user credentials
USER_CREDENTIALS = {
    'admin': '123'
}

# Configuration file path
CONFIG_FILE = 'config.ini'

# API details
API_URL = "https://dichvucong.gov.vn/jsp/rest.jsp"
HEADERS = {
    'sec-ch-ua': '"Microsoft Edge";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest',
    'sec-ch-ua-mobile': '?0',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'sec-ch-ua-platform': '"Windows"',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Dest': 'empty',
    'host': 'dichvucong.gov.vn',
    'Cookie': 'JSESSIONID=DAC3A0F6330C9D136EFD14D30BC60DD8; TS0115bee1=01f551f5ee5957d6ee581c1ef7b75731f481a444c2f8edd457c3430063bca7d71c4af90fa95d46facd6dee137ad0a36d29df847e8a; route=1717334178.72.363.825965'
}

# Helper function to clear the content frame
def clear_content_frame():
    for widget in content_frame.winfo_children():
        widget.destroy()

# Function to handle menu item selection
def select_menu(menu_button):
    for btn in menu_buttons:
        btn.configure(style="Inactive.TButton")  # Reset style for all buttons
    menu_button.configure(style="Active.TButton")  # Highlight the selected button
    clear_content_frame()

# Load configuration
def load_config():
    config = configparser.ConfigParser()
    config.read(CONFIG_FILE)
    return config

# Save configuration
def save_config(config):
    with open(CONFIG_FILE, 'w') as configfile:
        config.write(configfile)

# Settings page
def open_settings():
    clear_content_frame()
    config = load_config()
    
    def save_settings():
        config['DEFAULT']['ip_server'] = ip_server_var.get()
        config['DEFAULT']['username'] = username_var.get()
        config['DEFAULT']['password'] = password_var.get()
        config['DEFAULT']['unit_code'] = unit_code_var.get()
        save_config(config)
        messagebox.showinfo("Settings Saved", "Configuration has been saved successfully.")
    
    ip_server_var = StringVar(value=config.get('DEFAULT', 'ip_server', fallback=''))
    username_var = StringVar(value=config.get('DEFAULT', 'username', fallback=''))
    password_var = StringVar(value=config.get('DEFAULT', 'password', fallback=''))
    unit_code_var = StringVar(value=config.get('DEFAULT', 'unit_code', fallback=''))
    
    ttk.Label(content_frame, text="IP Server:").grid(row=0, column=0, padx=10, pady=10)
    ip_server_entry = ttk.Entry(content_frame, textvariable=ip_server_var)
    ip_server_entry.grid(row=0, column=1, padx=10, pady=10)
    
    ttk.Label(content_frame, text="Username:").grid(row=1, column=0, padx=10, pady=10)
    username_entry = ttk.Entry(content_frame, textvariable=username_var)
    username_entry.grid(row=1, column=1, padx=10, pady=10)
    
    ttk.Label(content_frame, text="Password:").grid(row=2, column=0, padx=10, pady=10)
    password_entry = ttk.Entry(content_frame, textvariable=password_var, show='*')
    password_entry.grid(row=2, column=1, padx=10, pady=10)
    
    ttk.Label(content_frame, text="Unit Code:").grid(row=3, column=0, padx=10, pady=10)
    unit_code_entry = ttk.Entry(content_frame, textvariable=unit_code_var)
    unit_code_entry.grid(row=3, column=1, padx=10, pady=10)
    
    save_button = ttk.Button(content_frame, text="Save", bootstyle=SUCCESS, command=save_settings)
    save_button.grid(row=4, columnspan=2, pady=20)

# Login Screen
def login_screen():
    for widget in root.winfo_children():
        widget.destroy()
    
    login_frame = ttk.Frame(root, padding=20)
    login_frame.pack(pady=100)

    ttk.Label(login_frame, text="Username:", font=("Helvetica", 12)).grid(row=0, column=0, padx=10, pady=10)
    username_entry = ttk.Entry(login_frame, width=30)
    username_entry.grid(row=0, column=1, padx=10, pady=10)
    
    ttk.Label(login_frame, text="Password:", font=("Helvetica", 12)).grid(row=1, column=0, padx=10, pady=10)
    password_entry = ttk.Entry(login_frame, show="*", width=30)
    password_entry.grid(row=1, column=1, padx=10, pady=10)
    
    def login():
        username = username_entry.get()
        password = password_entry.get()
        if USER_CREDENTIALS.get(username) == password:
            main_screen()
        else:
            messagebox.showerror("Login Failed", "Invalid username or password")
    
    login_button = ttk.Button(login_frame, text="Login", bootstyle=SUCCESS, command=login)
    login_button.grid(row=2, columnspan=2, pady=10)


# Function to fetch data from the API and display results
def fetch_data():
    time_type = time_type_var.get()
    period = period_var.get()
    
    payload = {
        
            "type": "ref",
            "p_nam": "2024",
            "p_6thang": 0,
            "p_tinh_id": "11362",
            "p_linhvuc": "0",
            "p_huyen_id": "",
            "p_thutuc_id": "0",
            "pageIndex": 1,
            "pageSize": 100,
            "p_default": 0,
            "p_xa_id": "0",
            "p_thang": "02",
            "p_quy": 0,
            "service": "report_xuhuongdiem_chiso_service"
        
    }
    
    try:
        response = requests.post(API_URL, headers=HEADERS, data=f"params={json.dumps(payload)}")
        response.raise_for_status()  # Raise an exception for HTTP errors
        print(response.text)

        try:
            #parse JSON response and display results
            
            results = response.json()
            display_results(results)
        except json.JSONDecodeError:
            messagebox.showerror("Error", "Failed to decode JSON response from the API.")
    except requests.RequestException as e:
        messagebox.showerror("Error", f"Failed to fetch data: {e}")

# Function to display results in a formatted way
def display_results(results):
    clear_content_frame()
    colors = ["#FFDDC1", "#FFC1C1", "#C1FFC1", "#C1C1FF", "#FFFFC1", "#C1FFFF"]
    random.shuffle(colors)
    Frame = ttk.Frame
    
    for i, item in enumerate(results[:6]):  # Display only the first 6 results
        frame = Frame(content_frame, padding=10, bootstyle="primary")
        frame.grid(row=i//3 + 1, column=i%3, padx=10, pady=10, sticky=NSEW)

        ttk.Label(frame, text=f"Chỉ số: {item['DESCRIPTION']}", font=("Helvetica", 14), background=colors[i]).pack(side=TOP, anchor=W)
        ttk.Label(frame, text=f"Điểm: {item['SCORE']} / {item['MAX_SCORE']}", font=("Helvetica", 12), background=colors[i]).pack(side=LEFT, padx=10)

# Main Application Screen
def main_screen():
    for widget in root.winfo_children():
        widget.destroy()
    
    global content_frame, menu_buttons, time_type_var, period_var
    menu_buttons = []
    
    # Define custom styles for buttons with larger font size
    style = ttk.Style()
    style.configure('Inactive.TButton', font=("Helvetica", 12), bootstyle="primary")
    style.configure('Active.TButton', font=("Helvetica", 12), bootstyle="info")
    
    # Left Menu
    menu_frame = ttk.Frame(root, width=300, padding=10, bootstyle="primary")
    menu_frame.pack(side=LEFT, fill=Y)
    
    ttk.Label(menu_frame, text="Menu", font=("Helvetica", 16), bootstyle="inverse-primary").pack(pady=20, anchor=W)
    
    def create_menu_button(text, png_icon_path, command):
        icon = PhotoImage(file=png_icon_path).subsample(3, 3)  # Resize icon if necessary
        button = ttk.Button(menu_frame, text=text, image=icon, compound=LEFT, style='Inactive.TButton', command=lambda: [select_menu(button), command()])
        button.image = icon  # Keep a reference to avoid garbage collection
        button.pack(fill=X, pady=10, padx=10, anchor=W)
        
        menu_buttons.append(button)
    
    create_menu_button("Tiến độ giải quyết", "icon1.png", lambda: print("Option 1 selected"))
    create_menu_button("Công khai minh bạch", "icon1.png", lambda: print("Option 2 selected"))
    create_menu_button("Số hoá hồ sơ", "icon1.png", lambda: print("Option 3 selected"))
    create_menu_button("Cài đặt chung", "settings.png", open_settings)
    
    # Spacer frame to push the logout button to the bottom
    ttk.Frame(menu_frame, bootstyle="primary").pack(expand=True, fill=BOTH)
    
    # Logout button at the bottom
    logout_icon = PhotoImage(file="logout.png").subsample(3, 3)  # Resize icon if necessary
    logout_button = ttk.Button(menu_frame, text="Logout", image=logout_icon, compound=LEFT, style='Inactive.TButton', command=login_screen)
    logout_button.image = logout_icon  # Keep a reference to avoid garbage collection
    logout_button.pack(side=BOTTOM, pady=10, padx=10, fill=X, anchor=W)

    # Right Content
    content_frame = ttk.Frame(root, padding=10)
    content_frame.pack(side=RIGHT, expand=True, fill=BOTH)
    
    # Time type selection
      # Time type selection
    time_type_var = StringVar(value="Năm")
    period_var = StringVar()
    year_var = StringVar(value="2024")
    
    search_frame = ttk.Frame(content_frame, padding=10)
    search_frame.grid(row=0, column=0, columnspan=3, pady=10, sticky=W)
    
    ttk.Label(search_frame, text="Loại thời gian:").pack(side=LEFT, padx=10)
    time_type_combo = ttk.Combobox(search_frame, textvariable=time_type_var, values=["Năm", "Tháng", "Quý"])
    time_type_combo.pack(side=LEFT, padx=10)
    
    ttk.Label(search_frame, text="Chọn thời gian:").pack(side=LEFT, padx=10)
    period_combo = ttk.Combobox(search_frame, textvariable=period_var)
    period_combo.pack(side=LEFT, padx=10)
    
    ttk.Label(search_frame, text="Năm:").pack(side=LEFT, padx=10)
    year_combo = ttk.Combobox(search_frame, textvariable=year_var, values=["2023", "2024", "2025"])
    year_combo.pack(side=LEFT, padx=10)
    
    def update_period_options(event):
        time_type = time_type_var.get()
        if time_type == "Năm":
            period_combo['values'] = []
        elif time_type == "Tháng":
            period_combo['values'] = [f"{i:02d}" for i in range(1, 13)]
        elif time_type == "Quý":
            period_combo['values'] = ["1", "2", "3", "4"]
        period_var.set("")
    
    time_type_combo.bind("<<ComboboxSelected>>", update_period_options)
    
    ttk.Button(search_frame, text="Thống kê", bootstyle=SUCCESS, command=fetch_data).pack(side=LEFT, padx=10)

root = ttk.Window(themename="cosmo")
root.title("Python GUI Application")
root.geometry("1200x600")

login_screen()
root.mainloop()
