import requests
import mysql.connector

huyen_id = 11211

# form_data = {
#     'params': '{"service":"dvcqg_get_phuong_xa_by_selected_v2","type":"qry","huyen_id":'+str(huyen_id)+'}'
# }

form_data = {
    'params': '{"type":"ref","p_nam":2023,"p_tinh_id":"11362","p_huyen_id":"+'+str(huyen_id)+'","pageIndex":1,"pageSize":100,"p_default":0,"service":"report_by_year_service_part2"}'
}

# Make the POST request with form data
response = requests.post('https://dichvucong.gov.vn/jsp/rest.jsp', data=form_data)

if response.status_code == 200:
    data = response.json()
else:
    print('Error:', response.status_code)
    exit()  # Exit the script if there was an error

# Connect to the database
cnx = mysql.connector.connect(
    user='root',
    password='root',
    host='localhost',
    database='dvc'
)
cursor = cnx.cursor()

for item in data:
    # Extract the required data from the 'item' dictionary
    column1_value = item['ID']
    column2_value = item['TEN_COQUAN'].split(' - ')[0]
    column3_value = huyen_id;
    # ... extract other column values as needed
        
    # Execute an INSERT query to save the data
    query = "INSERT INTO XA (XA_ID, TEN_XA, HUYEN_ID) VALUES (%s, %s, %s)"
    values = (column1_value, column2_value, column3_value)
    cursor.execute(query, values)

# Commit the changes and close the connection
cnx.commit()
cursor.close()
cnx.close()
