import paramiko
import time
import json

def ssh_command_with_input(command, input_data, timeout=3):
    #info server
    ip = '*************'
    port = 22
    username = 'root'
    password = '<PERSON><PERSON>master@2022'

    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(ip, port, username, password)

        session = ssh_client.get_transport().open_session()
        session.get_pty()
        session.exec_command(command)

        time.sleep(timeout)
        
        # Kiểm tra trạng thái kết nối trước khi nhận dữ liệu
        if session.recv_ready():
            output = session.recv(4096).decode()  # Tăng kích thước buffer
        else:
            output = ''

        if input_data:
            # Gửi input
            session.send(input_data + "\n")
            time.sleep(timeout)

            # Kiểm tra lại trạng thái kết nối
            if session.recv_ready():
                output += session.recv(4096).decode()  # Tăng kích thước buffer

        return output
    except paramiko.SSHException as e:
        print(f"SSH error: {e}")
    finally:
        session.close()
        ssh_client.close()

def check_ssh_connection():
    ip = '*************'
    port = 22
    username = 'root'
    password = 'Kubemaster@2022'

    try:
        # Tạo một client SSH
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # Kết nối đến máy chủ
        ssh_client.connect(ip, port, username, password, timeout=10)

        # Nếu kết nối thành công
        print("SSH connection successful.")
        return True
    except Exception as e:
        # Nếu có lỗi xảy ra
        print(f"SSH connection failed: {e}")
        return False
    finally:
        # Đóng kết nối nếu nó đã được mở
        try:
            ssh_client.close()
        except:
            pass

# check_ssh_connection()
def get_session():
    # Lệnh curl đầu tiên
    curl_command = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/g
    """

    # Chạy lệnh curl đầu tiên và lưu response
    response_1 = ssh_command_with_input(curl_command, "", 1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session

def main(mahoso):
    session = get_session()
    curl_command_2 = f"""
    curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{{"madonvi": "000.00.00.H12","session":"{session}", "service": "TraCuuHoSo", "mahoso": "{mahoso}"}}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/g
    """

    # Kết quả từ API đầu tiên
    response = ssh_command_with_input(curl_command_2, "", 1)
    #json decode unicode
    data = json.loads(response)
    #beautify json
    json_formatted_str = json.dumps(data,ensure_ascii=False, indent=2)
    print(json_formatted_str)

if __name__ == "__main__":
    user_input = input("Nhập mã hồ sơ: ")
    main(user_input)

