from datetime import datetime
import pandas as pd
from pymongo import MongoClient
from bson.objectid import ObjectId

def export_dossier_statistics(mongodb_uri, output_file):
    try:
        # Kết nối đến MongoDB
        client = MongoClient(mongodb_uri)
        
        # Chọn database
        db = client['svcPadman']
        dbLog = client['svcLogman']
        
        # Truy vấn collection
        dossier_collection = db['dossier']
        history_collection = dbLog['history']
        
        # Query để lấy danh sách hồ sơ
        query_dossier = {
            # "agency.ancestors.id": ObjectId('6494f6c7b6e17a7c177b0fa5'),
            "agency.ancestors.id": ObjectId('6494f6c7b6e17a7c177b0fa7'),
             "createdDate": {
                "$gte": pd.to_datetime("2024-01-01T00:00:00.000Z"),
                "$lt": pd.to_datetime("2024-12-26T00:00:00.000Z")
            },
        }
        
        # L<PERSON>y thông tin chi tiết của c<PERSON><PERSON> hồ sơ
        dossier_data = []
        dossiers = dossier_collection.find(query_dossier)
        
        for dossier in dossiers:
            # Chuyển ObjectId thành string
            dossier_id_str = str(dossier['_id'])
            print(dossier_id_str)
            
            # Query để kiểm tra yêu cầu bổ sung
            history_query = {
                "itemId": dossier_id_str,
                "action": {
                    "$elemMatch": {
                        "newValue": {"$regex": "bổ sung", "$options": "i"}
                    }
                }
            }
            
            # Đếm số lần yêu cầu bổ sung
            supplement_count = history_collection.count_documents(history_query)
            
            # Lấy thông tin thủ tục
            procedure_name = ""
            procedure_code = ""
            sector_name = ""
            if 'procedure' in dossier and 'translate' in dossier['procedure']:
                for trans in dossier['procedure']['translate']:
                    if trans.get('languageId') == 228:  # Tiếng Việt
                        procedure_name = trans.get('name', '')
                procedure_code = dossier['procedure'].get('code', '')
                if 'sector' in dossier['procedure'] and 'name' in dossier['procedure']['sector']:
                    for sector_trans in dossier['procedure']['sector']['name']:
                        if sector_trans.get('languageId') == 228:
                            sector_name = sector_trans.get('name', '')
            
            # Lấy trạng thái hồ sơ
            status_name = ""
            if 'dossierStatus' in dossier and 'name' in dossier['dossierStatus']:
                for status_trans in dossier['dossierStatus']['name']:
                    if status_trans.get('languageId') == 228:
                        status_name = status_trans.get('name', '')
            
            # Tạo dictionary chứa thông tin hồ sơ
            dossier_info = {
                'Mã hồ sơ': dossier.get('code', ''),
                'Mã thủ tục': procedure_code,
                'Tên thủ tục': procedure_name,
                'Lĩnh vực': sector_name,
                'Trạng thái': status_name,
                'Số lần yêu cầu bổ sung': supplement_count
            }
            
            dossier_data.append(dossier_info)
        
        # Tạo DataFrame và xuất ra Excel
        df = pd.DataFrame(dossier_data)
        
        # Tính tổng số hồ sơ và số hồ sơ có yêu cầu bổ sung
        total_dossiers = len(dossier_data)
        dossiers_with_supplements = sum(1 for d in dossier_data if d['Số lần yêu cầu bổ sung'] > 0)
        
        # Thêm dòng tổng kết
        summary_df = pd.DataFrame([
            {'Mã hồ sơ': 'Tổng số hồ sơ', 'Mã thủ tục': total_dossiers},
            {'Mã hồ sơ': 'Số hồ sơ có yêu cầu bổ sung', 'Mã thủ tục': dossiers_with_supplements}
        ])
        
        # Ghi ra file Excel
        with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Danh sách hồ sơ', index=False)
            summary_df.to_excel(writer, sheet_name='Thống kê', index=False)
            
            # Tự động điều chỉnh độ rộng cột
            for sheet in writer.sheets.values():
                for idx, col in enumerate(df.columns):
                    series = df[col]
                    max_len = max(
                        series.astype(str).str.len().max(),
                        len(str(col))
                    ) + 2
                    sheet.set_column(idx, idx, max_len)
        
        return {
            'total_dossiers': total_dossiers,
            'dossiers_with_supplements': dossiers_with_supplements,
            'output_file': output_file
        }
        
    except Exception as e:
        print(f"Lỗi: {str(e)}")
        return None
    
    finally:
        client.close()

# Sử dụng hàm
# if __name__ == "__main__":
#     mongodb_uri = "********************************************************************************************************************************"
#     result = count_document_requests(mongodb_uri)
    
#     if result:
#         print(f"Tổng số hồ sơ: {result['total_dossiers']}")
#         print(f"Số hồ sơ có yêu cầu bổ sung: {result['count_with_requests']}")

if __name__ == "__main__":
    mongodb_uri = "********************************************************************************************************************************"
    output_file = f"thong_ke_ho_so_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    result = export_dossier_statistics(mongodb_uri, output_file)
    if result:
        print(f"Đã xuất file: {result['output_file']}")
        print(f"Tổng số hồ sơ: {result['total_dossiers']}")
        print(f"Số hồ sơ có yêu cầu bổ sung: {result['dossiers_with_supplements']}")