import paramiko
import time
import json
import httpx
import asyncio

def ssh_command_with_input(command, input_data, timeout=3):
    #info server
    ip = '*************'
    port = 22
    username = 'root'
    password = '<PERSON>bemaster@2022'

    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(ip, port, username, password)

        session = ssh_client.get_transport().open_session()
        session.get_pty()
        session.exec_command(command)

        time.sleep(timeout)
        
        # Kiểm tra trạng thái kết nối trước khi nhận dữ liệu
        if session.recv_ready():
            output = session.recv(4096).decode()  # Tăng kích thước buffer
        else:
            output = ''

        if input_data:
            # Gửi input
            session.send(input_data + "\n")
            time.sleep(timeout)

            # Kiểm tra lại trạng thái kết nối
            if session.recv_ready():
                output += session.recv(4096).decode()  # Tăng kích thước buffer

        return output
    except paramiko.SSHException as e:
        print(f"SSH error: {e}")
    finally:
        session.close()
        ssh_client.close()

def check_ssh_connection():
    ip = '*************'
    port = 22
    username = 'root'
    password = 'Kubemaster@2022'

    try:
        # Tạo một client SSH
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # Kết nối đến máy chủ
        ssh_client.connect(ip, port, username, password, timeout=10)

        # Nếu kết nối thành công
        print("SSH connection successful.")
        return True
    except Exception as e:
        # Nếu có lỗi xảy ra
        print(f"SSH connection failed: {e}")
        return False
    finally:
        # Đóng kết nối nếu nó đã được mở
        try:
            ssh_client.close()
        except:
            pass

# check_ssh_connection()
def get_session():
    # Lệnh curl đầu tiên
    curl_command = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/login
    """

    # Chạy lệnh curl đầu tiên và lưu response
    response_1 = ssh_command_with_input(curl_command, "", 1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session

async def main(mahoso):
    session = get_session()
    obj = await getThongTinHs(mahoso)
    #change value of key session
    obj['session'] = session

    # Chuyển object thành JSON
    data_json = json.dumps(obj)

    # Tạo lệnh curl
    # curl_command_2 = f"""curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{data_json}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g"""

    # Kết quả từ API đầu tiên
    # response = ssh_command_with_input(curl_command_2, "", 1)
    print(data_json)
    #json decode unicode
    # data = json.loads(response)
    #beautify json
    # json_formatted_str = json.dumps(data,ensure_ascii=False, indent=2)
    # print(json_formatted_str)

async def getToken():
    # from api
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = 'grant_type=password&username=admin&password=Cmu#0801&client_id=web-onegate'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }


    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.post(url, data=payload, headers=headers)
        access_token = response.json()['access_token']
    return access_token

async def getThongTinHs(code):
    url = "https://ketnoi.dichvucongcamau.gov.vn/ad/integrated-event/get-log-by-list-code"
    token = await getToken()
    timeout = httpx.Timeout(60.0)  # Increase timeout to 10 seconds
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer '+token,
        'Cookie': 'JSESSIONID=979D1F437D51C126FF15E5BA8BE0E768'
    }

    payload = json.dumps([code])

    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.post(url, data=payload, headers=headers, timeout=timeout)
        data = {}
        if response.status_code == 200:
            rs =  response.json()[0]
            # print(rs)
            list_log = rs['listLog']
            data = list_log[0].get('data')
        return data


if __name__ == "__main__":
    user_input = input("Nhập mã hồ sơ: ")
    asyncio.run(main(user_input))

