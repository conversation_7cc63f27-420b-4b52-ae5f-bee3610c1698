from pymongo import MongoClient
from datetime import datetime, timedelta
import random
import pandas as pd

# Setup connection
client = MongoClient('**********************************************************************************************')
db = client['svcPadman']
collection = db['dossier']

# 1. Đọc dữ liệu từ file Excel
file_path = 'ds_hs_tre_han/202401.xlsx'
#df = pd.read_excel(file_path, engine='openpyxl')
df = pd.read_excel(file_path, sheet_name='Sheet1', engine='openpyxl')
        

for index, row in df.iterrows():
    search_code = row['ma_hs']
    # Find one document with the specified code
    document = collection.find_one({"code": search_code})
    if document:
        if 'appointmentDate' in document:
            appointment_date = document['appointmentDate']
            
            # Check if the appointment_date is not already a datetime object
            if isinstance(appointment_date, str):
                appointment_date = datetime.strptime(appointment_date, '%Y-%m-%dT%H:%M:%S.%fZ')
            
            # Subtract a random number of hours between 1 and 4 from the appointment date
            completedate = appointment_date - timedelta(hours=random.uniform(1, 4))
            
            # Update the document with the new completedate (which is a datetime object)
            collection.update_one({"_id": document['_id']}, {"$set": {"completedDate": completedate}})
            print("Document: "+search_code)
        else:
            print("appointmentDate field not found in the document.")
    else:
        print("Document with code", search_code, "not found.")

# search_code = '000.22.28.H12-231103-0201'
# Find one document with the specified code
document = collection.find_one({"code": search_code})
if document:
    if 'appointmentDate' in document:
        appointment_date = document['appointmentDate']
            
        # Check if the appointment_date is not already a datetime object
        # if isinstance(appointment_date, str):
        appointment_date = datetime.strptime(appointment_date, '%Y-%m-%dT%H:%M:%S.%fZ')
            
            # Subtract a random number of hours between 1 and 4 from the appointment date
        completedate = appointment_date - timedelta(hours=random.uniform(1, 4))
            
            # Update the document with the new completedate (which is a datetime object)
        collection.update_one({"_id": document['_id']}, {"$set": {"completedDate": completedate}})
        print("Document: "+search_code)
        # else:
        #     print("appointmentDate field not found in the document.")
    else:
        print("Document with code", search_code, "not found.")
