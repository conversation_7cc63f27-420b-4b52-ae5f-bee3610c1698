import mysql.connector
from datetime import date, datetime, timedelta

# Kết nối đến MySQL
conn = mysql.connector.connect(
    user='clusteradmin',
    password='ClusTer_pAss_2022',
    host='************',
    port='32354',
    database='cmu_bhtn'
)
cursor = conn.cursor(dictionary=True)

# L<PERSON>y các dòng cần update
cursor.execute("SELECT * FROM hs_huong_tctn where ngay_tao = '2024-04-05' AND huong_den_ngay IS NOT NULL AND id_thu_tuc_bhtn = 1")

# Lặp qua từng dòng và cập nhật lại den_ngay
for row in cursor.fetchall():
    # Lấy giá trị hiện tại của den_ngay
    current_den_ngay = row['huong_den_ngay']
    huong_tu_ngay = row['huong_tu_ngay']
    
    if not isinstance(current_den_ngay, date):
        # Chuyển đổi thành kiểu datetime để thêm 1 ngày
        current_den_ngay = datetime.strptime(current_den_ngay, '%Y-%m-%d')
    
    new_den_ngay = current_den_ngay + timedelta(days=1)
    
    # Cập nhật lại den_ngay trong cơ sở dữ liệu
    cursor.execute("UPDATE hs_huong_tctn SET huong_den_ngay = %s, ngay_ky= %s WHERE id_hs_huong_tctn = %s", (new_den_ngay.strftime('%Y-%m-%d'), huong_tu_ngay , row['id_hs_huong_tctn']))

# Lưu các thay đổi và đóng kết nối
conn.commit()
conn.close()
