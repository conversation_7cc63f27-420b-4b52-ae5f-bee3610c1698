import requests
import mysql.connector

# Connect to the database
cnx = mysql.connector.connect(
    user='qldt_cdytcm_usr',
    password='Cmu#2020',
    host='***********',
    port='3306',
    database='qldt_cdytcm'
)

cursor = cnx.cursor()

#Get data from huyen table
query = ("SELECT * from NHOM where ID_HOC_KY = 35 ")
cursor.execute(query)
datas = cursor.fetchall()

# Loop through the retrieved data
for dt in datas:
    id_nhom = dt[0]
    id_mon_hoc = dt[1]
    id_can_bo = dt[2]
    ten_nhom = dt[4]
    id_hoc_ky = 36
    hoc_lai = 1

    query = ("SELECT * FROM `qldt_cdytcm`.`NHOM` WHERE `TEN_NHOM` LIKE '%"+str(ten_nhom)+"%' AND `ID_HOC_KY` = '36' AND `ID_MON_HOC` = '"+str(id_mon_hoc)+"'")
    cursor.execute(query)
    data = cursor.fetchone()
    id_nhom_hl = 0
    if data is None:
       query = ("INSERT INTO `qldt_cdytcm`.`NHOM` (`ID_MON_HOC`, `ID_CAN_BO`, `ID_HOC_KY`, `TEN_NHOM`, `HOC_LAI`) VALUES ('"+str(id_mon_hoc)+"', '"+str(id_can_bo)+"', '"+str(id_hoc_ky)+"', '"+str(ten_nhom)+" (Lần 1)"+"', '"+str(hoc_lai)+"')")
       cursor.execute(query)
       id_nhom_hl = cursor.lastrowid
    else:
        id_nhom_hl = data[0]

    # Make an API call using the user data
    url = f"http://cdytcamau.vnptcamau.vn/nhap-diem-mon-hoc/lay-dssv-cam-thi?idNhom={id_nhom}"
    payload = {}
    payload = {}
    headers = {
    'Authorization': 'Basic YWRtaW46Q211IzEyM2FBQCE=',
    'Cookie': 'JSESSIONID=70AE83C654AC710085A7DBB8671B414E'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    result = response.json()
    resData = result.get('resData')
    if resData is not None:
        content = resData.get('content')
        if content is not None and len(content) > 0:
            # Loop through the retrieved data
            for i in content:
                #check if exists
                id_sinh_vien = i.get('sinhVien').get('idSinhVien')
                ma_sinh_vien = i.get('sinhVien').get('maSinhVien')
                print ("ID_SINH_VIEN: "+str(id_sinh_vien)+" - MA_SINH_VIEN: "+str(ma_sinh_vien)+" - ID_NHOM: "+str(id_nhom_hl))
                query = ("SELECT * FROM `qldt_cdytcm`.`SV_HOC_NHOM` WHERE `ID_SINH_VIEN` = '"+str(id_sinh_vien)+"' AND `ID_NHOM` = '"+str(id_nhom_hl)+"'")
                cursor.execute(query)
                data = cursor.fetchone()
                if data is None:
                    query = ("INSERT INTO `qldt_cdytcm`.`SV_HOC_NHOM` (`ID_SINH_VIEN`, `ID_NHOM`) VALUES ('"+str(id_sinh_vien)+"', '"+str(id_nhom_hl)+"')")
                    cursor.execute(query)
                    print(query)
                    print(cursor.rowcount, "record inserted.")
                   
# Close the database connection
cnx.close()
    

    
    
    

    


