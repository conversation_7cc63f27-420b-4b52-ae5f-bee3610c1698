import httpx
import mysql.connector

# Connect to the database
cnx = mysql.connector.connect(
    user='clusteradmin',
    password='ClusTer_pAss_2022',
    host='************',
    port='32354',
    database='cmu_bhtn'
)

cursor = cnx.cursor()
trang_thai = "11"
from_date = "2024-07-24 00:00:00"
to_date = "2024-07-24 23:59:59"

#Get data from huyen table
# query = ("SELECT h.uuid_hs_huong_tctn FROM hs_huong_tctn h join lich_su_chuyen_igate l on h.uuid_hs_huong_tctn = l.uuid_hs_huong_tctn WHERE h.id_tinh_trang_ho_so = "+trang_thai+" and id_thu_tuc_bhtn = 1 and (l.response_message = 'Lỗi gọi API chuy<PERSON><PERSON> <PERSON><PERSON> s<PERSON> sang IGate tr<PERSON> kết quả' OR l.response_message like '%Read timed out%' OR l.response_message like '%502 Bad%') "
#          +"AND l.created_date BETWEEN '"+from_date+"' AND '"+to_date+"' order by l.created_date desc")
query = ("SELECT uuid_hs_huong_tctn from hs_huong_tctn WHERE so_quyet_dinh >= 460 and  so_quyet_dinh <=  485 and YEAR(ngay_tao) = 2025 and id_thu_tuc_bhtn = 1 and id_tinh_trang_ho_so = 11")
cursor.execute(query)
datas = cursor.fetchall()

# Close the database connection
cnx.close()

# Loop through the retrieved data
for dt in datas:
    uuid = dt[0]
    print(uuid)

    # Make an API call using the user data
    api_url = "https://bhtncm.camau.gov.vn/sldtbxh/quan-ly-san-viec-lam/ql-bhtn/hs-huong-tctn/api/chuyen-tra-ket-qua-ho-so"
    bearer_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsibGFib3JfZXhwb3J0Iiwic29jaWFsX3Byb3RlY3Rpb24iLCJlZHVjYXRpb24iLCJjaGlsZHJlbiIsInBheW1lbnQiLCJleGNoYW5nZV9mbG9vcl9tYW5hZ2VtZW50IiwiaW52ZW50b3J5IiwidXNlciIsIm1lcml0b3Jpb3VzX3BlcnNvbiIsImRydWciXSwidXNlcl9uYW1lIjoiY211X2FkbWluIiwic2NvcGUiOlsiUkVBRCIsIldSSVRFIl0sImV4cCI6MTczNzY1NzA1OCwidXNlckRldGFpbHMiOnsidGVuRGFuZ05oYXAiOiJjbXVfYWRtaW4iLCJtYU5ndW9pRHVuZyI6MzI1MiwiaG9UZW4iOiJBZG1pbiB04buJbmggQ01VIiwibWFUaW5oIjoiOTYiLCJtYUh1eWVuIjpudWxsLCJtYVhhIjpudWxsLCJtYVBob25nQmFuIjozMjQsImNhcEJhY0RvblZpIjpudWxsLCJtYURvblZpIjozMDAsInRoZW0iOnRydWUsInN1YSI6dHJ1ZSwieG9hIjp0cnVlLCJjYXBCYWNEViI6MSwidXVpZE5ndW9pRHVuZyI6ImFhMzhmYzI3LTNkZWMtMTFlYy1hMDI2LTAwNTA1Njg4NjNjMiIsImlzU3VwcGVyQWRtaW4iOjAsImlzQ29uZ0RhbiI6MCwiYXZ0IjpudWxsLCJtYVRydW5nVGFtQlRYSCI6bnVsbH0sImF1dGhvcml0aWVzIjpbIlJFQUQiLCJVUERBVEUiLCJERUxFVEUiLCJBRE1JTiIsIkNSRUFURSJdLCJqdGkiOiI3ZjkwMjFhYi1mYWRlLTQwZmItYjg1Zi04YWZiMTk0MDdmYTkiLCJjbGllbnRfaWQiOiJ3ZWJzaXRlIn0.AzMlLwa5VuCdXi1JOBIozaXYK05Clk-yvYQV4lPjfIs"

    headers = {
        "Authorization": "Bearer {}".format(bearer_token),
        "Content-Type": "application/json"
    }

    body = {
        "uuidHsHuongTctn": str(uuid)
    }

    # Create a custom SSL context
    ssl_context = httpx.create_ssl_context()
    # Set stronger DH key length
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    #try catch recall api
    try:
        # Make the API call using httpx
        with httpx.Client(verify=ssl_context) as client:
            response = client.post(api_url, json=body, headers=headers)

        # Process the API response
        if response.status_code == 200:
            # Request successful
            response_data = response.json()
            # Process the response data
            print("Successfully posted data for uuid:"+uuid)
        else:
            # Request failed
            print("Failed to post data for uuid:"+uuid)
    except:
        print("Failed to post data for uuid:"+uuid)
        continue
    # # Make the API call using httpx
    # with httpx.Client(verify=ssl_context) as client:
    #     response = client.post(api_url, json=body, headers=headers)

    # # Process the API response
    # if response.status_code == 200:
    #     # Request successful
    #     response_data = response.json()
    #     # Process the response data
    #     print("Successfully posted data for uuid:"+uuid)
    # else:
    #     # Request failed
    #     print("Failed to post data for uuid:"+uuid)
