# Step 1: Install required libraries
# pip install requests pandas openpyxl

import requests
import pandas as pd

# Step 2: Request data from API
#token
bearer_token ="*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
headers = {
    "Authorization": f"Bearer {bearer_token}"
}

all_data = []
# Set initial page number
page = 0
# agency_level_id = "5f39f4335224cf235e134c5b"
# agency_level_id = "5f39f4155224cf235e134c59"
agency_level_id ="5febfe2295002b5c79f0fc9f"



while True:
    response = requests.get(f"https://ketnoi.dichvucongcamau.gov.vn/bd/procedure?page={page}&keyword=&size=50&spec=page&agency-level-id={agency_level_id}&sort=createdDate,desc&represent=true", headers=headers)
    # print(response)
    data = response.json().get("content")
    # print(data)
    # Break the loop if no data is returned
    if not data:
        break
    all_data.extend(data)
    page += 1

# ## Step 3: Filter only 'id' and 'code' from the data
filtered_data = [{ "code": item["code"],"link":"https://dichvucong.camau.gov.vn/vi/procedure/detail/"+item["id"]} for item in all_data if "id" in item and "code" in item]

# # Step 4: Convert filtered data to a Pandas DataFrame
df = pd.DataFrame(filtered_data)

# # Step 5: Save data to Excel
df.to_excel("DS_cap_Xa.xlsx", index=False, engine='openpyxl')
