import json
from pymongo import MongoClient
import pandas as pd
from bson.objectid import ObjectId
from datetime import datetime, time, timedelta
import random

code = "cnc"

with open('templates/dossier-'+code+'.json', 'r') as file:
    template = json.load(file)

# 1. <PERSON><PERSON><PERSON> dữ liệu từ file Excel
file_path = 'ds_hs_tre_han/'+code+'_huy_thang_11.xlsx'
df = pd.read_excel(file_path, engine='openpyxl')

def convert_dates_to_isodate(date_value):
    # Generate a random time
    random_time = time(random.randint(0, 23), random.randint(0, 59), random.randint(0, 59))
    # Combine date and random time
    combined_datetime = datetime.combine(date_value, random_time)

    # Convert to ISODate string format
    iso_date_str = combined_datetime.isoformat() + 'Z'

    return iso_date_str

        


# 2. Map dữ liệu từ Excel vào object template
data_to_insert = []
bodyApi = []
for index, row in df.iterrows():
    ngay_tiep_nhan = row['ngay_tiep_nhan']
    ngay_hen_tra = row['ngay_hen_tra']
    ngay_hoan_thanh = ngay_hen_tra - timedelta(days=1)
    bodyApi.append(row['ma_hs'])

    new_object = template.copy()
    new_object["_id"]= {"$oid": str(ObjectId())}
    new_object['code'] = row['ma_hs']  # giả sử cột 'Age' trong Excel chứa thông tin tuổi
    new_object['acceptedDate'] =  {"$date": convert_dates_to_isodate(ngay_tiep_nhan)}
    new_object['appliedDate'] = {"$date": convert_dates_to_isodate(ngay_tiep_nhan)}
    new_object['appointmentDate'] = {"$date": convert_dates_to_isodate(ngay_hen_tra)}
    new_object['completedDate'] = {"$date": convert_dates_to_isodate(ngay_hoan_thanh)}
    new_object['returnedDate'] = {"$date": convert_dates_to_isodate(ngay_hen_tra)}

    #replace '' to "" in new_object
    data_to_insert.append(new_object)

# 3. export data to json file
with open('outputs/'+code+'_huy_thang_11.json', 'w') as outfile:
    json.dump(data_to_insert, outfile)

# with open('bodyApi_fail.json', 'w') as outfile:
#     json.dump(bodyApi, outfile)


