from pymongo import MongoClient
import pandas as pd
import dateutil
from bson import ObjectId

# Kết nối tới MongoDB
client = MongoClient("**********************************************************************************************")  # Thay thế bằng chuỗi kết nối của bạn
db = client["svcBasepad"]  # Thay thế bằng tên database của bạn
dateStr = '2024-06-04T03:24:35.357Z'
myDatetime = dateutil.parser.parse(dateStr)

# L<PERSON>y dữ liệu từ collection `procedureForm`
procedure_forms = list(db.procedureForm.find())

# L<PERSON>y dữ liệu từ collection `procedureFormEform`
procedure_form_eforms = list(db.procedureFormEform.find())

# L<PERSON>y danh sách các ID trong procedureFormEform
procedure_form_eform_ids = set(eform['procedureForm']['id'] for eform in procedure_form_eforms)

# <PERSON><PERSON><PERSON> ra các procedureForm không có trong procedureFormEform
missing_procedure_forms = [form for form in procedure_forms if form['_id'] not in procedure_form_eform_ids]

# Tạo và chèn đối tượng mới vào collection procedureFormEform
for procedure_form in missing_procedure_forms:
    if procedure_form["_id"] != ObjectId("665d17fb740d43223886d128"):
        new_procedure_form_eform = {
        "_id": ObjectId(),
        "procedureForm": {
            "id": procedure_form["_id"],
            "form": procedure_form["form"],
            "procedure": procedure_form["procedure"]
        },
        "eForm": {
            "id": ObjectId("665fc8685fff9a001fb36c78"),
            "name": "hccbieumautam"
        },
        "file": {
            "id": ObjectId("666121d38123a348615ab5b1"),
            "filename": "mau-tt-congdan.docx",
            "size": 18685
        },
        "status": 1,
        "placeDisplay": 0,
        "type": {
            "id": ObjectId("6147087505ad830f82bd3133"),
            "name": [
                {
                    "languageId": 228,
                    "name": "Mẫu đơn"
                },
                {
                    "languageId": 46,
                    "name": "Form"
                }
            ]
        },
        "note": "",
        "createdDate": myDatetime,
        "updatedDate": myDatetime,
        "_class": "vn.vnpt.digo.basepad.document.ProcedureFormEform"
        }
    
        # Chèn đối tượng mới vào collection procedureFormEform
        db.procedureFormEform.insert_one(new_procedure_form_eform)

# Đóng kết nối
client.close()

print("Đã chèn các đối tượng mới vào procedureFormEform")