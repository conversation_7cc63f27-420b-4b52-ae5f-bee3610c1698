import json
import random
import subprocess

def run_curl_command(curl_command):
    """ Ch<PERSON>y lệnh curl và trả về kết quả. """
    process = subprocess.Popen(curl_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        print(f"Error: {stderr}")
        return None
    else:
        return stdout.decode('utf-8')
    

def get_session():
    # Lệnh curl đầu tiên
    curl_command_1 = """
    curl -d '{"username":"000.00.00.H12","password":"000.00.00.H12@123456"}' -H 'Content-Type: application/json' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider'  http://*************:8081/XrdAdapter/RestService/forward/mapi/login
    """

    # Ch<PERSON><PERSON> lệnh curl đầu tiên và lưu response
    response_1 = run_curl_command(curl_command_1)

    # lấy token từ response
    try:
        data_1 = json.loads(response_1)
        session = data_1['session']  # Thay đổi key tùy thuộc vào cấu trúc của JSON
    except (json.JSONDecodeError, KeyError):
        print("Không thể lấy dữ liệu quan trọng từ response")
        session = None
    return session

# Lệnh curl thứ hai, sử dụng dữ liệu quan trọng từ response đầu tiên


def tracuuhoso(masohoso,session):
    curl_command_2 = f"""
    curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{{"madonvi": "000.00.00.H12","session":"{session}", "service": "TraCuuHoSo", "mahoso": "{masohoso}"}}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g
    """

    # Kết quả từ API đầu tiên
    response_1 = run_curl_command(curl_command_2)

    return response_1

def tracuuhosoV1(masohoso):
    curl_command_3 = f"""
    curl -X GET "http://192.168.50.79/api/hoso/tracuuhosoV1?mahoso={masohoso}" -H "Accept: application/json"
    """

    # Kết quả từ API thứ hai
    response_2 = run_curl_command(curl_command_3)
    return response_2


def main():
    # masohoso = "000.00.14.H12-230116-0003"
    # Mở file JSON và đọc dữ liệu
    with open('ds-thang-9.json', 'r') as json_file:
        data = json.load(json_file)

    # Khởi tạo danh sách để lưu kết quả
    success = []
    failure = []
    # Phân tích JSON
    session = get_session()

    for element in data:
        masohoso = element['ma_hs']
        
        try:
            print(f"Đang đồng bộ hồ sơ {masohoso}")
            response_1 = tracuuhoso(masohoso,session)
            data_1 = json.loads(response_1)

            # check nếu error_code = -1 thì không tìm thấy mã hồ sơ
            if data_1['error_code'] == -1:
                failure.append(masohoso)
                print(f"Hồ sơ {masohoso} không tồn tại trên cổng DVCQG")
                return
            else:
                response_2 = tracuuhosoV1(masohoso)
                data_2 = json.loads(response_2)

                # Kiểm tra và tạo hoặc chỉnh sửa TaiLieuNop

                chu_ho_so = data_2['data'][0]['ChuHoSo']
                ma_ho_so = data_2['data'][0]['MaHoSo']

                if 'TaiLieuNop' in data_2['data'][0]:
                    tai_lieu_nop = data_2['data'][0]['TaiLieuNop']
                    for tl in tai_lieu_nop:
                        tl['DuocTaiSuDung'] = 1
                        tl['DuocSoHoa'] = 1
                        tl['DuocLayTuKhoDMQG'] = 0
                        tl['TenTepDinhKem'] = tl['TenTepDinhKem']
                        tl['DuongDanTaiTepTin'] = tl['DuongDanTaiTepTin']

                else:
                    # Tạo mockup cho TaiLieuNop
                    tai_lieu_nop = [{
                        "TepDinhKemId": str(random.randint(1000000, 9999999)),
                        "DuocTaiSuDung": "1",
                        "DuocSoHoa": "1",
                        "DuocLayTuKhoDMQG":"0",
                        "TenTepDinhKem": chu_ho_so + ".pdf",
                        "DuongDanTaiTepTin": "http://dvctt.camau.gov.vn/api/dvcqg/downloadfile?fileid=" + str(random.randint(1000000, 9999999)),
                        "DuocLayTuKhoDMQG": str(random.randint(0, 1))
                    }]

                # Kiểm tra và tạo hoặc chỉnh sửa DanhSachGiayToKetQua
                if 'DanhSachGiayToKetQua' in data_1['result'][0]:
                    danh_sach_giay_to_ket_qua = data_1['result'][0]['DanhSachGiayToKetQua']
                else:
                    # Tạo mockup cho DanhSachGiayToKetQua
                    danh_sach_giay_to_ket_qua = [{
                        "TenGiayTo": "KQ_" + ma_ho_so + ".pdf",
                        "DuongDanTepTinKetQua": "http://dvctt.camau.gov.vn/api/dvcqg/downloadfile?fileid=" + str(random.randint(1000000, 9999999)),
                        "MaGiayToKetQua": str(random.randint(10000, 99999))
                    }]


                new_object = {
                    "TenTTHC": data_2['data'][0]['TenTTHC'],
                    "HinhThuc": data_2['data'][0]['HinhThuc'],
                    "HoSoCoThanhPhanSoHoa": data_2['data'][0]['HoSoCoThanhPhanSoHoa'],
                    "LoaiDoiTuong": data_2['data'][0]['LoaiDoiTuong'],
                    "TrangThaiHoSo": data_1['result'][0]['TrangThaiHoSo'],
                    "KenhThucHien": data_2['data'][0]['KenhThucHien'],
                    "MaHoSo": data_2['data'][0]['MaHoSo'],
                    "MaTTHC": data_2['data'][0]['MaTTHC'],
                    "DSKetNoiCSDL": data_2['data'][0]['DSKetNoiCSDL'],
                    "NoiNopHoSo": data_2['data'][0]['NoiNopHoSo'],
                    "TenLinhVuc": data_1['result'][0]['TenLinhVuc'],
                    "DonViXuLy": data_2['data'][0]['DonViXuLy'],
                    "NgayHenTra": data_1['result'][0]['NgayHenTra'],
                    "MaDoiTuong": data_2['data'][0]['MaDoiTuong'],
                    "NgayTiepNhan": data_1['result'][0]['NgayTiepNhan'],
                    "DuocThanhToanTrucTuyen": data_2['data'][0]['DuocThanhToanTrucTuyen'],
                    "NgayNopHoSo": data_2['data'][0]['NgayNopHoSo'],
                    "TaiKhoanDuocXacThucVoiVNeID": data_2['data'][0]['TaiKhoanDuocXacThucVoiVNeID'],
                    "MaLinhVuc": data_1['result'][0]['MaLinhVuc'],
                    "DinhDanhCHS": data_2['data'][0]['DinhDanhCHS'],
                    "ChuHoSo": data_2['data'][0]['ChuHoSo'],
                    "TaiLieuNop": tai_lieu_nop,
                    "DanhSachGiayToKetQua": danh_sach_giay_to_ket_qua
                }

                data = {
                    "madonvi": "000.00.00.H12",
                    "data": [new_object],
                    "session": session,
                    "service": "DongBoHoSoMC",
                    "isUpdating": "true"
                }

                # Chuyển object thành JSON
                data_json = json.dumps(data)

                # Tạo lệnh curl
                curl_command = f"""curl -X POST -H "Content-Type: application/json" -H "Charset: utf-8" -d '{data_json}' -H 'dstcode: VN:GOV:000.00.00.G22:vpcpdvcprovider' http://*************:8081/XrdAdapter/RestService/forward/mapi/g"""
                    
                # Chạy lệnh curl
                response_3 = run_curl_command(curl_command)
        
                # Phân tích JSON
                data_3 = json.loads(response_3)
        
                # Kiểm tra kết quả
                if data_3['error_code'] == '0':
                    success.append(ma_ho_so)
                else:
                    failure.append(ma_ho_so)
                print(f"Đồng bộ hồ sơ {ma_ho_so} thành công")
        except:
            failure.append(ma_ho_so)
            continue

    # Lưu kết quả vào file log
    with open('success_log_t7.json', 'w') as success_file:
        json.dump(success, success_file)

    with open('failure_log_t7.json', 'w') as failure_file:
        json.dump(failure, failure_file)
        

#chạy hàm main
main()