from pymongo import MongoClient
from datetime import timedelta

# MongoDB connection string
client = MongoClient("**********************************************************************************************")

database_name = 'svc<PERSON>man'  # Replace with your database name
collection_name = 'dossier'  # Replace with your collection name
database = client[database_name]
collection = database[collection_name]
# Define the regex pattern for the code field
pattern = ".*2000269533-002-09-20240504.*"

# Query to find documents that match the pattern
search_query = {
    "$and": [
        {
            "code": {
                "$regex": pattern
            }
        },
        {
            "procedure.code": "CMU-02"
        }
    ]
}
documents = collection.find(search_query).skip(0).limit(1000)

# Update documents
for doc in documents:
    acceptedDate = doc['acceptedDate']
    new_appointment_date = acceptedDate + timedelta(days=20)
    new_due_date = acceptedDate + timedelta(days=20)

    result = collection.update_one(
        {
            "_id": doc['_id']
        },
        {
            "$set": {
                "appointmentDate": new_appointment_date,
                "dueDate": new_due_date
            }
        }
    )
    print(f"Updated document ID {doc['_id']} with new appointmentDate and dueDate.")

