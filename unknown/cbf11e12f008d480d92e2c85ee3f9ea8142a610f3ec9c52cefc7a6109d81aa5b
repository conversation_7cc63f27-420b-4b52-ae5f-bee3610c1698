import json
import httpx
import asyncio
from pymongo import MongoClient

# MongoDB setup
client = MongoClient('*********************************************************************************************************************************')
db = client['svcPadman']  # Replace 'your_database' with your MongoDB database name
collection = db['dossier']  # Replace 'your_collection' with your MongoDB collection name

# API setup
url = "http://localhost:8080/lgsp-hgi-lltp/sync-send-dossier"
# Function to call API with each ID and Code asynchronously
async def getToken():
    # from api
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = 'grant_type=password&username=admin&password=iGate#Cmu@2024&client_id=web-onegate'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }


    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")

    async with httpx.AsyncClient(verify=ssl_context, timeout=60.0) as client:
        response = await client.post(url, data=payload, headers=headers)
        access_token = response.json()['access_token']
    return access_token

async def call_api_with_id_code_async(id_code_list):
    ssl_context = httpx.create_ssl_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    async with httpx.AsyncClient(verify=ssl_context,timeout=60.0) as client:
        tasks = []
        for item in id_code_list:
            payload = {
                "id": item['id'],
                "code": item['code']
            }
            token = await getToken()
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            tasks.append(client.post(url, headers=headers, json=payload))
        
        # Await all tasks
        responses = await asyncio.gather(*tasks)
        for response in responses:
            print(f"Called API with status: {response.status_code}, response: {response.text}")

# Function to check status and make API calls
async def check_and_call_api_async():
    # Query MongoDB for relevant records
    results = collection.find({"code": False}, {"_id": 1, "code": 1})
            
    # Prepare list of IDs and Codes
    id_code_list = [{"id": str(result["_id"]), "code": result["code"]} for result in results]
    print(f"Found {len(id_code_list)} records to process")
            
    # Call the API for each item in the list asynchronously
    await call_api_with_id_code_async(id_code_list)

# Run the function every 3 minutes
async def main():
    #input by command list code need to sync
    list_code = input("Enter list code need to sync: ")
    list_code = list_code.split(',')
    for code in list_code:
        # Query MongoDB for relevant records
        results = collection.find({"code": code}, {"_id": 1, "code": 1})
                
        # Prepare list of IDs and Codes
        id_code_list = [{"id": str(result["_id"]), "code": result["code"]} for result in results]
        print(f"Found {len(id_code_list)} records to process")
                
        # Call the API for each item in the list asynchronously
        await call_api_with_id_code_async(id_code_list)



# Execute the main function
asyncio.run(main())
