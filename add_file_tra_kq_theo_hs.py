import httpx
import asyncio
import json
import pymongo
import ssl
import aiohttp

# Connect to MongoDB
mongo_uri = '*********************************************************************************************************************************'
client = pymongo.MongoClient(mongo_uri)

async def getToken():
    url = 'https://sso.dichvucongcamau.gov.vn/auth/realms/digo/protocol/openid-connect/token'
    payload = {
        'grant_type': 'password',
        'username': 'admin',
        'password': 'iGate#Cmu@2024',
        'client_id': 'web-onegate'
    }
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    
    async with httpx.AsyncClient(verify=ssl_context) as client:
        response = await client.post(url, headers=headers, data=payload)
        response.raise_for_status()
        access_token = response.json().get('access_token')
        return access_token

async def syncQG(code):
    url = "https://ketnoi.dichvucongcamau.gov.vn/pa/re-sync-dossier/--sync-by-code?isLGSPHCM=false"
    token = await getToken()
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}',
        'Cookie': 'JSESSIONID=979D1F437D51C126FF15E5BA8BE0E768'
    }
    
    payload = json.dumps([code])
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    
    async with httpx.AsyncClient(headers=headers, verify=ssl_context) as client:
        response = await client.post(url, headers=headers, data=payload)
        if response.status_code == 200:
            if response.json().get('affectedRows') == 1:
                return 200
            else:
                return 500
        else:
            return 501

async def upload_file(upload_url, file_path, token, file_name):
    headers = {'Authorization': f'Bearer {token}'}
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    
    async with httpx.AsyncClient(headers=headers, verify=ssl_context) as client:
        with open(file_path, 'rb') as file:
            files = {'files': (file_name, file), 'account': (None, '642e372d16e6207094eea7c8')}
            response = await client.post(upload_url, files=files)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Failed to upload the file: {response.status_code}")
                return None

async def update_attachment(put_url, attachment_data, token):
    payload = {"attachment": [attachment_data]}
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    ssl_context = ssl.create_default_context()
    ssl_context.set_ciphers("HIGH:!DH:!aNULL")
    
    async with httpx.AsyncClient(headers=headers, verify=ssl_context) as client:
        response = await client.put(put_url, json=payload)
        if response.status_code == 200:
            print("Successfully updated the attachment")
        else:
            print(f"Failed to update the attachment: {response.status_code}")

def get_documents(dossier_code):
    database_name = 'svcPadman'  # Replace with your database name
    collection_name = 'dossier'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
  
    documents = collection.find({"$and": [{"code": dossier_code}]})
    return documents

def get_file_id(dossier_id):
    database_name = 'svcPadman'  # Replace with your database name
    collection_name = 'dossierFormFile'  # Replace with your collection name
    database = client[database_name]
    collection = database[collection_name]
    documents = collection.find({"dossier.id": dossier_id}).limit(1)
    file_id = str(documents[0]['file'][0]['id'])
    return file_id

async def main():
    upload_url = 'https://ketnoi.dichvucongcamau.gov.vn/fi/file/--multiple?uuid=1'
    bearer_token = await getToken()

    dossier_code = input("Please enter the dossier code: ")

    documents = get_documents(dossier_code)
    success = 0
    fail = 0
    list_code_fail = []
    for document in documents:
        try:
            code = document['code']
            print("Code: " + code)
            file_name = code + '-KQ.pdf'
            dossier_id = document['_id']
            file_id = get_file_id(dossier_id)
            print("File id: " + file_id)
            put_url = 'https://ketnoi.dichvucongcamau.gov.vn/pa/dossier/' + str(dossier_id) + '/--online'

            downloaded_file_path = "download/" + file_name
            if downloaded_file_path:
                print("File downloaded successfully. File path:", downloaded_file_path)
                response_data = await upload_file(upload_url, downloaded_file_path, bearer_token, file_name)
                if response_data:
                    print("Upload Successful, Response:", response_data)

                    attachment_data = {
                        "id": response_data[0]["id"],
                        "filename": response_data[0]["filename"],
                        "size": response_data[0]["size"],
                        "group": "5f9bd9692994dc687e68b5a6"  # Update this if needed
                    }

                    await update_attachment(put_url, attachment_data, bearer_token)
                    await syncQG(code)

                print("Done: " + code)
                success += 1
        except Exception as e:
            print(e)
            list_code_fail.append(code)
            fail += 1

    print("Success: " + str(success))
    print("Fail: " + str(fail))

async def sync():
    dossier_code = input("Please enter the dossier code: ")
    documents = get_documents(dossier_code)
    success = 0
    fail = 0
    list_code_fail = []
    for document in documents:
        try:
            code = document['code']
            print("Code: " + code)
            await syncQG(code)
            print("Done: " + code)
            success += 1
        except Exception as e:
            print(e)
            list_code_fail.append(code)
            fail += 1

asyncio.run(main())
